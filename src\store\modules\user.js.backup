import { getVolunteerInfo} from '@/api/user';
import router from '@/router'; // 导入路由对象
const state = {
    userInfo: {
        userName: '测试用户',
        id: '10',
        // userName: '',
        // id: '',
    },
    volunteerId:'10'
};

const mutations = {
    SET_USER_INFO: (state, userInfo) => {
        state.userInfo = userInfo;
    },
    // INIT_USER_INFO: (state) => {
    //     state.userInfo = {
    //         userName: '',
    //         id: '',
    //     };
    // },
    SET_USER_INFO_EMAIL: (state, e) => {
        state.userInfo.userEmail = e;
    },
    SET_volunteerId: (state,e ) => {
        state.volunteerId = e;
    },
};
const actions = {
  async setVolunteerId({ commit }, volunteerId) {
        commit('SET_volunteerId', volunteerId);
    },
    async getInfo({ commit,state,dispatch }) {
        var reqData = {
            volunteerId:state.volunteerId
        }
        const infoResponse = await getVolunteerInfo(reqData);
        const userInfo = infoResponse.data.data;
        commit('SET_USER_INFO', userInfo);
        await dispatch('Permissions/isPerMissions', userInfo, { root: true });
        return userInfo; // 如果需要在外部访问返回值，可以返回 userInfo 或者根据需求返回其他内容
    },
    async logout({ commit }) {
         try {
         commit('SET_USER_INFO',{} ); 
         commit('config/SET_tabActive', 0, { root: true }); // 使用 root 选项
            // 清除token
            localStorage.removeItem('TOKEN');
            // 清除session
            sessionStorage.clear();
        } catch (error) {
            console.error('Error while clearing local storage:', error);
        }
              await router.push('/');
    }
};
export default {
    namespaced: true,
    state,
    mutations,
    actions
};
