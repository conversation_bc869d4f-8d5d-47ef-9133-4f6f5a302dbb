<template>
  <div class="page-container">
    <NavHeader ref="navHeader" title="运输业务" :back="true"></NavHeader>

    <!-- 业务分组 -->
    <div class="business-groups">
      <!-- 核心审核业务 -->
      <div class="business-group">
        <LabelHeader left="核心审核业务"></LabelHeader>
        <div class="group-card">
          <div class="menu-grid">
            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-approval', '运输核准审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a1.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.transportApprovalCount > 0">
                  {{ statisticsZTC.transportApprovalCount > 99 ? '99+' : statisticsZTC.transportApprovalCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>运输核准审核</h4>
                <p>审核运输核准申请材料</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-final-approval', '运输核准终审')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a2.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.agentFinalApprovalCount > 0">
                  {{ statisticsZTC.agentFinalApprovalCount > 99 ? '99+' : statisticsZTC.agentFinalApprovalCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>运输核准终审</h4>
                <p>运输申请终审流程</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/list', '准运证审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/bh.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.haulwayCount > 0">
                  {{ statisticsZTC.haulwayCount > 99 ? '99+' : statisticsZTC.haulwayCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>准运证审核</h4>
                <p>审核准运证申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-register/list', '运输单位注册审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/dh.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.yunCount > 0">
                  {{ statisticsZTC.yunCount > 99 ? '99+' : statisticsZTC.yunCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>运输单位注册审核</h4>
                <p>审核运输单位注册信息</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

          </div>
        </div>
      </div>

      <!-- 变更业务 -->
      <div class="business-group">
        <LabelHeader left="变更业务"></LabelHeader>
        <div class="group-card">
          <div class="menu-grid">
            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-extension/list', '有效期限延长审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a1.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.yAgentCount > 0">
                  {{ statisticsZTC.yAgentCount > 99 ? '99+' : statisticsZTC.yAgentCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>有效期限延长审核</h4>
                <p>审核有效期限延长申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>


            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-company/list', '企业基本资料变更审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a2.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.agentChangeCount > 0">
                  {{ statisticsZTC.agentChangeCount > 99 ? '99+' : statisticsZTC.agentChangeCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>企业基本资料变更审核</h4>
                <p>审核企业基本资料变更申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/HaulwayModel/transport-vehicle/list', '运输车辆变更审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/bh.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.agentChangeCarCount > 0">
                  {{ statisticsZTC.agentChangeCarCount > 99 ? '99+' : statisticsZTC.agentChangeCarCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>运输车辆变更审核</h4>
                <p>审核运输车辆变更申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { statisticsZTC } from "@/api/config";
import { mapState } from "vuex";
import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  components: {
    LabelHeader,
  },
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    ...mapState("config", ["statisticsZTC"]),
  },
  mounted() {
    // 页面加载完成后的初始化操作
    this.initPage();
  },
  methods: {
    // 初始化页面
    initPage() {
      // 获取统计数据
      this.getStatistics();
      console.log('运输业务页面初始化完成');
    },

    // 获取统计数据
    getStatistics() {
      const reqData = {};
      statisticsZTC(reqData).then((res) => {
        if (res.data.success) {
          this.$store.commit("config/SET_statisticsZTC", res.data.result);
          console.log('统计数据获取成功:', res.data.result);
        } else {
          this.$store.commit("config/remove_statisticsZTC");
          console.log('统计数据获取失败');
        }
      }).catch((error) => {
        console.error('获取统计数据出错:', error);
        this.$store.commit("config/remove_statisticsZTC");
      });
    },

    // 统一的菜单点击处理
    handleMenuClick(route, menuName) {
      // 添加点击反馈
      this.addClickFeedback();

      if (route) {
        // 有路由的菜单项，直接跳转
        this.$go(route);
      } else {
        // 开发中的菜单项，显示提示
        this.showDevelopingToast(menuName);
      }
    },

    // 显示开发中提示
    showDevelopingToast(menuName) {
      this.$toast({
        message: `${menuName}功能正在开发中，敬请期待...`,
        duration: 2500,
        position: 'middle',
      });
    },

    // 添加点击反馈效果
    addClickFeedback() {
      // 如果支持触觉反馈，添加轻微震动
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    },
  },
};
</script>

<style scoped lang="less">
// 页面容器
.page-container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  box-sizing: border-box;
}

// 业务分组容器
.business-groups {
  padding: 16px 16px 20px;
}

// 单个业务分组
.business-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 分组卡片
.group-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  overflow: hidden;
}

// 菜单网格
.menu-grid {
  padding: 8px;
}

// 菜单项
.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 8px 0;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    background: #f8faff;
    border-color: #d9e7ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  // 开发中状态
  &.developing {
    background: #fafafa;
    border-color: #e8e8e8;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
    }

    .menu-content h4 {
      color: #8c8c8c;
    }

    .menu-content p {
      color: #bfbfbf;
    }

    .menu-arrow {
      color: #d9d9d9;
    }
  }
}

// 菜单图标
.menu-icon {
  position: relative;
  width: 48px;
  height: 48px;
  background: #f8faff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;

  img {
    width: 28px;
    height: 28px;
  }

  // 通知徽章
  .notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    min-width: 18px;
    height: 18px;
    background: #ff4d4f;
    color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-sizing: border-box;
    border: 2px solid #ffffff;
    line-height: 1;
  }

  // 开发中标签
  .developing-tag {
    position: absolute;
    top: -4px;
    right: -8px;
    background: #faad14;
    color: #ffffff;
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 8px;
    line-height: 1;
    transform: scale(0.9);
  }
}

// 菜单内容
.menu-content {
  flex: 1;

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 4px 0;
    line-height: 22px;
  }

  p {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0;
    line-height: 18px;
  }
}

// 菜单箭头
.menu-arrow {
  color: #bfbfbf;
  font-size: 16px;
  margin-left: 8px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}
</style>
