
const state = {
    payMoney:'',
    bankInfo:'',
    type: 'ZHIFU',
    url: '',
    dialogShow:false
};
const mutations = {
    initData:(state)=>{
        state.payMoney = '',
        state.bankInfo= ''
    },
    SET_payMoney: (state, e) => {
        state.payMoney = e;
    },
    SET_bankInfo: (state, e) => {
        state.bankInfo = e;
    },
    SET_type: (state, e) => {
        state.type = e;
    },
    SET_dialogShow: (state, e) => {
        state.dialogShow = e;
    },
    SET_url: (state, e) => {
        state.url = e;
    },
};
const actions = {
    
};
export default {
    namespaced: true,
    state,
    mutations,
    actions
};
