<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData?.engineeringName }}</div>
        </div>
      </div>
      <div class="map-box">
        <van-button
          class="goMap-btn"
          type="primary"
          size="small"
          @click="goMap(indentData)"
          round
          icon="location-o"
        >
          查看地图
        </van-button>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="location" size="16px" color="#52c41a" />
        <div class="flex-row time">
          <i>产生地地址: </i><i class="timedata">{{ indentData?.coordinateInfo }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="shop-o" size="16px" color="#fa8c16" />
        <div class="flex-row time">
          <i>处置地名称: </i><i class="timedata">{{ indentData?.agent?.agentName }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="bag-o" size="16px" color="#722ed1" />
        <div class="flex-row time">
          <i>建筑垃圾类型: </i><i class="timedata">
            {{
              indentData?.moreGarbageTypeList
                ?.map((item) => item.dicValue)
                .join("，")
            }}
          </i>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row">
      <div class="new-text-sty">申请日期：{{ indentData.createDate }}</div>
      <div class="action-btn-new" @click="handleClick(indentData)" v-if="shouldShowButton(indentData)">
        <span class="btn-text">{{ getButtonText(indentData.typeState, indentData.applyState) }}</span>
        <van-icon name="arrow" size="14px" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
    currentTabState: {
      type: [String, Number],
      default: 1,
    },
  },
  computed: {
    statusClass() {
      // 根据开发文档，产生地业务使用 applyState 字段
      const state = this.indentData.applyState || this.indentData.typeState;

      switch (state) {
        case 2: // 待初审
          return "pending";
        case 9: // 待终审
          return "pending";
        case 8: // 初审通过
        case 5: // 终审通过
          return "success";
        case 3: // 初审不通过
        case 6: // 终审不通过
          return "rejected";
        default:
          return "pending";
      }
    },
    statusText() {
      // 根据开发文档，产生地业务的状态文本
      const state = this.indentData.applyState || this.indentData.typeState;

      switch (state) {
        case 2:
          return "待初审";
        case 9:
          return "待终审";
        case 8:
          return "初审通过";
        case 3:
          return "初审不通过";
        case 5:
          return "终审通过";
        case 6:
          return "终审不通过";
        default:
          return "待审核";
      }
    },
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      const date = new Date(isoString);
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    getButtonText(typeState, applyState) {
      // 根据开发文档，产生地业务使用 applyState 字段
      const state = applyState || typeState;

      if (state === 2) {
        return '审核'; // 待初审
      }

      if (state === 9) {
        return '终审'; // 待终审
      }

      if (state === 8 || state === 3 || state === 5 || state === 6) {
        return '查看详情'; // 已审核完成
      }

      return '查看详情'; // 默认
    },

    shouldShowButton(data) {
      // 根据开发文档，产生地业务的状态判断
      const state = data.applyState || data.typeState;

      // 显示按钮的状态：2-待初审, 9-待终审, 8-初审通过, 3-初审不通过, 5-终审通过, 6-终审不通过
      return state === 2 || state === 9 || state === 8 || state === 3 || state === 5 || state === 6;
    },

    goMap(e) {
      this.$router.push({ name: "CheckMap", params: { data: e } });
    },

    // 判断是否来自已完成tab
    isFromCompletedTab() {
      const currentRoute = this.$route.path;
      if (currentRoute.includes('/place-origin-approval') || currentRoute.includes('/place-origin-final-approval')) {
        // 检查当前是否在已完成tab
        return this.$parent && this.$parent.active === 1; // 假设已完成是第二个tab
      }
      return false;
    },

    handleClick(e) {
      console.log("点击产生地审核卡片：", e);

      // 根据开发文档，产生地业务的跳转逻辑
      const state = e.applyState || e.typeState;
      const isFromCompleted = this.isFromCompletedTab();

      // 根据状态跳转到不同页面
      if (state === 2 || state === 8 || state === 3) {
        // 初审相关状态跳转到初审页面
        this.$router.push({
          name: "PlaceOriginApproval",
          params: { data: e },
          query: isFromCompleted ? { from: 'completed' } : {}
        });
      } else if (state === 9 || state === 5 || state === 6) {
        // 终审相关状态跳转到终审页面
        this.$router.push({
          name: "PlaceOriginFinalApproval",
          params: { data: e },
          query: isFromCompleted ? { from: 'completed' } : {}
        });
      } else {
        // 默认跳转到初审页面
        this.$router.push({
          name: "PlaceOriginApproval",
          params: { data: e },
          query: isFromCompleted ? { from: 'completed' } : {}
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.top {
  padding: 16px;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.pre {
  flex: 1;
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.map-box {
  margin-left: 12px;
  
  .goMap-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    min-height: 28px;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 12px 16px;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    padding-bottom: 16px;
  }

  .timebox {
    align-items: center;
    flex: 1;

    .time {
      margin-left: 8px;
      font-size: 14px;
      color: #8c8c8c;
      line-height: 20px;

      i:first-child {
        color: #8c8c8c;
      }

      .timedata {
        color: #262626 !important;
        font-weight: 500;
        margin-left: 4px;
      }
    }
  }

  .new-text-sty {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;
  }

  .action-btn-new {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;

    &:hover {
      .btn-text {
        color: #40a9ff;
      }
    }

    .btn-text {
      font-size: 14px;
      color: #1989fa;
      font-weight: 500;
      margin-right: 6px;
      transition: color 0.3s ease;
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
