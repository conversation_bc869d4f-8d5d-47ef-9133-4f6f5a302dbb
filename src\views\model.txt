<template>
  <div class="container">
    <NavHeader ref="navHeader" title="产生地审核" :back="true"></NavHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              v-model="form.etXm"
              name="etXm"
              label="姓名："
              placeholder="请输入"
              :readonly="isReadonly"
            />
            <van-field
              v-model="form.etNl"
              name="etXm"
              label="年龄："
              placeholder="请输入"
              :readonly="isReadonly"
            />
            <van-field
              v-model="form.etXb"
              name="etXb"
              label="性别："
              :readonly="isReadonly"
            >
              <template #input>
                <van-radio-group v-model="form.etXb" direction="horizontal">
                  <van-radio
                    v-for="(item, index) in XB_LIST"
                    :key="index"
                    :name="item.code"
                  >
                    <div class="lable">{{ item.label }}</div>
                  </van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <!-- <van-field
              :value="form.csRq | formatDate"
              label="出生日期："
              placeholder="请选择"
              name="csRq"
              @click="showCs = true"
            /> -->
            <van-popup v-model="showCs" round position="bottom">
              <van-datetime-picker
                v-model="currentDate"
                type="date"
                title="选择年月日"
                :min-date="minDate"
                :max-date="maxDate"
                @confirm="onConfirm5"
                @cancel="showCs = false"
              />
            </van-popup>
            <van-field
              v-model="form.sfZh"
              name="sfZh"
              label="身份证号："
              placeholder="请输入"
            />
            <van-popup
              v-model="showPopup"
              round
              position="bottom"
              title="儿童分类"
              close-icon="close"
              closeable
            >
              <div class="checkBox">
                <van-checkbox-group v-model="selectedValues" @change="onChange">
                  <van-checkbox
                    v-for="option in columns"
                    :name="option.dataCode"
                    :key="option.dataCode"
                  >
                    {{ option.dataName }}
                  </van-checkbox>
                </van-checkbox-group>
              </div>
            </van-popup>
            <van-field
              v-model="form.etCode"
              name="etCode"
              label="儿童档案："
              placeholder="请输入"
              :readonly="isReadonly"
            />
            <van-field
              v-model="form.ssJgName"
              name="ssJgName"
              label="所属机构："
              placeholder="请输入"
              :readonly="isReadonly"
            />
            <!-- 
            <van-field
              :value="initToName(form.etMz)"
              label="民族："
              readonly
              placeholder="请选择"
              name="showMz"
              @click="showMz = true"
            >
            </van-field> -->
            <van-popup v-model="showMz" round position="bottom">
              <van-picker
                title="民族"
                show-toolbar
                :columns="mzList"
                @confirm="onConfirm1"
              >
                <template v-slot:option="mzList">
                  {{ mzList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <!-- <van-field
              :value="form.sqRq | formatDate"
              label="申请日期："
              readonly
              placeholder="请选择"
              name="csRq"
              @click="showRq = true"
            /> -->
            <van-popup v-model="showRq" round position="bottom">
              <van-datetime-picker
                v-model="currentDate"
                type="date"
                title="选择年月日"
                :min-date="minDate"
                :max-date="maxDate"
                @confirm="onConfirm4"
                @cancel="showRq = false"
              />
            </van-popup>
            <!-- <van-field
              :value="initToName(form.hkXz)"
              label="户口性质："
              readonly
              placeholder="请选择"
              name="showHk"
              @click="showHk = true"
            >
            </van-field> -->
            <van-popup v-model="showHk" round position="bottom">
              <van-picker
                title="户口性质"
                show-toolbar
                :columns="hkList"
                @confirm="onConfirm2"
              >
                <template v-slot:option="hkList">
                  {{ hkList.dataName }}
                </template>
              </van-picker>
            </van-popup>

            <van-field
              :value="glDj"
              label="管理等级："
              readonly
              placeholder="请选择"
              name="showHk"
              @click="showDj = true"
            >
            </van-field>
            <van-popup v-model="showDj" round position="bottom">
              <van-picker
                title="管理等级"
                show-toolbar
                :columns="djList"
                @confirm="onConfirm3"
              >
                <template v-slot:option="djList">
                  {{ djList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              :value="azQk"
              label="患艾滋情况："
              readonly
              placeholder="请选择"
              name="showAz"
              @click="showAz = true"
            >
            </van-field>
            <van-popup v-model="showAz" round position="bottom">
              <van-picker
                title="患艾滋情况"
                show-toolbar
                :columns="aZList"
                @confirm="onConfirm6"
              >
                <template v-slot:option="aZList">
                  {{ aZList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              :value="jkQk"
              label="健康情况："
              readonly
              placeholder="请选择"
              name="showJk"
              @click="showJk = true"
            >
            </van-field>
            <van-popup v-model="showJk" round position="bottom">
              <van-picker
                title="健康情况"
                show-toolbar
                :columns="jKList"
                @confirm="onConfirm7"
              >
                <template v-slot:option="jKList">
                  {{ jKList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              :value="xyQk"
              label="学业情况："
              placeholder="请选择"
              name="showJk"
              @click="showxyQk = true"
            >
            </van-field>
            <van-popup v-model="showxyQk" round position="bottom">
              <van-picker
                title="学业情况"
                show-toolbar
                :columns="XYQKList"
                @confirm="onConfirm8"
              >
                <template v-slot:option="jKList">
                  {{ jKList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              :value="jxQk"
              label="就学情况："
              readonly
              placeholder="请选择"
              name="showJk"
              @click="showJXQK = true"
            >
            </van-field>
            <van-popup v-model="showJXQK" round position="bottom">
              <van-picker
                title="健康情况"
                show-toolbar
                :columns="JXQKList"
                @confirm="onConfirm9"
              >
                <template v-slot:option="jKList">
                  {{ jKList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              :value="jtJj"
              label="经济来源："
              readonly
              placeholder="请选择"
              name="showJk"
              @click="showJTJJ = true"
            >
            </van-field>
            <van-popup v-model="showJTJJ" round position="bottom">
              <van-picker
                title="经济来源"
                show-toolbar
                :columns="JTJJList"
                @confirm="onConfirm10"
              >
                <template v-slot:option="JTJJList">
                  {{ JTJJList.dataName }}
                </template>
              </van-picker>
            </van-popup>
            <van-field
              v-model="form.hjCodeName"
              name="fieldValue"
              label="户籍地："
              readonly
              placeholder="请选择所在地区"
              @click="showhjDz = true"
            />
            <van-popup v-model="showhjDz" round position="bottom">
              <van-cascader
                v-model="form.hjCodeName"
                title="户籍地"
                :options="options"
                @close="showhjDz = false"
                @finish="onFinish_hj"
              />
            </van-popup>
            <van-field
              v-model="form.xzCodeName"
              name="fieldValueJz"
              label="居住地："
              readonly
              placeholder="请选择所在地区"
              @click="showjzDz = true"
            />
            <van-popup v-model="showjzDz" round position="bottom">
              <van-cascader
                v-model="form.xzCodeName"
                title="居住地址"
                :options="options"
                @close="showjzDz = false"
                @finish="onFinish_jz"
              />
            </van-popup>
            <van-field
              v-model="form.xzDz"
              name="ssJgName"
              label="详细地址："
              placeholder="请输入"
            />
          </van-form>

          <van-button style="width: 200px" type="info" @click="saveEtInfo()"
            >提交</van-button
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { initDataList, initToName } from "@/utils/init.js";
// import { formatDate2 } from "@/utils/format.js";
// import {
//   getEtInfo,
//   saveBaseEtInfoReq,
//   getAreaTreeListReq,
//   upEtJk,
//   upEtLife,
// } from "./api/index";

export default {
  data() {
    return {
      rtStr: "",
      hjDz: "",
      showhjDz: false,
      showjzDz: false,
      hjcascaderValue: "",

      etTypeImgData: [],
      //上面新加对象
      title: "修改儿童基本信息",
      showPopup: false,
      showPz: false,
      show: false,
      showFl: false,
      showMz: false,
      showHk: false,
      showCs: false,
      showDj: false,
      showDz: false,
      showJz: false,
      showRq: false,
      showAz: false,
      showJk: false,
      showxyQk: false,
      showJXQK: false,
      showJTJJ: false,
      options: [],
      selectedValues: [], // 选中的值
      fieldValue: "", //地区
      fieldValueJz: "", //地区
      cascaderValue: "", // 地区
      minDate: new Date(2002, 0, 1),
      maxDate: new Date(2030, 10, 1),
      currentDate: new Date(2024, 0, 1),
      //   sc: require("../../assets/images/sc.png"),
      //   pz: require("../../assets/images/pz.png"),
      itemContent: this.$route.params.id,
      isReadonly: true,
      isDisabled: true,
      etlx: "", // 儿童类型
      etMz: "", // 民族
      hkXz: "",
      glDj: "",
      sqRq: "",
      azQk: "",
      jkQk: "",
      csRq: "",
      fileName: "",
      filePath: "",
      uploadUrl: this.$attachmentUrl + "/fileUpload?busiFolder=rl",
      fileList: [], //附件
      XB_LIST: [
        { code: "1", label: "男" },
        { code: "0", label: "女" },
      ],
      form: {
        ssJgName: "",
        roleNames: "",
        jsBq: "",
        etCode: "",
        etXm: "",
        etNl: "",
        etXb: "",
        csRq: "",
        sfZh: "",
        etMz: "",
        sqRq: "",
        hkXz: "",
        glDj: "",
        hjDz: "",
        xzDz: "",
        azQk: "",
        jkQk: "",
      },
      tempFileUrl: "",
      showJg: false,
      areaList: [], //地区
      columns: [], //儿童分类
      mzList: [],
      hkList: [],
      djList: [],
      aZList: [],
      jKList: [],
      XYQKList: [],
      JXQKList: [],
      JTJJList: [],
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    // if (this.$store.state.areaList.length == 0) {
    //   this.getAreaList();
    // } else {
    //   this.options = this.removeEmptyChildren(this.$store.state.areaList);
    //   this.optionsJz = this.$store.state.areaList;
    // }
    // let _this = this;
    // window["getFileUrl"] = (res) => {
    //   _this.getFileUrl(res);
    // };
  },
  methods: {
    onClickRight() {
      this.showPz = true;
    },
    // 获取文件
    getFileUrl(JSONStr) {
      console.log("JSONStr", JSONStr);
      if (JSONStr.size > 1000 * 1000) {
        JSONStr.url = window.optApp.picChangeHavePath(JSONStr.url);
        JSONStr.url = JSONStr.url.split(";")[0];
      }
      this.showPz = false;
      this.form.rlTx = JSONStr.url;
    },
    upFileExc() {
      this.$notify({ type: "warning", message: "图片上传失败" });
    },
    getFileInfo(res) {
      console.log(res.result);
      window.optApp.delFile(this.tempFileUrl);
      if (isNonNull(res)) {
        console.log(res);
        if (res.code == 200) {
          this.fileList1.push({
            url: res.result,
          });
          this.fileList.push({
            url: this.$fileBaseUrl + res.result,
          });
        } else {
          this.$toast.fail(res.message);
        }
      } else {
        this.$message.error("系统异常");
      }
    },

    picChangePath(base) {
      console.log(base);
    },
    removeEmptyChildren(node) {
      if (typeof node === "object" && node !== null) {
        // 如果节点是对象，遍历对象的所有属性
        for (const key in node) {
          if (key === "children" && Array.isArray(node[key])) {
            if (node[key].length === 0) {
              delete node[key]; // 如果 children 是空数组，删除该属性
            } else {
              // 递归处理 children 数组中的每个元素
              node[key].forEach(this.removeEmptyChildren);
            }
          } else {
            // 递归处理其他属性
            this.removeEmptyChildren(node[key]);
          }
        }
      } else if (Array.isArray(node)) {
        // 如果节点是数组，递归处理每个元素
        node.forEach(this.removeEmptyChildren);
      }
      return node; // 返回处理后的节点
    },
    //保存
    saveEtInfo() {
      this.$loadingU.show("加载中...", 5000);
      this.$refs.form
        .validate()
        .then(() => {
          this.form.csRq = this.formatDate2(this.form.csRq);
          this.form.sqRq = this.formatDate2(this.form.sqRq);
          if (this.form.etXb === "0") {
            this.form.etXb = "女";
          } else {
            this.form.etXb = "男";
          }

          let param = {
            etInfo: {
              etId: this.itemContent,
              ssJg: this.$store.state.organId,
              ...this.form,
            },
            roleIds: this.selectedValues,
            userId: this.$store.state.userId,
          };
          let param2 = {
            ...this.form,
          };

          // 使用 Promise.all 等待所有请求完成
          Promise.all([
            upEtJk(param2),
            upEtLife(param2),
            saveBaseEtInfoReq(param),
          ])
            .then((results) => {
              const res = results[2];
              if (res.state === 0) {
                this.$notify({ type: "success", message: res.message });
                this.$router.go(-1);
              } else {
                this.$notify({ type: "danger", message: res.message });
              }
            })
            .catch(() => {
              this.$notify({ type: "warning", message: "系统接口出现异常" });
            })
            .finally(() => {
              this.$loadingU.hide();
            });
        })
        .catch((err) => {
          // 在这里去截取验证失败信息，并跳转到那个位置就需要设置name了，这个name可以随意设值
          if (err && err.length > 0 && err[0].name) {
            this.$refs.form.scrollToField(err[0].name, true);
          }
          this.$loadingU.hide();
        });
    },

    handlehjDz() {
      this.showhjDz = true;
    },
    handlejzDz() {
      this.showjzDz = true;
    },
    // 全部选项选择完毕后，会触发 finish 事件
    onFinish({ selectedOptions }) {
      this.showDz = false;
      this.areaList = selectedOptions.map((option) => option.value);
      this.fieldValue = selectedOptions.map((option) => option.text).join(",");
    },
    onFinish_hj({ selectedOptions }) {
      this.showhjDz = false;
      this.areaList = selectedOptions.map((option) => option.value);
      this.form.hjCodeName = selectedOptions
        .map((option) => option.text)
        .join(",");
      const codes = selectedOptions.map((option) => option.id);
      this.form.hjCode = codes[codes.length - 1]; // 取最后一个code
    },
    onFinish_jz({ selectedOptions }) {
      this.showjzDz = false;
      this.areaList = selectedOptions.map((option) => option.value);
      this.form.xzCodeName = selectedOptions
        .map((option) => option.text)
        .join(",");
      const codes = selectedOptions.map((option) => option.id);
      this.form.xzCode = codes[codes.length - 1]; // 取最后一个code
    },
    photograph() {
      window.optApp.openSysCamera();
    },
    locationUpload() {
      //  选择文件加一个参数  type 为1，返回文件信息。其他情况 上传之后 返回地址信息
      window.optApp.appFileChoose("image", 0);
    },
    //日期
    formatDate2(time) {
      return formatDate2(time);
    },
    //编码转中文
    initToName(code) {
      return initToName(code);
    },
    findDataName(array, code) {
      const entry = array.find((entry) => entry.dataCode === code);
      return entry ? entry.dataName : "";
    },
    //初始化
    init() {
      this.$loadingU.show("加载中...", 5000);
      getEtInfo({ etId: this.itemContent })
        .then((res) => {
          if (res.state == 0) {
            this.form = res.data.etInfo;
            this.fieldValue = res.data.etInfo.hjCodeSupper;
            this.fieldValueJz = res.data.etInfo.xzCodeSupper;
            this.rtStr = res.data.etInfo.roleNames;
            this.glDj = this.findDataName(this.djList, res.data.etInfo.glDj);
            this.azQk = this.findDataName(this.aZList, res.data.etInfo.azQk);
            this.jkQk = this.findDataName(this.jKList, res.data.etInfo.jkQk);
            this.xyQk = this.findDataName(this.XYQKList, res.data.etInfo.xyQk);
            this.jxQk = this.findDataName(this.JXQKList, res.data.etInfo.jxQk);
            this.jtJj = this.findDataName(this.JTJJList, res.data.etInfo.jtJj);
            if (res?.data?.etInfo?.roleIds) {
              this.selectedValues = res?.data?.etInfo?.roleIds
                .split(",")
                .sort()
                .reverse();
              const etTypes = this.selectedValues;
              const imgData = this.imgData;
              this.etTypeImgData = etTypes.map((etType) => imgData[etType]);
            }
            if (this.form.etXb === "女") {
              this.form.etXb = "0";
            } else {
              this.form.etXb = "1";
            }
            console.log("初始化", this.form.etXb);
            this.fileList.push({ url: res.data.etInfo.rlTx });
            this.$loadingU.hide();
          }
        })
        .catch(() => {
          this.$loadingU.hide();
          this.$notify({ type: "warning", message: "系统接口出现异常" });
        });
    },
    //地区
    getAreaList() {
      getAreaTreeListReq()
        .then((res) => {
          if (res.state == 0) {
            this.options = res.data;
            localStorage.setItem("areaList", JSON.stringify(this.options));
            this.$store.commit("SET_AREALIST", this.options);
          }
        })
        .catch(() => {
          this.$notify({ type: "warning", message: "系统接口出现异常" });
        });
    },
    //人脸上传
    onOversize() {
      this.$toast("文件大小超出限制");
    },
    onFail() {
      this.$toast("上传失败");
    },
    onSuccess(file) {
      console.log(file);
      // this.$toast('上传成功，文件地址：' + file.response.file.url);
    },
    uploadFile(file) {
      console.log(file);
    },
    handleRemove() {
      this.form.rlTx = "";
    },
    // 全部选项选择完毕后，会触发 finish 事件
    onConfirm(value) {
      this.showFl = false;
      this.form.jsBq = value.dataCode;
      this.etlx = value.dataName;
    },
    onConfirm1(value) {
      this.showMz = false;
      this.form.etMz = value.dataCode;
      this.etMz = value.dataName;
    },
    onConfirm2(value) {
      this.showHk = false;
      this.form.hkXz = value.dataCode;
      this.hkXz = value.dataName;
    },
    onConfirm3(value) {
      this.showDj = false;
      this.form.glDj = value.dataCode;
      this.glDj = value.dataName;
    },

    onConfirm4(value) {
      this.showRq = false;
      this.form.sqRq = this.currentDate.toString();
      // this.form.sqRq = value.dataCode
      // this.sqRq = value.dataName
    },
    onConfirm5(value) {
      this.showCs = false;
      this.form.csRq = this.currentDate.toString();
      // this.form.csRq = value.dataCode
      // this.csRq = value.dataName
    },
    onConfirm6(value) {
      this.showAz = false;
      this.form.azQk = value.dataCode;
      this.azQk = value.dataName;
    },
    onConfirm7(value) {
      this.showJk = false;
      this.form.jkQk = value.dataCode;
      this.jkQk = value.dataName;
    },
    onConfirm8(value) {
      this.showxyQk = false;
      this.form.xyQk = value.dataCode;
      this.xyQk = value.dataName;
    },
    onConfirm9(value) {
      this.showJXQK = false;
      this.form.jxQk = value.dataCode;
      this.jxQk = value.dataName;
    },
    onConfirm10(value) {
      this.showJTJJ = false;
      this.form.jtJj = value.dataCode;
      this.jtJj = value.dataName;
    },
    onChange(values) {
      // 找到每个 value 在 this.columns 中对应的索引
      let existingEntryIndexes = values.map((value) => {
        return this.columns.findIndex((entry) => entry.dataCode === value);
      });
      // 使用索引找到对应的 dataName
      let dataNames = existingEntryIndexes.map((index) => {
        return this.columns[index].dataName;
      });
      let dataCode = existingEntryIndexes.map((index) => {
        return this.columns[index].dataCode;
      });
      // 去重
      let dataCodeData = [...new Set(dataCode)];
      const etTypes = dataCodeData;
      const imgData = this.imgData;
      this.etTypeImgData = etTypes.map((etType) => imgData[etType]);
      let uniqueDataNames = [...new Set(dataNames)];
      // 将唯一的 dataNames 拼接成一个字符串，用逗号隔开
      let resultString = uniqueDataNames.join(", ");
      this.rtStr = resultString;
      this.form.roleNames = values.join(", ");
    },
    handleBack() {
      // 处理返回事件
      this.$router.go(-1);
      // 例如：this.$router.go(-1); 或者 this.$router.push('/path');
    },
    handleEdit() {
      this.isReadonly = false;
      this.$notify({ type: "success", message: "您可以编辑操作了" });
    },
  },
};
</script>

<style scoped lang="less">
.contian {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
}
.img2 {
  position: relative;
}

.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
// .grid-box {
//   display: flex;
//   background-color: #f7f8fa;
//   padding: 30px;
//   border-radius: 15px;
//   box-sizing: border-box;
// }
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
</style>
