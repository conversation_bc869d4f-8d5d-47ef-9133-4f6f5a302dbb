<template>
  <div class="null flex-colum">
    <img src="../../assets/images/nullState.png" class="imgsize" alt />
    <span>{{ msg }}</span>
  </div>
</template>
<script>
export default {
  name: "NullState",
  data() {
    return {};
  },
  props: {
    msg: {
      type: String,
      default: "暂无数据",
    },
  },
};
</script>
<style lang="less" scoped>
.null {
  width: 100%;
  font-style: italic;
  left: 0;
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
  .imgsize {
    width: 80%;
  }
  color: #b9b9b9;
  font-size: 15px;
}
</style>
