<template>
  <div class="custom-input">
    <div class="label">{{ label }}<i v-if="isWhite" class="isTrue"> *</i></div>
    <div class="input-container flex-row">
      <slot name="left-icon" v-if="hasLeftIcon"></slot>
      <input
        :type="type"
        :disabled="isDiasable"
        class="input-field"
        :style="inputFieldStyle"
        :placeholder="placeholder"
        :value="value"
        @click="handleClick"
        @input="handleInput"
      />
      <slot name="right-box" v-if="hasRightBox"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomInput",
  props: {
    isEnabled: {
      type: Boolean,
      default: true,
    },
    isWhite: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "text",
    },
    pathologicalId: {
      type: String,
      default: "null",
    },
    label: {
      type: String,
      default: "",
    },
    borderBottomColor: {
      type: String,
      default: "grey",
    },
    placeholder: {
      type: String,
      default: "Please enter",
    },
    error: {
      type: Boolean,
      default: false,
    },
    isDiasable: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String, // 或者直接使用type: any
      default: "",
    },
  },
  data() {
    return {
      dynamicBorderBottomColor: this.borderBottomColor,
    };
  },
  computed: {
    hasLeftIcon() {
      return !!this.$slots["left-icon"];
    },
    hasRightBox() {
      return !!this.$slots["right-box"];
    },

    inputFieldStyle() {
      return {
        borderBottomColor: this.isEnabled
          ? this.error
            ? "red"
            : this.dynamicBorderBottomColor
          : "grey",
        paddingLeft: this.hasLeftIcon ? "30px" : "0",
      };
    },
  },
  methods: {
    handleClick() {
      this.$emit("click"); // Emit inputQuestion event with answer
    },
    handleInput(event) {
      if (this.isEnabled) {
        const value = event.target.value;
        // 检查父组件是否监听了 'inputQuestion' 事件
        if (this.$listeners.inputQuestion) {
          var answer = {
            question: this.label,
            pathologicalId: this.pathologicalId,
            answerStr: value,
          };
          this.$emit("inputQuestion", answer); // Emit inputQuestion event with answer
        }
        // 检查父组件是否监听了 'input' 事件
        if (this.$listeners.input) {
          this.$emit("input", String(value));
        }
        this.dynamicBorderBottomColor = value
          ? "#0f62f9"
          : this.borderBottomColor;
      }
    },
  },
};
</script>

<style scoped>
.isTrue {
  color: rgb(252, 85, 49);
}
.custom-input {
  width: 100%;
  display: inline-block;
}

.input-container {
  position: relative;
  align-items: center;
  width: 100%;
}
.label {
  font-weight: 600;
  font-size: 16px;
  color: #112950;
}
.input-field {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 15px;
  color: #112950;
  border: none;
  outline: none;
  border-bottom: 2px solid transparent;
  background-color: transparent;
  width: 100%;
}

.input-field::placeholder {
  font-weight: 400;
  font-size: 15px;
  color: #b2bac6;
  line-height: 22px;
}

/* 如果需要更多的样式，可以在这里添加 */
</style>
