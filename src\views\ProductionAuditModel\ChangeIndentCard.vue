<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ getDisplayTitle(indentData) }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>申请时间: </i><i class="timedata">{{ formatDateTime(indentData.applyDate) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="location-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>变更类型: </i><i class="timedata">{{ getChangeTypeText(indentData.changeType) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row" v-if="indentData.startTime && indentData.endTime">
      <div class="flex-row timebox">
        <van-icon name="calendar-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>有效期: </i><i class="timedata">{{ indentData.startTime }} 至 {{ indentData.endTime }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row" v-if="indentData.content">
      <div class="flex-row timebox">
        <van-icon name="edit" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>变更理由: </i><i class="timedata">{{ indentData.content }}</i>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>{{ getButtonText(indentData) }}</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
    currentTabState: {
      type: [String, Number],
      default: 1,
    },
  },
  computed: {
    statusClass() {
      // 根据审核状态返回样式类
      switch (this.indentData.typeState) {
        case 1: // 待审核
          return "pending";
        case 2: // 已完成
          return "success";
        default:
          return "pending";
      }
    },
    statusText() {
      // 根据审核状态返回状态文本
      switch (this.indentData.typeState) {
        case 1:
          return "待审核";
        case 2:
          return "已完成";
        default:
          return "待审核";
      }
    },
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
          return '-';
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    getDisplayTitle(data) {
      // 根据变更类型显示不同的标题
      if (data.constructionSite && data.constructionSite.siteName) {
        return data.constructionSite.siteName;
      }
      if (data.changeAgent && data.changeAgent.agentName) {
        return data.changeAgent.agentName;
      }
      if (data.administrativeApplyAgent && data.administrativeApplyAgent.agentName) {
        return data.administrativeApplyAgent.agentName;
      }
      return '变更申请';
    },

    getChangeTypeText(changeType) {
      const changeTypeMap = {
        1: '产生地延期申请',
        2: '产生地变更运输单位',
        3: '产生地变更处置场所',
        4: '产生地变更建筑垃圾产生种类与数量',
        5: '运输业务延期申请',
        6: '产生地延期审核', // 这是我们当前处理的类型
        7: '运输业务变更企业基础资料',
        8: '运输业务变更运输车辆',
        9: '处置地延期申请',
        10: '处置业务变更处置单位基本资料',
        11: '处置业务变更建筑垃圾处置类型与处置能力',
      };
      return changeTypeMap[changeType] || '未知变更类型';
    },

    getButtonText() {
      // 使用当前tab状态而不是数据中的typeState
      const currentState = parseInt(this.currentTabState);

      if (currentState === 1) {
        return '审核'; // 待审核
      }

      if (currentState === 2) {
        return '查看详情'; // 已完成
      }

      return '查看详情'; // 默认
    },

    handleClick(e) {
      // 根据type和changeType跳转到对应的审核页面
      const routeNameMap = {
        // 产生业务 (type=1)
        "1-1": "ProductionStopApproval", // 产生地报停
        "1-2": "ProductionTransportChangeApproval", // 运输单位变更
        "1-3": "ProductionDisposalChangeApproval", // 处置场所变更
        "1-4": "ProductionWasteApproval", // 垃圾种类数量（现在是延期审核样式）
        "1-5": "ProductionCycleChangeApproval", // 垃圾产生周期
        "1-6": "ProductioYanqiApproval", // 产生地延期审核

        // 处理业务 (type=0)
        "0-1": "DisposalStopApproval", // 处置场所报停
        "0-6": "DisposalExtensionApproval", // 延长有效期审核
        "0-10": "DisposalUnitChangeApproval", // 处置单位基本资料变更
        "0-11": "DisposalCapabilityChangeApproval", // 处置类型能力变更

        // 运输业务 (type=3)
        "3-6": "TransportExtensionApproval", // 有效期限延长审核
        "3-8": "TransportCompanyChangeApproval", // 企业基本资料变更
        "3-9": "TransportCompanyChangeApproval", // 运输车辆变更（合并在一个页面）
      };

      const routeKey = `${e.type}-${e.changeType}`;
      const targetRouteName = routeNameMap[routeKey];

      if (targetRouteName) {
        console.log("跳转到路由：", targetRouteName, "数据：", e);
        // 使用params方式传递数据，避免URL中出现[object Object]
        this.$router.push({
          name: targetRouteName,
          params: { data: e },
        });
      } else {
        console.log("未找到对应路由，type：", e.type, "changeType：", e.changeType);
        // 默认跳转到延期审核页面
        this.$router.push({
          name: "ProductioYanqiApproval",
          params: { data: e },
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.state-box {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  
  &.pending {
    background: #faad14;
  }
  
  &.success {
    background: #52c41a;
  }
  
  &.error {
    background: #ff4d4f;
  }
}

.top {
  padding: 16px;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.pre {
  flex: 1;
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 8px 16px;
  
  &:last-child {
    padding-bottom: 16px;
  }
}

.timebox {
  align-items: center;
  width: 100%;
}

.time {
  margin-left: 8px;
  align-items: center;
  
  i {
    font-style: normal;
    font-size: 14px;
    color: #8c8c8c;
    
    &.timedata {
      color: #262626;
      font-weight: 500;
    }
  }
}

.pre-c {
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 0;
  
  p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #1989fa;
    margin-right: 4px;
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
