<template>
  <div>
    <div style="width: 100%; height: 150px" v-if="top150"></div>
    <div class="button" @click="handleClick">
      <div class="btn" :style="{ backgroundColor: buttonColor }">
        <!-- 具名插槽，用于插入图标 -->
        {{ title }}
        <slot name="icon"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "Đang tải...",
    },
    buttonColor: {
      type: String,
      default: "#0065ff",
    },
    top150: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>

<style scoped lang="less">
.button {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 0;
  background-color: rgba(255, 255, 255, 0.3); /* 半透明背景 */
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.button > .btn {
  display: flex; /* 使图标与文本在同一行 */
  align-items: center;
  justify-content: center;
  gap: 8px; /* 图标与文本的间距 */
  width: 327px;
  height: 54px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 17px;
  color: #ffffff;
  line-height: 54px;
  text-align: center;
}
</style>
