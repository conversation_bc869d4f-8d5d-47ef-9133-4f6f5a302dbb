# Development Environment Setup Documentation

## Overview
This document describes the changes made to enable local development without OAuth login and how to restore production environment settings.

## Quick Start Scripts

### Development
- `start-dev.cmd` - Start development server with login bypass
- `dev-start.bat` - Enhanced development script with checks

### Production
- `build-production.cmd` - Build for production
- `build-prod.bat` - Enhanced production build script
- `restore-production.bat` - Restore production login settings

## AI Restoration Instructions
When AI needs to restore production environment, follow these steps:
1. Run `restore-production.bat` OR follow manual steps below
2. Verify all changes are applied correctly
3. Test OAuth login flow works properly

## Changes Made for Development

### 1. User Store Module (`src/store/modules/user.js`)
**Development Changes:**
```javascript
const state = {
    userInfo: {
        userName: '测试用户',
        id: '10',
        // userName: '',
        // id: '',
    },
    volunteerId:'10'
};
```

**Production Restore:**
```javascript
const state = {
    userInfo: {
        // userName: '测试用户',
        // id: '10',
        userName: '',
        id: '',
    },
    volunteerId:''
};
```

### 2. Environment Configuration (`.env.development`)
**Development Changes:**
```
NODE_ENV = 'development'
VUE_APP_PUBLIC_PATH = './'
VUE_APP_PORT = 4040
VUE_APP_API_URL = api/
# VUE_APP_API_URL = api/care-cms
# 开发环境跳过登录
VUE_APP_SKIP_LOGIN = true
#开发环境
```

**Production Restore:**
Remove or set `VUE_APP_SKIP_LOGIN = false`

### 3. Index View Component (`src/views/IndexView.vue`)
**Development Changes:**
```javascript
} else {
  // 开发环境跳过登录
  if (process.env.VUE_APP_SKIP_LOGIN === 'true') {
    console.log("开发环境，跳过登录检查，使用测试用户");
    // 使用store中预设的测试用户信息，无需额外设置
  } else {
    this.goLogin();
  }
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**Production Restore:**
```javascript
} else {
  this.goLogin();
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

## Production Environment Restoration Steps

### Step 1: Restore User Store
1. Open `src/store/modules/user.js`
2. Comment out test user info:
   - Change `userName: '测试用户'` to `userName: ''`
   - Change `id: '10'` to `id: ''`
   - Change `volunteerId:'10'` to `volunteerId:''`

### Step 2: Update Environment Configuration
1. Open `.env.development`
2. Remove or comment out `VUE_APP_SKIP_LOGIN = true`

### Step 3: Restore Index View Logic
1. Open `src/views/IndexView.vue`
2. Replace the development login logic with production logic
3. Remove the `VUE_APP_SKIP_LOGIN` check

### Step 4: Verify Production Settings
1. Check `.env.production` has correct API URL
2. Ensure `vue.config.js` proxy settings are correct for production
3. Test OAuth login flow works properly

## OAuth Login Flow (Production)

### 1. Login Initialization
- User clicks login or accesses app without code
- Calls `initLogin()` API (`/minapi/zwlogin/initLogin`)
- Redirects to OAuth provider: `https://www.xtdzzw.cn/xtsso/authz/oauth/v20/authorize`

### 2. OAuth Callback
- OAuth provider redirects back with `code` parameter
- App extracts code from URL (supports both query string and hash)
- Calls `loginWithCode(code)` method

### 3. Login Verification
- Calls `toLogin(code)` API (`/minapi/zwlogin/toLogin`)
- Backend validates code and returns user info
- Frontend stores user info in Vuex store and localStorage

## API Configuration

### Development
- Base URL: `api/` (proxied through vue.config.js)
- Proxy target: `http://**************:5511/tpss/`

### Production
- Base URL: `http://**************:5511/tpss` (from .env.production)

## Dependencies
- Vue 2.6.14
- Vant 2.12.53 (Mobile UI)
- Vue Router 3.5.1
- Vuex 3.1.0
- Axios 1.6.2

## Build Commands
- Development: `npm run serve`
- Production: `npm run build`
- Lint: `npm run lint`

## Notes
- Router mode: 'history'
- Mobile-first design with amfe-flexible
- PostCSS px-to-rem conversion
- FastClick for mobile optimization
