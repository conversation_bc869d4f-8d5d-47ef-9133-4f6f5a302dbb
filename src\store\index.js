import Vue from 'vue';
import Vuex from 'vuex';
import user from './modules/user'
import tips from './modules/tips'
import player from './modules/player'
import config from './modules/config'
import pay from './modules/pay'
import record from './modules/record'
import Permissions from './modules/Permissions'
import proturn from './modules/proturn'
import createPersistedState from 'vuex-persistedstate';
Vue.use(Vuex);
const store = new Vuex.Store({
    modules: {
        tips,
        player,
        config,Permissions,
        pay,proturn,record,
        user // 注册你的用户模块
        // 其他模块
    },
    plugins: [
        //注册模块并保存数据
        createPersistedState({
            storage: window.sessionStorage,
            paths: ["Permissions","record","user","pay","config", "game", "handlegame", "tips", "websock", "register", "player","proturn"]
        })
    ],
});

export default store;
