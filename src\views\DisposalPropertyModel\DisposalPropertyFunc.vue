<template>
  <div class="page-container">
    <NavHeader ref="navHeader" title="处置业务" :back="true"></NavHeader>

    <!-- 业务分组 -->
    <div class="business-groups">
      <!-- 核心审核业务 -->
      <div class="business-group">
        <LabelHeader left="核心审核业务"></LabelHeader>
        <div class="group-card">
          <div class="menu-grid">
            <div class="menu-item" @click="handleMenuClick('/DisposalPropertyModel/list', '处置场所审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/bh.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.aPendingApprovalCount > 0">
                  {{ statisticsZTC.aPendingApprovalCount > 99 ? '99+' : statisticsZTC.aPendingApprovalCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置核准审核</h4>
                <p>审核处置场所申请材料</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/DisposalPropertyModel/final/list', '处置核准终审')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a2.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.aFinalPendingApprovalCount > 0">
                  {{ statisticsZTC.aFinalPendingApprovalCount > 99 ? '99+' : statisticsZTC.aFinalPendingApprovalCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置核准终审</h4>
                <p>处置申请终审流程</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>


            <div class="menu-item" @click="handleMenuClick('/DisposalPropertyModel/register/list', '处置场所注册审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/dh.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.chuCount > 0">
                  {{ statisticsZTC.chuCount > 99 ? '99+' : statisticsZTC.chuCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置单位注册审核</h4>
                <p>审核处置单位注册信息</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

          </div>
        </div>
      </div>

      <!-- 延期与变更业务 -->
      <div class="business-group">
        <LabelHeader left="变更业务审核"></LabelHeader>
        <div class="group-card">
          <div class="menu-grid">

            <div class="menu-item" @click="handleMenuClick('/DisposalAudit/extension/list', '延长有效期审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/tz.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.yChuCount > 0">
                  {{ statisticsZTC.yChuCount > 99 ? '99+' : statisticsZTC.yChuCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>延长有效期审核</h4>
                <p>审核延长有效期申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/DisposalAudit/company/list', '处置单位变更审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a4.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.chuChangeInfoCount > 0">
                  {{ statisticsZTC.chuChangeInfoCount > 99 ? '99+' : statisticsZTC.chuChangeInfoCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置单位基本资料变更审核</h4>
                <p>审核处置单位基本资料变更申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/DisposalAudit/capacity/list', '建筑垃圾处置类型与处置能力变更审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/a5.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.chuChangeGarbageCount > 0">
                  {{ statisticsZTC.chuChangeGarbageCount > 99 ? '99+' : statisticsZTC.chuChangeGarbageCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置类型能力变更审核</h4>
                <p>审核建筑垃圾处置类型与处置能力变更</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

            <div class="menu-item" @click="handleMenuClick('/DisposalPropertyModel/baoting/list', '处置场所报停审核')">
              <div class="menu-icon">
                <img src="@/assets/images/cg/tz.png" alt="" />
                <div class="notification-badge" v-if="statisticsZTC?.bChuCount > 0">
                  {{ statisticsZTC.bChuCount > 99 ? '99+' : statisticsZTC.bChuCount }}
                </div>
              </div>
              <div class="menu-content">
                <h4>处置场所报停审核</h4>
                <p>审核处置场所报停申请</p>
              </div>
              <van-icon name="arrow" class="menu-arrow" />
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { statisticsZTC } from "@/api/config";
import { mapState } from "vuex";
import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  components: {
    LabelHeader,
  },
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    ...mapState("config", ["statisticsZTC"]),
  },
  mounted() {
    // 页面加载完成后的初始化操作
    this.initPage();
  },
  methods: {
    // 初始化页面
    initPage() {
      // 获取统计数据
      this.getStatistics();
      console.log('处置业务页面初始化完成');
    },

    // 获取统计数据
    getStatistics() {
      const reqData = {};
      statisticsZTC(reqData).then((res) => {
        if (res.data.success) {
          this.$store.commit("config/SET_statisticsZTC", res.data.result);
          console.log('统计数据获取成功:', res.data.result);
        } else {
          this.$store.commit("config/remove_statisticsZTC");
          console.log('统计数据获取失败');
        }
      }).catch((error) => {
        console.error('获取统计数据出错:', error);
        this.$store.commit("config/remove_statisticsZTC");
      });
    },

    // 统一的菜单点击处理
    handleMenuClick(route, menuName) {
      // 添加点击反馈
      this.addClickFeedback();

      if (route) {
        // 有路由的菜单项，直接跳转
        this.$go(route);
      } else {
        // 开发中的菜单项，显示提示
        this.showDevelopingToast(menuName);
      }
    },

    // 显示开发中提示
    showDevelopingToast(menuName) {
      this.$toast({
        message: `${menuName}功能正在开发中，敬请期待...`,
        duration: 2500,
        position: 'middle',
      });
    },

    // 添加点击反馈效果
    addClickFeedback() {
      // 如果支持触觉反馈，添加轻微震动
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    },
  },
};
</script>

<style scoped lang="less">
// 页面容器
.page-container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  box-sizing: border-box;
}

// 业务分组容器
.business-groups {
  padding: 16px 16px 20px;
}

// 单个业务分组
.business-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 分组卡片
.group-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  overflow: hidden;
}

// 菜单网格
.menu-grid {
  padding: 8px;
}

// 菜单项
.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 8px 0;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    background: #f8faff;
    border-color: #d9e7ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  // 开发中状态
  &.developing {
    background: #fafafa;
    border-color: #e8e8e8;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
    }

    .menu-content h4 {
      color: #8c8c8c;
    }

    .menu-content p {
      color: #bfbfbf;
    }

    .menu-arrow {
      color: #d9d9d9;
    }
  }
}

// 菜单图标
.menu-icon {
  position: relative;
  width: 48px;
  height: 48px;
  background: #f8faff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;

  img {
    width: 28px;
    height: 28px;
  }

  // 通知徽章
  .notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    min-width: 18px;
    height: 18px;
    background: #ff4d4f;
    color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-sizing: border-box;
    border: 2px solid #ffffff;
    line-height: 1;
  }

  // 开发中标签
  .developing-tag {
    position: absolute;
    top: -4px;
    right: -8px;
    background: #faad14;
    color: #ffffff;
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 8px;
    line-height: 1;
    transform: scale(0.9);
  }
}

// 菜单内容
.menu-content {
  flex: 1;

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 4px 0;
    line-height: 22px;
  }

  p {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0;
    line-height: 18px;
  }
}

// 菜单箭头
.menu-arrow {
  color: #bfbfbf;
  font-size: 16px;
  margin-left: 8px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

// 响应式适配
@media (max-width: 375px) {
  .business-groups {
    padding: 0 12px 20px;
  }

  .menu-item {
    padding: 14px;

    .menu-icon {
      width: 44px;
      height: 44px;
      margin-right: 12px;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .menu-content h4 {
      font-size: 15px;
    }

    .menu-content p {
      font-size: 12px;
    }
  }
}

// 深色模式适配（如果需要）
@media (prefers-color-scheme: dark) {
  .page-container {
    background: linear-gradient(180deg, #1f1f1f 0%, #141414 100%);
  }

  .group-card {
    background: #262626;
    border-color: #434343;
  }

  .menu-item {
    background: #262626;
    border-color: #434343;

    &:hover {
      background: #303030;
      border-color: #595959;
    }

    &.developing {
      background: #1f1f1f;
      border-color: #303030;

      &:hover {
        background: #262626;
      }
    }
  }

  .menu-content h4 {
    color: #ffffff;
  }

  .menu-content p {
    color: #8c8c8c;
  }

  .menu-icon {
    background: #303030;
  }
}
</style>
