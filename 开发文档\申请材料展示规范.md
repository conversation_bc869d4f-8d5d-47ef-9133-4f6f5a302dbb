# 申请材料展示规范

## 1. 数据结构

### 1.1 附件数据格式
```javascript
attachments: [
  {
    "id": 7323,
    "attachType": "applyImgs1", // 附件类型标识
    "orgId": 1,
    "relatedId": 219,
    "attachMark": 1,
    "displayOrder": null,
    "displayTitle": "行政许可申请表", // 显示标题
    "filePath": "/uploads/file.pdf", // 文件路径
    "fileName": "file.pdf"
  }
]
```

## 2. 固定分类规范

### 2.1 产生业务申请材料分类（按顺序显示）
1. **行政许可申请书** - `attachType: "applyImgs1"`
2. **营业执照或法人证书** - `attachType: "applyImgs2"`
3. **授权委托书** - `attachType: "applyImgs3"`
4. **信用承诺书** - `attachType: "applyImgs4"`
5. **运输合同** - `attachType: "applyImgs5"`
6. **处置合同** - `attachType: "applyImgs6"`
7. **建筑垃圾产生信息表** - `attachType: "applyImgs7"`

### 2.2 其他业务类型分类
根据具体业务需求定义相应的 `attachType` 分类。

## 3. 页面实现

### 3.1 模板结构
```vue
<!-- 申请材料 -->
<div class="attachments-section">
  <LabelHeader left="申请材料"></LabelHeader>
  <div class="information">
    <div class="y-card-box">
      <div class="y-card">
        <div class="categorized-attachments">
          <!-- 每个分类 -->
          <div class="attachment-category" v-for="category in attachmentCategories" :key="category.type">
            <div class="category-title">{{ category.title }}</div>
            <div class="category-files">
              <!-- 有文件时显示文件列表 -->
              <div
                v-for="(item, index) in getAttachmentsByType(category.type)"
                :key="category.type + '-' + index"
                class="attachment-row"
                @click="previewFile(item)"
              >
                <!-- 图片文件 -->
                <div v-if="isImageFile(item.filePath)" class="image-row">
                  <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                  <div class="attachment-title">{{ item.displayTitle }}</div>
                </div>
                
                <!-- 非图片文件 -->
                <div v-else class="file-row">
                  <div class="file-info">
                    <div class="file-icon">
                      <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                    </div>
                    <div class="file-details">
                      <div class="file-name">{{ item.displayTitle }}</div>
                      <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                    </div>
                    <div class="file-action">
                      <van-icon name="eye-o" size="20px" color="#1989fa" />
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 无文件时显示提示 -->
              <div v-if="getAttachmentsByType(category.type).length === 0" class="no-files">
                暂无文件
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

### 3.2 数据配置
```javascript
data() {
  return {
    // 申请材料分类配置
    attachmentCategories: [
      { type: 'applyImgs1', title: '行政许可申请书' },
      { type: 'applyImgs2', title: '营业执照或法人证书' },
      { type: 'applyImgs3', title: '授权委托书' },
      { type: 'applyImgs4', title: '信用承诺书' },
      { type: 'applyImgs5', title: '运输合同' },
      { type: 'applyImgs6', title: '处置合同' },
      { type: 'applyImgs7', title: '建筑垃圾产生信息表' },
    ],
  };
},
```

### 3.3 核心方法
```javascript
methods: {
  // 根据attachType获取对应的附件
  getAttachmentsByType(attachType) {
    if (!this.infoData || !this.infoData.attachments) {
      return [];
    }
    
    // 只返回指定类型且有实际文件路径的附件
    return this.infoData.attachments.filter(item => 
      item.attachType === attachType &&
      item.filePath && 
      item.filePath.trim() !== '' && 
      item.filePath !== null && 
      item.filePath !== undefined
    );
  },

  // 获取文件完整URL
  getFileUrl(filePath) {
    if (!filePath) return '';
    if (filePath.startsWith('http')) {
      return filePath;
    }
    return `${FILE_BASE_URL}${filePath}`;
  },

  // 判断是否为图片文件
  isImageFile(filePath) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
    return imageExtensions.includes(extension);
  },

  // 文件预览
  previewFile(item) {
    const fileUrl = this.getFileUrl(item.filePath);

    if (this.isImageFile(item.filePath)) {
      // 图片大图预览 - 使用直接导入的 ImagePreview 组件
      ImagePreview([fileUrl]);
    } else {
      // 其他文件类型预览
      const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
      if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
        this.openFilePreview(fileUrl, extension);
      } else {
        this.downloadFile(fileUrl, item.displayTitle);
      }
    }
  },

  // 打开文件预览（非图片文件）
  openFilePreview(fileUrl, extension) {
    if (extension === '.pdf') {
      // PDF可以直接在浏览器中预览
      window.open(fileUrl, '_blank');
    } else {
      // Office文档使用在线预览服务
      const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
      window.open(previewUrl, '_blank');
    }
  },

  // 下载文件
  downloadFile(fileUrl, fileName) {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },
}
```

## 4. 样式规范

### 4.1 分类容器样式
```less
.attachments-section {
  .categorized-attachments {
    .attachment-category {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
        padding: 0 4px;
      }
    }
  }
}
```

### 4.2 文件显示样式
```less
.category-files {
  .attachment-row {
    margin-bottom: 12px;
    cursor: pointer;

    // 图片行样式
    .image-row {
      img {
        width: 100%;
        max-height: 200px;
        object-fit: contain;
        border-radius: 8px;
        background: #f5f5f5;
        border: 1px solid #e6f4ff;
      }

      .attachment-title {
        font-size: 12px;
        color: #8c8c8c;
        text-align: center;
        margin-top: 6px;
        line-height: 18px;
      }
    }

    // 文件行样式
    .file-row {
      .file-info {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #e6f4ff;
        border-radius: 8px;
        transition: all 0.3s;

        .file-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8faff;
          border-radius: 6px;
          margin-right: 10px;
        }

        .file-details {
          flex: 1;

          .file-name {
            font-size: 13px;
            color: #262626;
            font-weight: 500;
            margin-bottom: 2px;
            line-height: 18px;
          }

          .file-type {
            font-size: 11px;
            color: #8c8c8c;
            line-height: 14px;
          }
        }

        .file-action {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  // 无文件提示样式
  .no-files {
    font-size: 12px;
    color: #8c8c8c;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;
  }
}
```

## 5. 文件预览功能

### 5.1 图片大图查看
- **功能**：点击图片可以查看大图，支持缩放和滑动
- **实现**：使用 Vant 的 `ImagePreview` 组件
- **触发**：点击图片区域即可触发
- **支持格式**：`.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`

### 5.2 文档预览
- **PDF文档**：在新窗口直接打开
- **Office文档**：使用微软在线预览服务
- **其他文件**：直接下载到本地

### 5.3 文件类型识别
```javascript
// 支持的图片格式
const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

// 支持在线预览的文档格式
const previewableExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
```

## 6. 交互体验

### 6.1 图片显示
- **缩略图**：在列表中显示适当大小的缩略图
- **点击反馈**：添加点击效果和过渡动画
- **加载状态**：图片加载时显示占位符

### 6.2 文件列表
- **图标区分**：不同文件类型使用不同颜色的图标
- **悬停效果**：鼠标悬停时显示边框高亮
- **点击区域**：整个文件行都可点击

## 7. 使用说明

1. **必须显示所有分类标题**：即使某个分类下没有文件，也要显示分类标题和"暂无文件"提示
2. **文件路径验证**：只显示有实际文件路径的附件
3. **图片大图查看**：点击图片自动调用 `this.$imagePreview()` 查看大图
4. **文档预览**：支持PDF直接预览，Office文档在线预览
5. **响应式设计**：适配移动端显示
6. **调试支持**：在控制台打印附件数据便于调试

## 8. 依赖组件

### 8.1 必需导入
```javascript
import { ImagePreview } from 'vant';
import { FILE_BASE_URL } from "@/utils/globalConstants";
```

### 8.2 必需组件
- **Vant ImagePreview**：用于图片大图查看
- **Vant Icon**：用于文件类型图标显示
- **LabelHeader**：用于区域标题显示

### 8.3 图片预览调用方式
```javascript
// 正确的调用方式
ImagePreview([fileUrl]);

// 如果全局注册有问题，也可以尝试
this.$ImagePreview([fileUrl]);
```

## 9. 错误处理

### 9.1 文件加载失败
- 图片加载失败时显示默认占位图
- 文件预览失败时提供下载选项

### 9.2 数据异常
- 附件数据为空时显示"暂无文件"
- 文件路径无效时不显示该附件
