<template>
  <div class="card">
    <div class="state-box" :class="statusClass">
      {{ statusText }}
    </div>
    <div class="top flex-row">
      <!-- <img :src="indentData?.pro?.projectImg" class="proimg" alt="" /> -->
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData?.pro?.projectName }}</div>
        </div>
        <!-- <div class="text-ellipsis-2" v-if="indentData?.pro?.proIntroduction">
          {{ indentData?.pro?.proIntroduction }}
        </div> 
        <div class="text-ellipsis-2" v-else>Chưa có hồ sơ</div>-->
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="friends-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>tên người dùng:&nbsp;</i
          ><i class="timedata">{{ indentData?.order?.userName }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row" style="margin-top: -10px">
      <div class="flex-row timebox">
        <van-icon name="notes-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>Cấp:&nbsp;</i
          ><i class="timedata">{{ indentData?.pro?.proLevel }}</i>
        </div>
      </div>
      <div class="flex-row timebox">
        <van-icon name="points" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>Tiền Thưởng: &nbsp;</i
          ><i class="timedata"
            >đ {{ this.$moneyGs(indentData?.pro?.subsidyMoney) }}</i
          >
        </div>
      </div>
    </div>
    <!-- //年龄与人数 -->
    <div class="bottom flex-row" style="margin-top: -10px">
      <div class="flex-row timebox">
        <van-icon name="manager-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>Nam:&nbsp;</i
          ><i class="timedata">{{ indentData?.pro?.manNum }} </i>&nbsp;/&nbsp;
          <i>Nữ:&nbsp;</i
          ><i class="timedata">{{ indentData?.pro?.womanNum }} </i>
        </div>
      </div>
      <div class="flex-row timebox">
        <van-icon name="friends-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>Tuổi:&nbsp;</i
          ><i class="timedata"
            >{{ indentData?.pro?.starAge }}-{{ indentData?.pro?.endAge }}</i
          >
        </div>
      </div>
    </div>
    <div class="bottom flex-row" style="margin-top: -10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>Thời gian:</i
          ><i class="timedata">{{
            this.$formatIsoString(indentData?.order?.regTime)
          }}</i>
        </div>
      </div>
      <div></div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { trialOrder } from "@/api/pro";
import { addPrePayOrder } from "@/api/pay";
import { Dialog } from "vant";
export default {
  data() {
    return {
      isDialogShow: true,
    };
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}), // 默认启用一个空对象
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    statusClass() {
      switch (this.indentData.order.orderStatus) {
        case (0, 2, 3, 4):
          return "error";
        case (6, 7):
          return "success";
        case 5:
          return "end";
        default:
          return "end";
      }
    },
    statusText() {
      switch (this.indentData.order.orderStatus) {
        case 0:
          return "Ký kết hợp đồng";
        //待签订协议
        case 2:
          return "Chưa thanh toán";
        //待付款
        case 3:
          return "Chưa xác nhận địa chỉ";
        //待确认地址
        case 4:
          return "Đang giao thuốc";
        //待收货
        case 5:
          return "Bắt đầu thử thuốc";
        //开始试药
        case 6:
          return "Nhận tiền đặt cọc";
        //领取押金
        case 7:
          return "Nhận tiền thưởng";
        //领取补偿金
        case 9:
          return "Hủy xin việc";
        // return "取消订单";
        case 10:
          return "Đã xong";
        // return "订单结束";
        default:
          return "";
      }
    },
  },
  methods: {
    btnText() {
      switch (this.indentData.order.orderStatus) {
        case 0:
          return "Ký Hợp Đồng";
        // return "去签协议";
        case 2:
          return "Đặt Ngay";
        // return "去付款";
        case 3:
          return "Xác Nhận Địa Chỉ";
        // return "确认地址";
        case 4:
          return "Xác Nhận Nhận Hàng";
        // return "确认收货";
        case 5:
          return "Tải Lên Video";
        // return "开始试药";
        case 6:
          return "Hoàn Trả Đặc Cọc";
        // return "退押金";
        case 7:
          return "Nhận Tiền Thưởng";
        // return "领取补偿金";
        default:
          return "";
      }
    },
    formatDateTime(isoString) {
      // 将 ISO 8601 字符串转换为 Date 对象
      const date = new Date(isoString);
      // 检查是否为有效日期
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      // 获取年、月、日、小时、分钟和秒
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      // 拼接成所需格式
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    },
    handleClick(status) {
      if (status === 0) {
        this.sumBitGoSign();
      }
      if (status === 2) {
        this.goPay();
      }
      if (status === 3) {
        this.configAddress();
      }
      if (status === 4) {
        this.configAddressIsjion();
      }
      if ([6, 5, 7].includes(status)) {
        this.goRecord();
      }
    },
    configAddress() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/set_show", true);
    },
    goPay() {
      this.$store.commit("pay/initData");
      this.$store.commit("pay/SET_type", "ZHIFU");
      var reqData = {
        orderNo: this.indentData.order.orderNo,
        volunteerId: this.userInfo.volunteerId,
      };
      addPrePayOrder(reqData).then((res) => {
        if (res.data.data.data !== null) {
          this.$store.commit("pay/SET_url", res.data.data.data.qrDataURL);
          this.$store.commit("pay/SET_dialogShow", true);
        } else {
          this.$toast({
            message: res.data.data.desc,
            duration: 2000,
          });
        }
      });
    },
    goRecord() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$store.commit("proturn/SET_orderData", this.indentData.order);
      this.$router.push("/drug/recordstate");
    },
    configAddressIsjion() {
      Dialog.confirm({
        title: "Xác nhận đã nhận thuốc",
        // message: "确认收到药品，开始试药？请仔细确认，这代表试药即将开始！！",
        message:
          "Xác nhận đã nhận thuốc và bắt đầu dùng thử? Vui lòng xác nhận cẩn thận, điều này có nghĩa là phiên tòa sắp bắt đầu!!",
        confirmButtonColor: "#0065ff",
        confirmButtonText: "Xác Nhận",
        cancelButtonText: "Hủy Bỏ",
      })
        .then(() => {
          var reqData = {
            orderNo: this.indentData.order.orderNo,
            volunteerId: this.userInfo.volunteerId,
          };
          trialOrder(reqData).then((res) => {
            this.$toast({
              message: res.data.ynMsg,
              duration: 2000,
            });
            if (res.data.state === 0) {
              this.$emit("getList");
            }
          });
        })
        .catch(() => {
          // on cancel
        });
    },
    sumBitGoSign() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.dispatch("proturn/resetCellData");
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$router.push("/pro/paydetils");
    },
  },
};
</script>

<style scoped lang="less">
.card {
  width: 327px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #f2f4f5;
  position: relative;
  .state-box {
    position: absolute;
    right: 30px;
    border-radius: 10px;
    top: -10px;
    padding: 2px 12px;
    box-sizing: border-box;
    font-weight: 400;
    font-size: 12px;
    text-align: center;
    color: #ffffff;
    line-height: 18px;
  }
  .success {
    background-color: #1989fa;
  }
  .error {
    background-color: #f33060;
  }
  .end {
    background-color: #909399;
  }
  .top {
    align-items: center;
    padding: 16px 16px 0;
    box-sizing: border-box;
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.proimg {
  width: 72px;
  height: 72px;
  border-radius: 12px 12px 12px 12px;
}

.pre {
  width: 100%;
  height: 40px;
  justify-content: space-around;
}
.pre div:nth-child(1) {
  width: 100%;
  font-weight: 600;
  font-size: 17px;
  color: #112950;
}

.pre div:nth-child(2) {
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #8e9aab;
  line-height: 18px;
}
.bottom {
  padding: 10px 16px;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  .timebox {
    align-items: center;
    .timeimg {
      width: 16px;
      height: 16px;
    }
    .time {
      margin-left: 5px;
      font-weight: 600;
      font-size: 12px;
      color: #b2bac6;
      .timedata {
        color: #5e6f88 !important;
      }
    }
  }
  .pre-c {
    padding: 5px 5px;
    align-items: center;
    font-weight: 600;
    font-size: 12px;
    color: #1989fa;
    line-height: 18px;
  }
}
</style>
