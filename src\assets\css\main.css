:root {
  --login-padding: 157px;
  /*主题色*/
  --color-zhuti: #0f62f9;
  --color-del: #F56C6C;
  --color-zhuti-bg:#0f62f9;
  /* 公用字体颜色 */
  --color-gray: #B9B9B9;
  --color-font: #B9B9B9;
  /* 副标题颜色 */
  --color-subtitle: #A9A9A9;
  /* nav底色，按钮底色 */
  --background-blue: #133CB2;
  /*一号字体*/
  --font-size-1: 20pt;
  /*二号字体*/
  --font-size-2: 18pt;
  /*三号字体*/
  --font-size-3: 4rem;
  /*四号字体*/
  --font-size-4: 5rem;
  /*字体颜色*/
  --font-color-1: #b2b2b2;
  /*二号字体*/
  --font-color-2: #030303;
  /*三号字体*/
  --font-size-3: 4rem;
  /*四号字体*/
  --font-size-4: 5rem;
  --padding-common: 0 16px;
  --header-color-bule: rgb(64, 158, 255);
  --backHome-green: #91D100;
  --backHome-blue: #58C5FE;
  --backHome-yellow: #FFCC00;
  --backHome-shadow: #999999;
  --backFooterBar-gary: #F7F7F7;
  --margin-leftvw: 1vw;
  --margin-topvh: 1vh;
  --margin-buttom2vh: 2vh;
  --margin-top3vh: 3vh;
  --margin-top4vh: 4vh;
  --margin-top5vh: 5vh;
  --padding1vw: 1vw;
  --padding2vw: 2vw;
  --padding-left1vw: 1vw;
  --padding-left2vw: 2vw;
  --padding-left3vw: 3vw;
  --padding-left4vw: 4vw;
  --padding-left5vw: 5vw;
  --padding-right2vw: 2vw;
  --padding-right3vw: 3vw;
  --padding-right4vw: 4vw;
  --padding-right5vw: 5vw;
  --padding-2vw: 2vw;
  --margin-left-2-5vw: 2.5vw;
  --height-adjustable: 32px;
  --lineHeight-adjustable: 32px;
}