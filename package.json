{"name": "xhdl", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.6.2", "core-js": "^3.8.3", "fastclick": "^1.0.6", "flv.js": "^1.6.2", "js-md5": "^0.8.3", "js-sha256": "^0.10.1", "less-loader": "^6.0.0", "lodash": "^4.17.21", "md5": "^2.3.0", "postcss-pxtorem": "^6.0.0", "qrcode": "^1.5.3", "qs": "^6.14.0", "vant": "^2.12.53", "vue": "^2.6.14", "vue-esign": "^1.1.4", "vue-i18n": "6", "vue-native-websocket": "^2.0.15", "vue-pdf": "^1.0.0", "vue-pull-to": "^0.1.8", "vue-router": "^3.5.1", "vue-video-player": "^4.0.6", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "cache-loader": "^4.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "html-webpack-plugin": "^5.6.4", "less": "^4.0.0", "vue-template-compiler": "^2.6.14", "webpack-dev-server": "^5.2.2"}}