<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      title="运输准行证业务申请审核"
      :back="true"
    ></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.agent?.agentName"
              name="content"
              label-width="120"
              label="运输企业名称："
              :readonly="true"
            />
            <van-field
              :value="infoData?.absorptionYard?.yardName"
              name="content"
              label="产生地名称："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.constructionSite?.siteName"
              name="content"
              label="处置地名称："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="
                (infoData?.startDate || '') + '-' + (infoData?.endDate || '')
              "
              name="content"
              label="有效期："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData.createDate"
              name="content"
              label="申请日期："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.absorptionYard?.coordinateInfo"
              name="content"
              label="产生地地址："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.constructionSite?.coordinateInfo"
              name="content"
              label="处置地地址："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.garbageTypeDic?.dicValue"
              name="content"
              label="建筑垃圾类型："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="`${infoData?.allCapacity ?? ''} 方`"
              name="content"
              label="申报方量："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="
                infoData && infoData.truckList && infoData.truckList.length > 0
                  ? infoData.truckList[0].plateNo
                  : ''
              "
              name="content"
              label="运输车辆："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="
                infoData &&
                infoData.mapListDocument &&
                infoData.mapListDocument.length > 0
                  ? infoData.mapListDocument[0].name
                  : ''
              "
              name="content"
              label="运输线路："
              label-width="120"
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader left="审核"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="applyState" label="审核：" label-width="120px">
              <template #input>
                <van-radio-group v-model="state" direction="horizontal">
                  <van-radio :name="1">通过</van-radio>
                  <van-radio :name="2" style="margin-left: 20px"
                    >驳回</van-radio
                  >
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.acceptedRemarks"
              name="acceptedRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="审核备注"
              type="textarea"
              maxlength="100"
              placeholder="请输入备注"
              show-word-limit
            />
          </van-form>
          <div style="width: 100%; height: 150px"></div>
          <div class="btn-box">
            <yButton title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { updateHaulwaySealH5 } from "@/api/config";
export default {
  data() {
    return {
      form: {
        sealDate: "",
        sealType: 1,
        acceptedRemarks: "",
      },
      state: "",
      showCalendar: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审批备注" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    this.infoData = this.$route.params.data;
  },
  methods: {
    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.sealDate = `${year}-${month}-${day}`;
      this.showCalendar = false;
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始
      const day = String(now.getDate()).padStart(2, "0");
      const hour = String(now.getHours()).padStart(2, "0");
      const minute = String(now.getMinutes()).padStart(2, "0");
      const second = String(now.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    sumBit() {
      console.log("提交的数据：", this.form);
      if (this.state !== "") {
        var reqData = {};
        if (this.state === 1) {
          var reqData = {
            ...this.form,
            sealState: 1,
            sealDate: this.getCurrentTime(),
            id: this.infoData.id,
          };
        }
        if (this.state === 2) {
          var reqData = {
            ...this.form,
            sealDate: this.getCurrentTime(),
            examine_result: 9,
            id: this.infoData.id,
          };
        }
        updateHaulwaySealH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
</style>
