# Build Scripts Documentation

This document describes the Windows batch scripts created for building and deploying the project in different environments.

## Available Scripts

### Development Scripts

#### `start-dev.cmd` (Original)
- Basic development server startup
- Includes dependency installation check
- Enables login bypass for development

#### `start-dev-enhanced.bat` (Enhanced)
- Enhanced development server startup
- Comprehensive environment checks
- Automatic development configuration setup
- Better error handling and troubleshooting

### Build Scripts

#### `build.bat` (Interactive)
- Interactive script to choose build environment
- Calls appropriate environment-specific build script
- User-friendly menu interface

#### `build-test.bat` (Test Environment)
- Builds for test environment
- API URL: `http://**************:47025/tpss`
- Automatic configuration management
- Backup and restore of original settings

#### `build-production.bat` (Production Environment)
- Builds for production environment
- API URL: `http://**************:5511/tpss`
- Calls production restore script first
- OAuth login enabled, development bypasses disabled

### Configuration Scripts

#### `restore-production.bat` (Production Restore)
- Restores production environment settings
- Disables development login bypass
- Removes test user credentials
- Enables OAuth login flow
- Creates backups before making changes

## Usage Instructions

### For Development

1. **Start Development Server:**
   ```cmd
   start-dev-enhanced.bat
   ```
   - Automatically checks and installs dependencies
   - Configures development environment
   - Starts server at http://localhost:8080

### For Building

1. **Interactive Build (Recommended):**
   ```cmd
   build.bat
   ```
   - Choose between test and production environments
   - Guided build process

2. **Direct Test Build:**
   ```cmd
   build-test.bat
   ```
   - Builds directly for test environment

3. **Direct Production Build:**
   ```cmd
   build-production.bat
   ```
   - Builds directly for production environment

### For Production Deployment

1. **Restore Production Settings:**
   ```cmd
   restore-production.bat
   ```
   - Run this before production build
   - Ensures OAuth login is enabled
   - Removes development bypasses

## Environment Configurations

### Test Environment
- **API URL:** `http://**************:47025/tpss`
- **Purpose:** Testing and staging
- **Features:** Same as production but on test servers

### Production Environment
- **API URL:** `http://**************:5511/tpss`
- **Purpose:** Live production deployment
- **Features:** OAuth login, secure authentication

### Development Environment
- **API Proxy:** `http://**************:47025/tpss/`
- **Purpose:** Local development
- **Features:** Login bypass, test user, hot reload

## File Modifications

### Automatic Backups
All scripts create backups before modifying files:
- `.env.production.backup`
- `vue.config.js.backup`
- `user.js.backup`
- `IndexView.vue.backup`
- `.env.development.backup`

### Modified Files
Scripts automatically modify these files:
- `.env.production` - Environment configuration
- `vue.config.js` - Build configuration (temporary)
- `src/store/modules/user.js` - User store settings
- `src/views/IndexView.vue` - Login logic
- `.env.development` - Development settings

## Troubleshooting

### Common Issues

1. **Node.js not found:**
   - Install Node.js from https://nodejs.org/
   - Ensure it's added to system PATH

2. **npm install fails:**
   - Try deleting `node_modules` folder
   - Run `npm install --legacy-peer-deps`

3. **Build fails:**
   - Check for syntax errors in code
   - Ensure all dependencies are installed
   - Check available disk space

4. **Port 8080 in use:**
   - Stop other development servers
   - Or modify port in `vue.config.js`

### Error Recovery

If scripts fail, restore from backups:
```cmd
copy .env.production.backup .env.production
copy vue.config.js.backup vue.config.js
copy src\store\modules\user.js.backup src\store\modules\user.js
copy src\views\IndexView.vue.backup src\views\IndexView.vue
copy .env.development.backup .env.development
```

## Security Notes

### Production Builds
- OAuth login is automatically enabled
- Test user credentials are disabled
- Development bypasses are removed
- All security features are activated

### Development Builds
- Login bypass is enabled for convenience
- Test user credentials are active
- Not suitable for production deployment

## Build Output

### Location
All builds output to the `dist/` folder

### Contents
- `index.html` - Main application file
- `static/` - CSS, JS, and asset files
- `favicon.ico` - Application icon

### Deployment
Upload the entire contents of `dist/` folder to your web server.

## Script Features

### Error Handling
- Comprehensive error checking
- Automatic rollback on failure
- Clear error messages and solutions

### User Feedback
- Progress indicators
- Success/failure notifications
- Next steps guidance

### Automation
- Dependency installation
- Configuration management
- Backup creation and restoration

## Requirements

- Windows operating system
- Node.js (version 14 or higher recommended)
- npm (comes with Node.js)
- PowerShell (for advanced text processing)

## Support

If you encounter issues with these scripts:
1. Check the error messages for specific guidance
2. Verify all requirements are met
3. Try running scripts as administrator if permission issues occur
4. Check the backup files if restoration is needed
