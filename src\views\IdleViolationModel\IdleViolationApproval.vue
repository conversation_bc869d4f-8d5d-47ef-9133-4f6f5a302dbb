<template>
  <div class="container">
    <NavHeader ref="navHeader" title="闲置地违规审核" :back="true"></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.rule_name"
              name="content"
              label="地块名称："
              :readonly="true"
            />
            <van-field
              :value="infoData.content"
              name="content"
              label="违规内容:"
              :readonly="true"
            />
            <van-field
              v-for="(item, index) in infoData.attachments"
              :key="index"
              :name="item.attachType"
              :label="item.displayTitle"
              label-width="120px"
            >
              <template #input>
                <img
                  class="uploadImgsize"
                  :src="`http://113.87.160.8:47025/upload/${item.filePath}`"
                  alt=""
                />
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader
      left="附件查阅"
      v-if="infoData.attachments?.length !== 0"
    ></LabelHeader>
    <div class="information" v-if="infoData.attachments?.length !== 0">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              v-for="(item, index) in infoData.attachments"
              :key="index"
              :name="item.attachType"
              :label="item.displayTitle"
              label-width="120px"
            >
              <template #input>
                <img
                  class="uploadImgsize"
                  :src="`http://113.87.160.8:47025/upload/${item.filePath}`"
                  alt=""
                />
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader left="审核情况"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="radio" label="审核情况">
              <template #input>
                <van-radio-group v-model="state" direction="horizontal">
                  <van-radio :name="true">确认审核</van-radio>
                  <van-radio :name="false">误报清除</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </van-form>
          <div class="btn-box">
            <yButton title="提交" :top150="true" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { addCheckH5, upload, deleteCheck } from "@/api/config";
export default {
  data() {
    return {
      form: {
        agentName: "",
        illegalDate: "",
        violationTime: "",
        deductionNum: "",
        loadAttachment: "",
        isAttachmentModify: false,
      },
      state: true,
      imgPatrolList: [],
      showCalendar: false,
      showCalendar2: false,
      showCalendar3: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    this.infoData = this.$route.params.data;
  },

  methods: {
    afterRead(fileObj, fileType) {
      const file = fileObj.file;
      const formData = new FormData();
      formData.append("file", file);
      upload(formData).then((res) => {
        if (res.data.state === "success") {
          this.form.imgPatrolList = res.data.data;
          var url = `http://113.87.160.8:47025/upload/${res.data.data}`;
          this.imgPatrolList.push(url);
          this.form.loadAttachment = `[{"displayTitle":"违规依据附件1","attachType":"onlineAlarmExamine","relatedId":null,"attachMark":1,"filePath":"${res.data.data}"}]`;
          this.$toast({
            message: "图片上传成功",
            duration: 2000,
          });
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.startTime = `${year}-${month}-${day}`;
      this.showCalendar = false;
    },
    onConfirm2(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.endTime = `${year}-${month}-${day}`;
      this.showCalendar2 = false;
    },
    onConfirm3(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      const second = String(date.getSeconds()).padStart(2, "0");
      this.form.illegalDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      this.showCalendar3 = false;
    },
    sumBit() {
      if (this.form.acceptedRemarks !== "" && this.state !== "") {
        var reqData = {
          alarmId: this.infoData.alarm_id,
          agentId: this.infoData.agent_id,
          agentName: this.infoData.agentName,
          illegalDate: this.getCurrentTime(),
          checkNormId: this.infoData?.check_norm_id,
          deductionNum: 0,
          alarmZtcId: this.infoData?.alarm_id,
          isAttachmentModify: false,
          loadAttachment: "",
          alarmType: this.infoData?.alarm_type,
          startTime: this.getCurrentTime(),
          endTime: this.getCurrentTime(),
          id: this.infoData?.id,
          truckId: this.infoData?.truck_id,
          agentCheckType: this?.infoData.agent_check_type,
        };
        if (this.state) {
          this.addck(reqData);
        } else {
          this.deleteCk(reqData);
        }
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始
      const day = String(now.getDate()).padStart(2, "0");
      const hour = String(now.getHours()).padStart(2, "0");
      const minute = String(now.getMinutes()).padStart(2, "0");
      const second = String(now.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    addck(reqData) {
      addCheckH5(reqData).then((res) => {
        if (res.data.state === "success") {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
          this.$goback();
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    deleteCk(reqData) {
      deleteCheck(reqData).then((res) => {
        if (res.data.state === "success") {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
          this.$goback();
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
</style>
