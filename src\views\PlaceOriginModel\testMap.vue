<template>
  <div>
    <NavHeader ref="navHeader" title="电子围栏" :back="true"></NavHeader>
    <div id="container"></div>
    <div class="flushMarkContent">
      <div class="mapContent">
        <div class="mainMapTest">
          <div class="mainTest flex-row">
            <div class="mainTestBtn" @click="clearAll">清除围栏</div>
            <div class="mainTestBtn" @click="drawPolygon">确认围栏</div>
          </div>
        </div>
        <div class="btnClass flex-row">
          <div
            class="button"
            @click="save"
            style="background-color: #e6a23c; width: 100%"
          >
            保存
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
export default {
  name: "TMapClickMarker",
  data() {
    return {
      map: null,
      markerLayer: null,
      polygonLayer: null,
      points: [],
      canAddPoint: false, // 控制是否可以打点
      regionDef: "",
      // regionDef:
      //   "111.53217941893936,36.08954006817939;111.54140917818633,36.0895357747582;111.5414063409049,36.077637047609315;111.53217658624217,36.077641339597015;111.53217941893936,36.08954006817939",
    };
  },
  mounted() {
    this.infoData = this.$route.params.data;
    if (this.infoData?.pageCode === "PlaceOriginApproval") {
      this.$store.dispatch("config/setActive", this.infoData);
    }
    if (this.infoData?.regionDef === "") {
      Dialog.alert({
        title: "提示",
        message: "缺少区域定义数据，无法继续。",
      }).then(() => {
        this.$router.go(-1); // 返回上一页
      });
      return;
    }
    var centerdata = this.calculateCenter(this.infoData.regionDef);
    this.regionDef = this.infoData.regionDef;
    this.initMap(centerdata);
  },
  methods: {
    initMap(e) {
      this.map = new TMap.Map("container", {
        center: new TMap.LatLng(e.centerLat, e.centerLng), // 设置为中间位置
        zoom: 11,
      });
      this.markerLayer = new TMap.MultiMarker({
        id: "marker-layer",
        map: this.map,
        geometries: [],
      });

      this.polygonLayer = new TMap.MultiPolygon({
        id: "polygon-layer",
        map: this.map,
        geometries: [],
        styles: {
          style_blue: new TMap.PolygonStyle({
            color: "#3f85ff",
            showBorder: true,
            borderColor: "#3777FF",
            borderWidth: 2,
          }),
        },
      });

      // 监听点击添加点
      this.map.on("click", this.handleMapClick);
      // 加载初始 polygon 区域
      this.loadRegionDef();
    },
    calculateCenter(regionDef) {
      console.log("regionDef", regionDef);
      // 拆分字符串为坐标数组
      const coordinates = regionDef.split(";");
      let totalLat = 0;
      let totalLng = 0;
      const numPoints = coordinates.length;
      // 遍历坐标，计算总经度和总纬度
      coordinates.forEach((coord) => {
        const [lng, lat] = coord.split(",").map(Number); // 分割并转换为数字
        totalLng += lng;
        totalLat += lat;
      });
      // 计算中心点的经纬度
      const centerLat = totalLat / numPoints;
      const centerLng = totalLng / numPoints;
      console.log("计算中心点:", centerLat, centerLng);
      return { centerLat, centerLng };
    },
    handleMapClick(evt) {
      if (!this.canAddPoint) return;
      const point = evt.latLng;
      this.points.push(point);
      this.markerLayer.setGeometries(this.points.map((p) => ({ position: p })));
    },
    drawPolygon() {
      if (this.points.length < 3) {
        alert("请至少添加 3 个点来绘制多边形！");
        return;
      }

      this.polygonLayer.setGeometries([
        {
          id: "user-draw",
          styleId: "style_blue",
          paths: [this.points],
        },
      ]);
      this.canAddPoint = false;
    },
    clearAll() {
      this.points = [];
      this.markerLayer.setGeometries([]);
      this.polygonLayer.setGeometries([]);
      this.canAddPoint = true;
    },
    save() {},
    loadRegionDef() {
      console.log("加载区域定义:", this.regionDef);
      const coords = this.regionDef.split(";").map((pair) => {
        const [lng, lat] = pair.split(",").map(Number);
        return new TMap.LatLng(lat, lng);
      });

      this.polygonLayer.setGeometries([
        {
          id: "init-region",
          styleId: "style_blue",
          paths: [coords],
        },
      ]);

      this.markerLayer.setGeometries(
        coords.map((p) => ({
          position: p,
        }))
      );

      // 禁止重新打点
      this.canAddPoint = false;
    },
  },
};
</script>

<style scoped lang="less">
#container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  position: relative;
}

.btn {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  padding: 8px 16px;
  background-color: #3777ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
}

.clear {
  left: 120px;
  background-color: #ff4d4f;
}

.btn:hover {
  opacity: 0.85;
}
.btn-box {
  position: relative;
  z-index: 9999999;
}

.mapContent {
  width: 86%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  padding: 15px;
  background-color: #fff;
  border-radius: 35px 35px 0 0;
  z-index: 2001;
}
.mainTestTitle {
  display: inline-block;
  margin-right: 15px;
  font-size: 16px;
  color: #666;
  line-height: 56px;
  text-align: right;
  height: 23px;
}

.mainTest {
  margin: 8px 0;
  justify-content: space-between;
}
.mainTestBtn {
  width: 45%;
  text-align: center;
  margin-left: 15px;
  background: #ff9602;
  color: #fff;
  border-radius: 30px;
  padding: 0px 10px;
  cursor: pointer;
  line-height: 36px;
  height: 36px;
  font-size: 16px;
  display: inline-block;
  margin-right: 10px;
}
.btnClass {
  margin-top: 10px;
  align-items: center;
  .button {
    color: #fff;
    height: 35px;
    line-height: 35px;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    display: inline-block;
  }
}
</style>
