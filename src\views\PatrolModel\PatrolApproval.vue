<template>
  <div class="container">
    <NavHeader ref="navHeader" title="案件审核" :back="true"></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.content"
              name="content"
              label="审核内容："
              :readonly="true"
            />

            <van-field
              :value="infoData?.violationLoation"
              name="content"
              label="违规时间："
              :readonly="true"
            />
            <van-field
              :value="infoData?.violationType"
              name="content"
              label="违规类型："
              :readonly="true"
            />
            <van-field
              :value="infoData?.violationLoation"
              name="content"
              label="管辖区："
              :readonly="true"
            />
            <!-- <van-field
              :value="infoData?.violationLoation"
              name="content"
              label="违规位置:"
              :readonly="true"
            /> -->
            <van-field
              :value="infoData?.createDate"
              name="content"
              label="创建日期:"
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader left="审核情况"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="applyState" label="审核：" label-width="120px">
              <template #input>
                <van-radio-group v-model="form.state" direction="horizontal">
                  <van-radio :name="1">属实</van-radio>
                  <van-radio :name="2">误报</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.acceptedRemarks"
              name="acceptedRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="审核备注"
              type="textarea"
              maxlength="100"
              placeholder="请输入备注"
              show-word-limit
            />
          </van-form>
          <div class="btn-box">
            <yButton title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { saveOrUpdateH5 } from "@/api/config";
export default {
  data() {
    return {
      form: {
        minusScore: 0,
        state: 0,
      },
      showCalendar: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    this.infoData = this.$route.params.data;
  },

  methods: {
    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.explorationData = `${year}-${month}-${day}`;
      this.showCalendar = false;
    },
    sumBit() {
      console.log(this.form);
      if (this.form.minusScore !== 0 && this.form.state !== "") {
        var reqData = {
          ...this.form,
          id: this.infoData.id,
        };
        saveOrUpdateH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
</style>
