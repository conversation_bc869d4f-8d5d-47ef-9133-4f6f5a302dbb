<template>
  <div class="home">
    <NavHeader
      ref="navHeader"
      title="违规报警数据列表"
      :back="true"
    ></NavHeader>

    <!-- 列表内容区域 -->
    <div class="information cell">
      <div class="indent-box">
        <StandardTransportViolationCard
          class="card"
          :indentData="item"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></StandardTransportViolationCard>
      </div>

      <!-- 空状态 -->
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { agentCheckRecordListH5 } from "@/api/config";
import StandardTransportViolationCard from "@/views/TransportViolationsModel/StandardTransportViolationCard.vue";
import nullState from "@/components/com/NullCop.vue";
import NavHeader from "@/components/com/NavHeader.vue";
export default {
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      loading: false,
      finished: false,
      option1: [
        {
          text: "请选择渠道",
          value: "",
        },
      ],
      value1: "请选择",
      navHeight: 0,
      formData: {
        checkState: 0,
        pageNum: 1,
        pageSize: 10,
        applyType: 1,
        applyState: 2,
        year: 2025,
        monthly: 7,
        baseType: "truck",
      },
    };
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    StandardTransportViolationCard,
    nullState,
    NavHeader,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    addValueAndText(dataArray) {
      return dataArray.map((item) => {
        return {
          ...item,
          value: item.dataCode,
          text: item.dataName,
        };
      });
    },
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSearch() {
      this.formData.page = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },
    getList() {
      if (this.loading) return;
      this.loading = true;
      this.$loadingU.show("请等待....", 5000);
      const reqData = {
        ...this.formData,
      };
      agentCheckRecordListH5(reqData)
        .then((res) => {
          console.log("获取列表数据：", res);
          if (res.data.success) {
            const newData = res.data.result;
            this.dataList = newData.list;
            console.log("获取到的数据：", this.dataList);
          } else {
            if (this.formData.page === 1) {
              this.dataList = [];
            }
            this.finished = true;
          }
          this.loading = false;
          this.$loadingU.hide();
        })
        .catch(() => {
          this.$loadingU.hide();
          this.loading = false;
        });
    },
    nextPage(item) {
      this.$router.push({ name: "eTjbxx", params: { id: item.etId } });
    },
  },
};
</script>

<style scoped lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px;

  .indent-box {
    padding-top: 16px;

    .card {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
}


</style>
