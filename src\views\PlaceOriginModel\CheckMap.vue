<template>
  <div>
    <NavHeader ref="navHeader" title="电子围栏" :back="true"></NavHeader>
    <div id="container"></div>
    <div class="flushMarkContent">
      <div class="mapContent">
        <div class="mainMapTest">
          <div class="mainTest flex-row">
            <div class="mainTestBtn" @click="clearAll">清除围栏</div>
            <div class="mainTestBtn" @click="drawPolygon">确认围栏</div>
          </div>
        </div>
        <div class="btnClass flex-row">
          <div
            class="button"
            @click="save"
            style="background-color: #e6a23c; width: 100%"
          >
            保存
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
import { updateStateH5 } from "@/api/config";
export default {
  name: "TMapClickMarker",
  data() {
    return {
      map: null,
      markerLayer: null,
      polygonLayer: null,
      points: [],
      infoData: "",
      canAddPoint: false, // 控制是否可以打点
      regionDef: "",
      // regionDef:
      //   "111.53217941893936,36.08954006817939;111.54140917818633,36.0895357747582;111.5414063409049,36.077637047609315;111.53217658624217,36.077641339597015;111.53217941893936,36.08954006817939",
    };
  },
  mounted() {
    console.log("=== CheckMap 组件 mounted ===");
    console.log("$route.params:", this.$route.params);
    console.log("$route.params.data:", this.$route.params.data);
    console.log("$route.params.data 类型:", typeof this.$route.params.data);

    this.infoData = this.$route.params.data;

    console.log("接收到的 infoData:", this.infoData);
    console.log("infoData 类型:", typeof this.infoData);
    console.log("infoData 是否为对象:", this.infoData && typeof this.infoData === 'object');

    // 详细检查 infoData
    if (this.infoData) {
      console.log("infoData.regionDef:", this.infoData.regionDef);
      console.log("regionDef 类型:", typeof this.infoData.regionDef);
      console.log("regionDef 值:", JSON.stringify(this.infoData.regionDef));
      console.log("infoData.pageCode:", this.infoData.pageCode);
      console.log("infoData.id:", this.infoData.id);
      console.log("infoData.engineeringName:", this.infoData.engineeringName);
    } else {
      console.error("infoData 为空或未定义！");
      Dialog.alert({
        title: "错误",
        message: "页面数据获取失败，请重试。",
      }).then(() => {
        this.$router.go(-1);
      });
      return;
    }

    if (this.infoData?.pageCode === "PlaceOriginApproval") {
      console.log("设置 store 状态");
      this.$store.dispatch("config/setActive", this.infoData);
    }

    // 检查 regionDef
    if (!this.infoData.regionDef) {
      console.error("regionDef 字段不存在或为 falsy 值");
      Dialog.alert({
        title: "提示",
        message: "缺少区域定义数据，无法继续。",
      }).then(() => {
        this.$router.go(-1); // 返回上一页
      });
      return;
    }

    if (this.infoData.regionDef === "") {
      console.error("regionDef 为空字符串");
      Dialog.alert({
        title: "提示",
        message: "缺少区域定义数据，无法继续。",
      }).then(() => {
        this.$router.go(-1); // 返回上一页
      });
      return;
    }

    console.log("开始计算中心点，regionDef:", this.infoData.regionDef);
    try {
      var centerdata = this.calculateCenter(this.infoData.regionDef);
      console.log("计算得到的中心点:", centerdata);

      this.regionDef = this.infoData.regionDef;
      console.log("设置 this.regionDef:", this.regionDef);

      console.log("开始初始化地图...");
      this.initMap(centerdata);
    } catch (error) {
      console.error("地图初始化失败:", error);
      Dialog.alert({
        title: "错误",
        message: "地图初始化失败：" + error.message,
      }).then(() => {
        this.$router.go(-1);
      });
    }
  },
  methods: {
    convertToRegionDef(coords) {
      if (!Array.isArray(coords) || coords.length === 0) return "";

      const pointStrings = coords.map((point) => `${point.lng},${point.lat}`);

      // 闭合区域（最后一个点与第一个点相同）
      pointStrings.push(pointStrings[0]);

      return pointStrings.join(";");
    },
    save() {
      var newReGionDef = this.convertToRegionDef(this.points);
      var reqData = {
        regionDef: newReGionDef,
        id: this.infoData.id,
      };
      updateStateH5(reqData)
        .then((res) => {
          console.log(res);
          if (res.data.success) {
            this.$toast.success("保存成功");
            this.$router.go(-1);
          } else {
            this.$toast.fail("保存失败，请稍后再试");
          }
        })
        .catch((error) => {
          console.error("保存失败:", error);
          this.$toast.fail("保存失败，请稍后再试");
        });
    },
    initMap(e) {
      console.log("=== initMap 开始 ===");
      console.log("传入的中心点参数:", e);
      console.log("centerLat:", e.centerLat);
      console.log("centerLng:", e.centerLng);

      // 检查 TMap 是否可用
      if (typeof TMap === 'undefined') {
        console.error("TMap 未定义，请检查腾讯地图 API 是否正确加载");
        throw new Error("腾讯地图 API 未加载");
      }

      console.log("TMap 对象:", TMap);
      console.log("TMap.Map:", TMap.Map);
      console.log("TMap.LatLng:", TMap.LatLng);

      try {
        console.log("创建地图实例...");
        this.map = new TMap.Map("container", {
          center: new TMap.LatLng(e.centerLat, e.centerLng), // 设置为中间位置
          zoom: 13,
        });
        console.log("地图实例创建成功:", this.map);

        console.log("创建标记图层...");
        this.markerLayer = new TMap.MultiMarker({
          id: "marker-layer",
          map: this.map,
          geometries: [],
        });
        console.log("标记图层创建成功:", this.markerLayer);

        console.log("创建多边形图层...");
        this.polygonLayer = new TMap.MultiPolygon({
          id: "polygon-layer",
          map: this.map,
          geometries: [],
          styles: {
            style_blue: new TMap.PolygonStyle({
              color: "#3f85ff",
              showBorder: true,
              borderColor: "#3777FF",
              borderWidth: 2,
            }),
          },
        });
        console.log("多边形图层创建成功:", this.polygonLayer);

        console.log("添加地图点击事件监听...");
        // 监听点击添加点
        this.map.on("click", this.handleMapClick);

        console.log("开始加载初始区域...");
        // 加载初始 polygon 区域
        this.loadRegionDef();

        console.log("=== initMap 完成 ===");
      } catch (error) {
        console.error("initMap 过程中发生错误:", error);
        throw error;
      }
    },
    calculateCenter(regionDef) {
      console.log("=== calculateCenter 开始 ===");
      console.log("输入的 regionDef:", regionDef);
      console.log("regionDef 类型:", typeof regionDef);
      console.log("regionDef 长度:", regionDef ? regionDef.length : 'undefined');

      if (!regionDef || typeof regionDef !== 'string') {
        console.error("regionDef 无效:", regionDef);
        throw new Error("regionDef 必须是有效的字符串");
      }

      // 拆分字符串为坐标数组
      const coordinates = regionDef.split(";");
      console.log("分割后的坐标数组:", coordinates);
      console.log("坐标点数量:", coordinates.length);

      if (coordinates.length === 0) {
        console.error("没有找到有效的坐标点");
        throw new Error("regionDef 中没有有效的坐标点");
      }

      let totalLat = 0;
      let totalLng = 0;
      const numPoints = coordinates.length;

      // 遍历坐标，计算总经度和总纬度
      coordinates.forEach((coord, index) => {
        console.log(`处理第 ${index + 1} 个坐标:`, coord);
        if (!coord || coord.trim() === '') {
          console.warn(`第 ${index + 1} 个坐标为空，跳过`);
          return;
        }

        const parts = coord.split(",");
        console.log(`坐标分割结果:`, parts);

        if (parts.length !== 2) {
          console.error(`第 ${index + 1} 个坐标格式错误:`, coord);
          return;
        }

        const [lng, lat] = parts.map(Number);
        console.log(`解析的经纬度: lng=${lng}, lat=${lat}`);

        if (isNaN(lng) || isNaN(lat)) {
          console.error(`第 ${index + 1} 个坐标包含非数字值: lng=${lng}, lat=${lat}`);
          return;
        }

        totalLng += lng;
        totalLat += lat;
      });

      // 计算中心点的经纬度
      const centerLat = totalLat / numPoints;
      const centerLng = totalLng / numPoints;

      console.log("总经度:", totalLng);
      console.log("总纬度:", totalLat);
      console.log("点数量:", numPoints);
      console.log("计算得到的中心点:", { centerLat, centerLng });

      if (isNaN(centerLat) || isNaN(centerLng)) {
        console.error("计算的中心点包含 NaN 值");
        throw new Error("无法计算有效的中心点");
      }

      console.log("=== calculateCenter 结束 ===");
      return { centerLat, centerLng };
    },
    handleMapClick(evt) {
      if (!this.canAddPoint) return;
      const point = evt.latLng;
      this.points.push(point);
      this.markerLayer.setGeometries(this.points.map((p) => ({ position: p })));
    },
    drawPolygon() {
      if (this.points.length < 3) {
        alert("请至少添加 3 个点来绘制多边形！");
        return;
      }

      this.polygonLayer.setGeometries([
        {
          id: "user-draw",
          styleId: "style_blue",
          paths: [this.points],
        },
      ]);
      this.canAddPoint = false;
    },
    clearAll() {
      this.points = [];
      this.markerLayer.setGeometries([]);
      this.polygonLayer.setGeometries([]);
      this.canAddPoint = true;
    },
    loadRegionDef() {
      console.log("=== loadRegionDef 开始 ===");
      console.log("当前 this.regionDef:", this.regionDef);
      console.log("regionDef 类型:", typeof this.regionDef);
      console.log("regionDef 长度:", this.regionDef ? this.regionDef.length : 'undefined');

      if (!this.regionDef) {
        console.error("regionDef 为空，无法加载区域");
        return;
      }

      try {
        console.log("开始解析坐标...");
        const coordPairs = this.regionDef.split(";");
        console.log("分割后的坐标对:", coordPairs);

        const coords = coordPairs.map((pair, index) => {
          console.log(`处理第 ${index + 1} 个坐标对:`, pair);
          if (!pair || pair.trim() === '') {
            console.warn(`第 ${index + 1} 个坐标对为空，跳过`);
            return null;
          }

          const parts = pair.split(",");
          if (parts.length !== 2) {
            console.error(`第 ${index + 1} 个坐标对格式错误:`, pair);
            return null;
          }

          const [lng, lat] = parts.map(Number);
          console.log(`解析坐标: lng=${lng}, lat=${lat}`);

          if (isNaN(lng) || isNaN(lat)) {
            console.error(`第 ${index + 1} 个坐标包含非数字值:`, pair);
            return null;
          }

          const latLng = new TMap.LatLng(lat, lng);
          console.log(`创建 TMap.LatLng:`, latLng);
          return latLng;
        }).filter(coord => coord !== null);

        console.log("有效坐标数量:", coords.length);
        console.log("所有有效坐标:", coords);

        if (coords.length === 0) {
          console.error("没有有效的坐标点");
          return;
        }

        console.log("设置多边形几何体...");
        this.polygonLayer.setGeometries([
          {
            id: "init-region",
            styleId: "style_blue",
            paths: [coords],
          },
        ]);
        console.log("多边形设置完成");

        console.log("设置标记点...");
        this.markerLayer.setGeometries(
          coords.map((p, index) => {
            console.log(`设置第 ${index + 1} 个标记点:`, p);
            return {
              position: p,
            };
          })
        );
        console.log("标记点设置完成");

        // 禁止重新打点
        this.canAddPoint = false;
        console.log("=== loadRegionDef 完成 ===");
      } catch (error) {
        console.error("loadRegionDef 过程中发生错误:", error);
        throw error;
      }
    },
  },
};
</script>

<style scoped lang="less">
#container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  position: relative;
}

.btn {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  padding: 8px 16px;
  background-color: #3777ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
}

.clear {
  left: 120px;
  background-color: #ff4d4f;
}

.btn:hover {
  opacity: 0.85;
}
.btn-box {
  position: relative;
  z-index: 9999999;
}

.mapContent {
  width: 86%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  padding: 15px;
  background-color: #fff;
  border-radius: 35px 35px 0 0;
  z-index: 2001;
}
.mainTestTitle {
  display: inline-block;
  margin-right: 15px;
  font-size: 16px;
  color: #666;
  line-height: 56px;
  text-align: right;
  height: 23px;
}

.mainTest {
  margin: 8px 0;
  justify-content: space-between;
}
.mainTestBtn {
  width: 45%;
  text-align: center;
  margin-left: 15px;
  background: #ff9602;
  color: #fff;
  border-radius: 30px;
  padding: 0px 10px;
  cursor: pointer;
  line-height: 36px;
  height: 36px;
  font-size: 16px;
  display: inline-block;
  margin-right: 10px;
}
.btnClass {
  margin-top: 10px;
  align-items: center;
  .button {
    color: #fff;
    height: 35px;
    line-height: 35px;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    display: inline-block;
  }
}
</style>
