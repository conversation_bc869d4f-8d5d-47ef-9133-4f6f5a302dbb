<template>
  <div class="home">
    <!-- 顶部导航栏 -->
    <NavHeader ref="navHeader" :title="pageTitle" :back="true"></NavHeader>

    <!-- 标签页 -->
    <van-tabs v-model="active" @change="onTabChange" sticky>
      <van-tab
        v-for="(tab, index) in tabOptions"
        :key="index"
        :title="tab.text"
      >
      </van-tab>
    </van-tabs>

    <!-- 列表内容区域 -->
    <div class="information cell">
      <div class="indent-box">
        <StandardRegisterCard
          class="card"
          :indentData="item"
          :currentTabState="tabOptions[active].value"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></StandardRegisterCard>
      </div>
      
      <!-- 空状态 -->
      <div v-if="dataList.length === 0" class="center">
        <NullCop msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { sysUserTempListH5 } from "@/api/config";
import StandardRegisterCard from "@/views/HaulwayModel/StandardRegisterCard.vue";
import NullCop from "@/components/com/NullCop.vue";
import NavHeader from "@/components/com/NavHeader.vue";

export default {
  name: "HaulwayTransportRegisterList",
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: 0, // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: 1 }, // typeState: 1
        { text: "已完成", value: 2 }, // typeState: 2
      ],
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        isDeleteNew: 0, // 运输单位注册审核
        typeState: 1, // 默认为待审核
      },
    };
  },
  computed: {
    pageTitle() {
      return "运输单位注册审核";
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200);
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    StandardRegisterCard,
    NullCop,
    NavHeader,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    // 标签页切换
    onTabChange(index) {
      this.active = index;
      this.formData.typeState = this.tabOptions[index].value;
      this.formData.pageNum = 1;
      this.dataList = [];
      this.getList();
    },

    // 滚动加载
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },

    // 获取列表数据
    async getList() {
      try {
        console.log("请求参数:", this.formData);
        const res = await sysUserTempListH5(this.formData);
        console.log("接口返回:", res);

        if (res.data.success || res.data.state === "success") {
          const newData = res.data.result || {};
          if (this.formData.pageNum === 1) {
            this.dataList = newData.list || [];
          } else {
            this.dataList = [...this.dataList, ...(newData.list || [])];
          }
          
          this.total = res.data.total || 0;
          this.loading = false;
          this.finished = (newData.list || []).length === 0;
        } else {
          this.$toast.fail(res.data.message || "获取数据失败");
          this.loading = false;
          this.finished = true;
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$toast.fail("获取数据失败");
        this.loading = false;
        this.finished = true;
      }
    },
  },
};
</script>

<style scoped lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px;
  
  .indent-box {
    padding-top: 16px;
    
    .card {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
}
</style>
