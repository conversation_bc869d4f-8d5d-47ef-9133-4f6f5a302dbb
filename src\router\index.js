import Vue from 'vue'
import VueRouter from 'vue-router'
import IndexView from '../views/IndexView'
import tipsShow from '../views/tipsShow.vue'
import PlaceOriginList from '../views/PlaceOriginModel/PlaceOriginList.vue'
import PlaceOriginFinalList from '../views/PlaceOriginModel/PlaceOriginFinalList.vue'
import PlaceOriginRegisterList from '../views/PlaceOriginModel/PlaceOriginRegisterList.vue'
import PlaceOriginRegisterApproval from '../views/PlaceOriginModel/PlaceOriginRegisterApproval.vue'
import PlaceOriginFunc from '../views/PlaceOriginModel/PlaceOriginFunc.vue'
import PlaceOriginApproval from '../views/PlaceOriginModel/PlaceOriginApproval.vue'
import PlaceOriginFinalApproval from '../views/PlaceOriginModel/PlaceOriginFinalApproval.vue'
import CheckMap from '../views/PlaceOriginModel/CheckMap.vue'
import testMap from '../views/PlaceOriginModel/testMap.vue'
import ProductionAuditList from '../views/ProductionAuditModel/ProductionAuditList.vue'
import ProductionYanqiList from '../views/ProductionAuditModel/ProductionYanqiList.vue'
import ProductioYanqiApproval from '../views/ProductionAuditModel/ProductioYanqiApproval.vue'
import ProductionExtensionApproval from '../views/ProductionAuditModel/ProductionExtensionApproval.vue'
import ProductionTransportList from '../views/ProductionAuditModel/ProductionTransportList.vue'
import ProductionDisposalList from '../views/ProductionAuditModel/ProductionDisposalList.vue'
import ProductionWasteList from '../views/ProductionAuditModel/ProductionWasteList.vue'
import ProductionWasteApproval from '../views/ProductionAuditModel/ProductionWasteApproval.vue'
import ProductionCycleList from '../views/ProductionAuditModel/ProductionCycleList.vue'
import ProductionTransportChangeApproval from '../views/ProductionAuditModel/ProductionTransportChangeApproval.vue'
import ProductionDisposalChangeApproval from '../views/ProductionAuditModel/ProductionDisposalChangeApproval.vue'
import ProductionStopApproval from '../views/ProductionAuditModel/ProductionStopApproval.vue'
import ProductionStopList from '../views/ProductionAuditModel/ProductionStopList.vue'
import ProductionCycleChangeApproval from '../views/ProductionAuditModel/ProductionCycleChangeApproval.vue'
import DisposalStopApproval from '../views/DisposalPropertyModel/DisposalStopApproval.vue'
import TransportCompanyChangeApproval from '../views/HaulwayModel/TransportCompanyChangeApproval.vue'
import DisposalUnitChangeApproval from '../views/DisposalPropertyModel/DisposalUnitChangeApproval.vue'
import DisposalCapabilityChangeApproval from '../views/DisposalPropertyModel/DisposalCapabilityChangeApproval.vue'
import DisposalExtensionList from '../views/DisposalPropertyModel/DisposalExtensionList.vue'
import DisposalExtensionApproval from '../views/DisposalPropertyModel/DisposalExtensionApproval.vue'
import DisposalCompanyList from '../views/DisposalPropertyModel/DisposalCompanyList.vue'
import DisposalCompanyApproval from '../views/DisposalPropertyModel/DisposalCompanyApproval.vue'
import DisposalCapacityList from '../views/DisposalPropertyModel/DisposalCapacityList.vue'
import DisposalCapacityApproval from '../views/DisposalPropertyModel/DisposalCapacityApproval.vue'
import haulwayList from '../views/HaulwayModel/haulwayList.vue'
import haulwayApproval from '../views/HaulwayModel/haulwayApproval.vue'
import HaulwayFunc from '../views/HaulwayModel/HaulwayFunc.vue'
import TransportApprovalList from '../views/HaulwayModel/TransportApprovalList.vue'
import TransportFinalApprovalList from '../views/HaulwayModel/TransportFinalApprovalList.vue'
import TransportApproval from '../views/HaulwayModel/TransportApproval.vue'
import TransportFinalApproval from '../views/HaulwayModel/TransportFinalApproval.vue'
import TransportExtensionList from '../views/HaulwayModel/TransportExtensionList.vue'
import TransportExtensionApproval from '../views/HaulwayModel/TransportExtensionApproval.vue'
import TransportCompanyList from '../views/HaulwayModel/TransportCompanyList.vue'
import TransportCompanyApproval from '../views/HaulwayModel/TransportCompanyApproval.vue'
import TransportVehicleList from '../views/HaulwayModel/TransportVehicleList.vue'
import TransportVehicleApproval from '../views/HaulwayModel/TransportVehicleApproval.vue'
import PatrolList from '../views/PatrolModel/PatrolList.vue'
import PatrolAdd from '../views/PatrolModel/PatrolAdd.vue'
import PatrolApproval from '../views/PatrolModel/PatrolApproval.vue'
import TransportViolationsList from '../views/TransportViolationsModel/TransportViolationsList.vue'
import TransportViolationsApproval from '../views/TransportViolationsModel/TransportViolationsApproval.vue'
import IdleViolationList from '../views/IdleViolationModel/IdleViolationList.vue'
import IdleViolationApproval from '../views/IdleViolationModel/IdleViolationApproval.vue'
import DisposalPropertyList from '../views/DisposalPropertyModel/DisposalPropertyList.vue'
import DisposalPropertyApproval from '../views/DisposalPropertyModel/DisposalPropertyApproval.vue'
import DisposalPropertyFunc from '../views/DisposalPropertyModel/DisposalPropertyFunc.vue'
import DisposalPropertyFinalList from '../views/DisposalPropertyModel/DisposalPropertyFinalList.vue'
import DisposalPropertyFinalApproval from '../views/DisposalPropertyModel/DisposalPropertyFinalApproval.vue'
import DisposalPropertyRegisterList from '../views/DisposalPropertyModel/DisposalPropertyRegisterList.vue'
import DisposalPropertyRegisterApproval from '../views/DisposalPropertyModel/DisposalPropertyRegisterApproval.vue'
import DisposalPropertyBaotingList from '../views/DisposalPropertyModel/DisposalPropertyBaotingList.vue'
import HaulwayTransportRegisterList from '../views/HaulwayModel/HaulwayTransportRegisterList.vue'
import HaulwayTransportRegisterApproval from '../views/HaulwayModel/HaulwayTransportRegisterApproval.vue'
import Register from '../views/RegisterModel/RegisterApproval.vue'
Vue.use(VueRouter)
const routes = [
  {
    path: '/',
    name: 'IndexView',
    component: IndexView,
  },
  {
    path: '/tipsShow',
    name: 'tipsShow',
    component: tipsShow,
  },
  {
    path: '/PlaceOrigin/func',
    name: 'PlaceOriginFunc',
    component: PlaceOriginFunc,
  },
  {
    path: '/PlaceOrigin/list',
    name: 'PlaceOriginList',
    component: PlaceOriginList,
  },
  {
    path: '/PlaceOrigin/final/list',
    name: 'PlaceOriginFinalList',
    component: PlaceOriginFinalList,
  },
  {
    path: '/PlaceOrigin/register/list',
    name: 'PlaceOriginRegisterList',
    component: PlaceOriginRegisterList,
  },
  {
    path: '/PlaceOrigin/map/:data',
    name: 'CheckMap',
    component: CheckMap,
  },
  {
    path: '/PlaceOrigin/testmap',
    name: 'testMap',
    component: testMap,
  },
  {
    path: '/PlaceOrigin/approval/:data',
    name: 'PlaceOriginApproval',
    component: PlaceOriginApproval,
  },
  {
    path: '/PlaceOrigin/final/approval/:data',
    name: 'PlaceOriginFinalApproval',
    component: PlaceOriginFinalApproval,
  },
  {
    path: '/PlaceOrigin/register/approval/:data',
    name: 'PlaceOriginRegisterApproval',
    component: PlaceOriginRegisterApproval,
  },
  {
    path: '/register/approval/:data',
    name: 'Register',
    component: Register,
  },
  {
    path: '/ProductionAudit/list',
    name: 'ProductionAuditList',
    component: ProductionAuditList,
  },
  {
    path: '/ProductionAudit/yanqi/list',
    name: 'ProductionYanqiList',
    component: ProductionYanqiList,
  },
  {
    path: '/ProductionAudit/yanqi/approval/:data',
    name: 'ProductioYanqiApproval',
    component: ProductioYanqiApproval,
  },
  {
    path: '/ProductionAudit/extension/approval',
    name: 'ProductionExtensionApproval',
    component: ProductionExtensionApproval,
  },
  {
    path: '/ProductionAudit/transport/list',
    name: 'ProductionTransportList',
    component: ProductionTransportList,
  },
  {
    path: '/ProductionAudit/disposal/list',
    name: 'ProductionDisposalList',
    component: ProductionDisposalList,
  },
  {
    path: '/ProductionAudit/waste/list',
    name: 'ProductionWasteList',
    component: ProductionWasteList,
  },
  {
    path: '/ProductionAudit/waste/approval/:data',
    name: 'ProductionWasteApproval',
    component: ProductionWasteApproval,
  },
  {
    path: '/ProductionAudit/cycle/list',
    name: 'ProductionCycleList',
    component: ProductionCycleList,
  },
  {
    path: '/ProductionAudit/transport/change/approval/:data',
    name: 'ProductionTransportChangeApproval',
    component: ProductionTransportChangeApproval,
  },
  {
    path: '/ProductionAudit/disposal/change/approval/:data',
    name: 'ProductionDisposalChangeApproval',
    component: ProductionDisposalChangeApproval,
  },
  {
    path: '/ProductionAudit/stop/list',
    name: 'ProductionStopList',
    component: ProductionStopList,
  },
  {
    path: '/ProductionAudit/stop/approval/:data',
    name: 'ProductionStopApproval',
    component: ProductionStopApproval,
  },
  {
    path: '/ProductionAudit/cycle/change/approval/:data',
    name: 'ProductionCycleChangeApproval',
    component: ProductionCycleChangeApproval,
  },
  {
    path: '/DisposalAudit/stop/approval/:data',
    name: 'DisposalStopApproval',
    component: DisposalStopApproval,
  },
  {
    path: '/TransportAudit/company/change/approval/:data',
    name: 'TransportCompanyChangeApproval',
    component: TransportCompanyChangeApproval,
  },
  {
    path: '/DisposalAudit/unit/change/approval/:data',
    name: 'DisposalUnitChangeApproval',
    component: DisposalUnitChangeApproval,
  },
  {
    path: '/DisposalAudit/capability/change/approval/:data',
    name: 'DisposalCapabilityChangeApproval',
    component: DisposalCapabilityChangeApproval,
  },
  {
    path: '/DisposalAudit/extension/list',
    name: 'DisposalExtensionList',
    component: DisposalExtensionList,
  },
  {
    path: '/DisposalAudit/extension/approval/:data',
    name: 'DisposalExtensionApproval',
    component: DisposalExtensionApproval,
  },
  {
    path: '/DisposalAudit/company/list',
    name: 'DisposalCompanyList',
    component: DisposalCompanyList,
  },
  {
    path: '/DisposalAudit/company/approval/:data',
    name: 'DisposalCompanyApproval',
    component: DisposalCompanyApproval,
  },
  {
    path: '/DisposalAudit/capacity/list',
    name: 'DisposalCapacityList',
    component: DisposalCapacityList,
  },
  {
    path: '/DisposalAudit/capacity/approval/:data',
    name: 'DisposalCapacityApproval',
    component: DisposalCapacityApproval,
  },
  {
    path: '/HaulwayModel/func',
    name: 'HaulwayFunc',
    component: HaulwayFunc,
  },
  {
    path: '/HaulwayModel/list',
    name: 'haulwayList',
    component: haulwayList,
  },
  {
    path: '/HaulwayModel/approval/:data',
    name: 'haulwayApproval',
    component: haulwayApproval,
  },
  {
    path: '/HaulwayModel/transport-approval',
    name: 'TransportApprovalList',
    component: TransportApprovalList,
  },
  {
    path: '/HaulwayModel/transport-final-approval',
    name: 'TransportFinalApprovalList',
    component: TransportFinalApprovalList,
  },
  {
    path: '/HaulwayModel/transport/approval/:data',
    name: 'TransportApproval',
    component: TransportApproval,
  },
  {
    path: '/HaulwayModel/transport/final/approval/:data',
    name: 'TransportFinalApproval',
    component: TransportFinalApproval,
  },
  {
    path: '/HaulwayModel/transport-extension/list',
    name: 'TransportExtensionList',
    component: TransportExtensionList,
  },
  {
    path: '/HaulwayModel/transport-extension/approval/:data',
    name: 'TransportExtensionApproval',
    component: TransportExtensionApproval,
  },
  {
    path: '/HaulwayModel/transport-company/list',
    name: 'TransportCompanyList',
    component: TransportCompanyList,
  },
  {
    path: '/HaulwayModel/transport-company/approval/:data',
    name: 'TransportCompanyApproval',
    component: TransportCompanyApproval,
  },
  {
    path: '/HaulwayModel/transport-vehicle/list',
    name: 'TransportVehicleList',
    component: TransportVehicleList,
  },
  {
    path: '/HaulwayModel/transport-vehicle/approval/:data',
    name: 'TransportVehicleApproval',
    component: TransportVehicleApproval,
  },
  {
    path: '/HaulwayModel/transport-register/list',
    name: 'HaulwayTransportRegisterList',
    component: HaulwayTransportRegisterList,
  },
  {
    path: '/HaulwayModel/transport-register/approval/:data',
    name: 'HaulwayTransportRegisterApproval',
    component: HaulwayTransportRegisterApproval,
  },
  {
    path: '/PatrolModel/list',
    name: 'PatrolList',
    component: PatrolList,
  },
  {
    path: '/PatrolModel/add',
    name: 'PatrolAdd',
    component: PatrolAdd,
  },
  {
    path: '/PatrolModel/approval/:data',
    name: 'PatrolApproval',
    component: PatrolApproval,
  },
  {
    path: '/TransportViolationsModel/list',
    name: 'TransportViolationsList',
    component: TransportViolationsList,
  },
  {
    path: '/TransportViolationsModel/approval/:data',
    name: 'TransportViolationsApproval',
    component: TransportViolationsApproval,
  },
  {
    path: '/IdleViolationModel/list',
    name: 'IdleViolationList',
    component: IdleViolationList,
  },
  {
    path: '/IdleViolationModel/approval/:data',
    name: 'IdleViolationApproval',
    component: IdleViolationApproval,
  },
  {
    path: '/DisposalPropertyModel/list',
    name: 'DisposalPropertyList',
    component: DisposalPropertyList,
  },
  {
    path: '/DisposalPropertyModel/approval/:data',
    name: 'DisposalPropertyApproval',
    component: DisposalPropertyApproval,
  },
  {
    path: '/DisposalPropertyModel/register/list',
    name: 'DisposalPropertyRegisterList',
    component: DisposalPropertyRegisterList,
  },
  {
    path: '/DisposalPropertyModel/register/approval/:data',
    name: 'DisposalPropertyRegisterApproval',
    component: DisposalPropertyRegisterApproval,
  },
  {
    path: '/DisposalPropertyModel/baoting/approval/:data',
    name: 'DisposalPropertyBaotingApproval',
    component: () => import('@/views/DisposalPropertyModel/baoting/approval.vue'),
  },
  {
    path: '/DisposalPropertyModel/baoting/list',
    name: 'DisposalPropertyBaotingList',
    component: DisposalPropertyBaotingList,
  },
  {
    path: '/DisposalPropertyModel/func',
    name: 'DisposalPropertyFunc',
    component: DisposalPropertyFunc,
  },
  {
    path: '/DisposalPropertyModel/final/list',
    name: 'DisposalPropertyFinalList',
    component: DisposalPropertyFinalList,
  },
  {
    path: '/DisposalPropertyModel/final/approval/:data',
    name: 'DisposalPropertyFinalApproval',
    component: DisposalPropertyFinalApproval,
  },
]
const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})
// 不需要token的白名单
router.beforeEach((to, from, next) => {
  // const publicPages = ['/', '/register', '/email',"/emailadd", '/resetPwd', '/tel', '/configresetpwd', '/login','/findpwd'];
  // const authRequired = !publicPages.includes(to.path);
  // const loggedIn = localStorage.getItem('TOKEN');
  // if (authRequired && !loggedIn) {
  //   return next('/');
  // }
    // 从 window.location.hash 中提取 ?code=xxx
    // const hash = window.location.hash; // "#/?code=03fb..."
    // const queryString = hash.split('?')[1] || '';
    // const hashParams = new URLSearchParams(queryString);
    // const code = hashParams.get("code");
    // 首页但没有 code 参数，就跳转 tipsshow
    // if (to.path === '/' && !code) {
    //   return next('/tipsshow');
    // }
  next(); 
});

export default router
