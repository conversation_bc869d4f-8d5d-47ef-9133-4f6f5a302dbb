<template>
  <div class="app-container">
    <div class="flex-spacebetw">
      <el-button-group class="header-btn">
        <el-upload
          :multiple="false"
          accept=".pdf"
          action="#"
          :http-request="httpRequest"
          :show-file-list="false"
        >
          <el-button size="mini" type="primary">点击上传</el-button>
        </el-upload>
      </el-button-group>
      <div class="right_box">

      </div>
    </div>
    <el-table
      :data="listData"
      stripe
      empty-text="暂无数据"
      fit
      style="width: 100%"
      :header-cell-style="this.$headerCellColor"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="文件名" prop="fileName" align="center">
      </el-table-column>
      <el-table-column label="操作人" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.handleMan }}</span>
        </template>
      </el-table-column>
      <el-table-column label="pdfId">
        <template slot-scope="{ row }">
          <span>{{ row.pdfId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.regTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上传文件路径" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.upFilePath }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="400"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <div class="table-btn" @click="handleDel(row)">删除</div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="40%"
      :close-on-click-modal="false"
      :title="addOrEdit()"
      :visible.sync="dialogVisible"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <div class="item-box">
          <el-form-item label="用户名" prop="loginName">
            <el-input v-model="temp.userName" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="用户性别" prop="passWord">
            <el-input v-model="temp.userSex" placeholder="请输入用户性别" />
          </el-form-item>
        </div>
        <div class="item-box">
          <el-form-item label="用户电话" prop="userName">
            <el-input v-model="temp.phoneNumber" placeholder="请输入用户电话" />
          </el-form-item>
          <el-form-item label="住址" prop="mobileNo">
            <el-input v-model="temp.homeAddr" placeholder="请输入住址" />
          </el-form-item>
        </div>
        <div class="item-box">
          <el-form-item label="身份证" prop="userName">
            <el-input v-model="temp.idCode" placeholder="请输入身份证" />
          </el-form-item>
          <div></div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'add' ? addConfig() : editConfig()"
          >确认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  addPdfFile,
  removePdfFile,
  getPdfFileList,
  base64Pdf,
} from "@/pages/ycxaAdmin/api";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

export default {
  name: "Xtyh",
  components: { Pagination },

  data() {
    return {
      temp: {},
      listData: [],
      qkey: "",
      file: "", //预览文件
      dialogStatus: "add",
      dialogVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //覆盖默认的上传行为，可以自定义上传的实现
    httpRequest(data) {
      // 调用转方法base64
      this.getBase64(data.file)
        .then((resBase64) => {
          //获取文件，不带data:application/pdf;base64, 前缀
          this.file = resBase64.split(",")[1];

          //获取文件名称
          this.fileName = data.file.name;
          //调用上传接口
          this.upload();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    upload() {
      var reqData = {
        base64Str: this.file,
        busiFolder: "test",
        fileName: this.fileName,
      };
      base64Pdf(reqData).then((res) => {
        if (res.data.state !== -1) {
          var reqData2 = {
            upFilePath: res.data.url,
            fileName: this.fileName,
          };
          addPdfFile(reqData2).then((e) => {
            if (e.data.state === 0) {
              this.$notify({
                type: "success",
                title: "tips",
                message: e.data.message,
                duration: 2000,
              });
              this.getList();
            } else {
              this.$notify({
                type: "error",
                title: "tips",
                message: e.data.message,
                duration: 2000,
              });
            }
          });
        } else {
          this.$notify({
            type: "error",
            title: "tips",
            message: res.data.ynMsg,
            duration: 2000,
          });
        }
      });
    },

    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        let fileResult = "";
        reader.readAsDataURL(file);
        // 开始转码
        reader.onload = () => {
          fileResult = reader.result;
        };
        // 转码失败
        reader.onerror = (error) => {
          reject(error);
        };
        // 转码结束
        reader.onloadend = () => {
          resolve(fileResult);
        };
      });
    },
    handleEdit(row) {
      this.temp = row;
      this.dialogStatus = "edit";
      this.dialogVisible = true;
    },
    addOrEdit() {
      if ((this.dialogStatus = "add")) {
        return "新建用户";
      }
      if ((this.dialogStatus = "edit")) {
        return "编辑";
      }
    },
    handleCreate() {
      this.temp = {};
      this.dialogStatus = "add";
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.temp = {};
    },
    addConfig() {
      var reqData = {
        ...this.temp,
      };
      addPersonnelFiles(reqData).then((res) => {
        if (res.data.state === 0) {
          this.$notify({
            type: "success",
            title: "tips",
            message: res.data.message,
            duration: 2000,
          });
          this.getList();
          this.closeDialog();
        } else {
          this.$notify({
            type: "error",
            title: "tips",
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    editConfig() {
      var reqData = {
        ...this.temp,
      };
      updatePersonnelFiles(reqData).then((res) => {
        if (res.data.state === 0) {
          this.$notify({
            type: "success",
            title: "tips",
            message: res.data.message,
            duration: 2000,
          });
          this.getList();
          this.closeDialog();
        } else {
          this.$notify({
            type: "error",
            title: "tips",
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    handleDel(row) {
      this.$confirm("此操作将删除该条数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const param = {
          pdfId: row.pdfId,
        };
        removePdfFile(param).then((res) => {
          if (res.data.state === 0) {
            this.$notify({
              type: "success",
              title: "tips",
              message: res.data.message,
              duration: 2000,
            });
            this.getList();
          } else {
            this.$notify({
              type: "error",
              title: "tips",
              message: res.data.message,
              duration: 2000,
            });
          }
        });
      });
    },
    getList() {
      getPdfFileList().then((res) => {
        if (res.data.state === 0) {
          this.listData = res.data.data;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
@import "../../assets/css/table.css";

.item-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .el-form-item {
    width: 45%;
  }
}
.right_box {
  width: 300px;
}
.flex-spacebetw {
  display: flex;
  justify-content: space-between;
}
</style>
