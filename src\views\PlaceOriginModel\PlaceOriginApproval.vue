<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="isViewMode ? '产生地审核详情' : '产生地审核'" :back="true"></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.engineeringName"
              name="content"
              label="产生地名称："
              :readonly="true"
            />
            <!-- 
            <van-field
              :value="infoData?.agent?.agentName"
              name="content"
              label="处置地名称："
              :readonly="true"
            />-->
            <van-field
              :value="infoData?.coordinateInfo"
              name="content"
              label="产生地地址："
              :readonly="true"
            >
              <template #button>
                <van-button
                  size="mini"
                  type="primary"
                  @click="goMap"
                  icon="location-o"
                  round
                  class="map-btn"
                >
                  地图
                </van-button>
              </template>
            </van-field>
            <van-field
              :value="infoData.moreGarbageTypeListStr"
              name="content"
              label="建筑垃圾类型："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="
                (infoData?.beginValidityTime || '') +
                ' 至 ' +
                (infoData?.endValidityTime || '')
              "
              name="content"
              label="有效期："
              :readonly="true"
            />
             <!-- 
            <van-field
              :value="`${infoData?.allCapacity ?? ''} 方`"
              name="content"
              label="申报容量："
              :readonly="true"
            />-->
            <van-field
              :value="infoData?.addressDistrict?.fullAreaName"
              name="content"
              label="所属地区:"
              :readonly="true"
            />
            <van-field
              :value="infoData?.administrativeApplyAgent?.agentName"
              name="content"
              label="单位名称："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.mainCarrierUnitAgent?.agentName || '-'"
              name="content"
              label="主运单位："
              :readonly="true"
            />
            <van-field
              :value="getJointTransportUnits()"
              name="content"
              label="联运单位："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section">
      <LabelHeader left="申请材料"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <!-- 按固定分类显示申请材料 -->
            <div class="categorized-attachments">
              <!-- 行政许可申请书 -->
              <div class="attachment-category">
                <div class="category-title">行政许可申请书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs1')"
                    :key="'applyImgs1-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs1').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 营业执照或法人证书 -->
              <div class="attachment-category">
                <div class="category-title">营业执照或法人证书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs2')"
                    :key="'applyImgs2-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs2').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 授权委托书 -->
              <div class="attachment-category">
                <div class="category-title">授权委托书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs3')"
                    :key="'applyImgs3-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs3').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 信用承诺书 -->
              <div class="attachment-category">
                <div class="category-title">信用承诺书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs4')"
                    :key="'applyImgs4-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs4').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 运输合同 -->
              <div class="attachment-category">
                <div class="category-title">运输合同</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs5')"
                    :key="'applyImgs5-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs5').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 处置合同 -->
              <div class="attachment-category">
                <div class="category-title">处置合同</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs6')"
                    :key="'applyImgs6-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs6').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 建筑垃圾产生信息表 -->
              <div class="attachment-category">
                <div class="category-title">建筑垃圾产生信息表</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs7')"
                    :key="'applyImgs7-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs7').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LabelHeader :left="isViewMode ? '审核结果' : '审核情况'"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 查看模式：显示审核结果 -->
            <template v-if="isViewMode">
              <van-field name="applyState" label="审核结果：" label-width="120px">
                <template #input>
                  <van-radio-group
                    :value="infoData.applyState"
                    direction="horizontal"
                    disabled
                  >
                    <van-radio :name="8">通过</van-radio>
                    <van-radio :name="3" style="margin-left: 20px"
                      >不通过
                    </van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                :value="getAuditRemarks(infoData)"
                name="auditRemarks"
                label="审核备注："
                label-width="120px"
                type="textarea"
                rows="3"
                :readonly="true"
              />
              <van-field
                :value="infoData.applyDate || ''"
                name="auditDate"
                label="审核时间："
                label-width="120px"
                :readonly="true"
              />
            </template>

            <!-- 编辑模式：审核操作 -->
            <template v-else>
              <van-field name="applyState" label="审核：" label-width="120px">
                <template #input>
                  <van-radio-group
                    v-model="form.applyState"
                    direction="horizontal"
                  >
                    <van-radio :name="8">通过</van-radio>
                    <van-radio :name="3" style="margin-left: 20px"
                      >不通过
                    </van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                v-model="form.ruralDevelopmentRemarks"
                name="ruralDevelopmentRemarks"
                label-width="120px"
                rows="3"
                autosize
                label="审核备注:"
                type="textarea"
                maxlength="100"
                placeholder="请输入备注"
                show-word-limit
              />
            </template>
          </van-form>
          <div class="btn-box" v-if="!isViewMode">
            <yButton :top150="true" title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { updateStateH5 } from "@/api/config";
import { FILE_BASE_URL } from "@/utils/globalConstants";
import { ImagePreview } from 'vant';

export default {
  data() {
    return {
      form: {
        explorationData: "",
        applyState: "",
        ruralDevelopmentRemarks: "",
      },

      showCalendar: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        ruralDevelopmentRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: "",
      value: "",

    };
  },
  computed: {
    ...mapState("config", ["pageData"]),
    // 判断是否为查看模式（已审核完成）
    isViewMode() {
      // 如果是从已完成tab进入，则为查看模式
      const fromCompletedTab = this.$route.query.from === 'completed';

      // 如果有typeState字段，使用typeState判断
      if (this.infoData && this.infoData.typeState !== undefined) {
        return fromCompletedTab || this.infoData.typeState === 17 || this.infoData.typeState === 18;
      }
      // 否则根据applyState判断：8-通过, 3-不通过 表示已审核完成
      const isCompletedStatus = this.infoData && (this.infoData.applyState === 8 || this.infoData.applyState === 3);
      return fromCompletedTab || isCompletedStatus;
    },
    hasAttachments() {
      // 总是显示申请材料部分，因为我们要显示固定的分类标题
      return true;
    },
  },
  created() {
    // this.init();
  },
  mounted() {
    console.log("=== PlaceOriginApproval mounted 开始 ===");
    window.removeEventListener("scroll", this.onScroll);

    console.log("$route.params:", this.$route.params);
    console.log("$route.params.data:", this.$route.params.data);
    console.log("$route.params.data 类型:", typeof this.$route.params.data);
    console.log("this.pageData:", this.pageData);

    try {
      if (this.pageData?.pageCode === "PlaceOriginApproval") {
        console.log("使用 pageData 作为数据源");
        this.infoData = this.pageData;
      } else {
        console.log("使用 $route.params.data 作为数据源");
        this.infoData = this.$route.params.data;
      }

      // 数据验证
      if (!this.infoData) {
        console.error("页面数据为空，请检查路由传参");
        console.error("pageData:", this.pageData);
        console.error("$route.params.data:", this.$route.params.data);
        this.$toast.fail("页面数据获取失败");
        return;
      }

      // 详细的调试信息
      console.log("=== 页面数据详情 ===");
      console.log("完整数据对象：", this.infoData);
      console.log("数据类型：", typeof this.infoData);
      console.log("是否为对象：", this.infoData && typeof this.infoData === 'object');
      console.log("对象键值：", Object.keys(this.infoData));

      // 关键字段检查
      console.log("=== 关键字段检查 ===");
      console.log("id：", this.infoData.id);
      console.log("engineeringName：", this.infoData.engineeringName);
      console.log("applyState：", this.infoData.applyState);
      console.log("typeState：", this.infoData.typeState);
      console.log("regionDef：", this.infoData.regionDef);
      console.log("regionDef 类型：", typeof this.infoData.regionDef);
      console.log("regionDef 长度：", this.infoData.regionDef ? this.infoData.regionDef.length : 'undefined');
      console.log("coordinateInfo：", this.infoData.coordinateInfo);
      console.log("content：", this.infoData.content);

      console.log("是否查看模式：", this.isViewMode);

      // 打印申请材料JSON数据
      console.log("=== 申请材料数据 ===");
      console.log("attachments 完整数据：", JSON.stringify(this.infoData.attachments, null, 2));
      if (this.infoData.attachments && this.infoData.attachments.length > 0) {
        this.infoData.attachments.forEach((item, index) => {
          console.log(`附件${index + 1}:`, {
            attachType: item.attachType,
            displayTitle: item.displayTitle,
            filePath: item.filePath
          });
        });
      }
      console.log("=== PlaceOriginApproval mounted 数据检查完成 ===");
    } catch (error) {
      console.error("页面初始化失败：", error);
      console.error("错误堆栈：", error.stack);
      this.$toast.fail("页面初始化失败");
    }

    // 如果数据中已有审核结果，设置到表单中（用于显示已选择的状态）
    if (this.infoData && this.infoData.applyState) {
      this.form.applyState = this.infoData.applyState;
      console.log("设置表单applyState：", this.infoData.applyState);
    }

    // 如果数据中已有审核备注，设置到表单中
    if (this.infoData) {
      const remarks = this.infoData.ruralDevelopmentRemarks || this.infoData.acceptedRemarks;
      if (remarks) {
        this.form.ruralDevelopmentRemarks = remarks;
        console.log("设置表单备注：", remarks);
      }
    }
  },
  beforeDestroy() {
    this.$store.commit("config/clear");
    // 页面销毁前执行，比如移除事件、清除定时器
    console.log("页面即将退出");
  },
  methods: {
    // 获取联运单位列表
    getJointTransportUnits() {
      if (!this.infoData || !this.infoData.uuitApplyAgents || this.infoData.uuitApplyAgents.length === 0) {
        return '-';
      }

      // 提取所有联运单位的名称
      const unitNames = this.infoData.uuitApplyAgents
        .filter(agent => agent.agentName) // 过滤掉没有名称的
        .map(agent => agent.agentName);

      return unitNames.length > 0 ? unitNames.join('、') : '-';
    },

    // 根据attachType获取对应的附件
    getAttachmentsByType(attachType) {
      if (!this.infoData || !this.infoData.attachments) {
        return [];
      }

      // 只返回指定类型且有实际文件路径的附件
      return this.infoData.attachments.filter(item =>
        item.attachType === attachType &&
        item.filePath &&
        item.filePath.trim() !== '' &&
        item.filePath !== null &&
        item.filePath !== undefined
      );
    },

    // 获取审核结果文本
    getAuditResultText(applyState) {
      switch (applyState) {
        case 8:
          return '通过';
        case 3:
          return '不通过';
        default:
          return '未知';
      }
    },

    // 获取审核备注（兼容不同字段名）
    getAuditRemarks(data) {
      return data.ruralDevelopmentRemarks || data.acceptedRemarks || '无';
    },

    // 文件预览相关方法
    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      return `${FILE_BASE_URL}${filePath}`;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PPT演示';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        // 图片预览 - 使用直接导入的 ImagePreview 组件
        ImagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.explorationData = `${year}-${month}-${day}`;
      this.showCalendar = false;
    },
    goMap(e) {
      console.log("=== 点击查看地图按钮 ===");
      console.log("事件参数 e:", e);
      console.log("当前 this.infoData:", this.infoData);
      console.log("this.infoData 类型:", typeof this.infoData);
      console.log("this.infoData 是否为对象:", this.infoData && typeof this.infoData === 'object');

      // 检查关键字段
      if (this.infoData) {
        console.log("regionDef 字段:", this.infoData.regionDef);
        console.log("regionDef 类型:", typeof this.infoData.regionDef);
        console.log("regionDef 长度:", this.infoData.regionDef ? this.infoData.regionDef.length : 'undefined');
        console.log("id 字段:", this.infoData.id);
        console.log("engineeringName 字段:", this.infoData.engineeringName);
      } else {
        console.error("this.infoData 为空或未定义！");
        this.$toast.fail("数据获取失败，无法查看地图");
        return;
      }

      var infoData = {
        ...this.infoData,
        pageCode: "PlaceOriginApproval",
      };

      console.log("准备传递给地图的数据:", infoData);
      console.log("传递数据的 regionDef:", infoData.regionDef);

      // 验证 regionDef 是否存在且有效
      if (!infoData.regionDef) {
        console.error("regionDef 字段缺失或为空！");
        this.$toast.fail("缺少地图区域数据，无法查看地图");
        return;
      }

      if (typeof infoData.regionDef !== 'string' || infoData.regionDef.trim() === '') {
        console.error("regionDef 格式无效！", infoData.regionDef);
        this.$toast.fail("地图区域数据格式错误");
        return;
      }

      console.log("开始跳转到地图页面...");
      this.$router.push({ name: "CheckMap", params: { data: infoData } });
    },
    sumBit() {
      console.log("提交的数据：", this.form);
      if (this.form.ruralDevelopmentRemarks !== "" && this.form.applyState !== "") {
        // 获取当前日期，格式为 YYYY-MM-DD
        const today = new Date();
        const applyDate = today.getFullYear() + '-' +
          String(today.getMonth() + 1).padStart(2, '0') + '-' +
          String(today.getDate()).padStart(2, '0');

        var reqData = {
          id: this.infoData.id,
          applyState: this.form.applyState,
          applyDate: applyDate,
          ruralDevelopmentRemarks: this.form.ruralDevelopmentRemarks,
        };

        console.log("提交的请求数据：", reqData);

        updateStateH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 10px;
}

// 附件部分
.attachments-section {
  .categorized-attachments {
    .attachment-category {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
        padding: 0 4px;
      }

      .category-files {
        .attachment-row {
          margin-bottom: 12px;
          cursor: pointer;

          &:last-child {
            margin-bottom: 0;
          }

          // 图片行样式
          .image-row {
            img {
              width: 100%;
              max-height: 200px;
              object-fit: contain;
              border-radius: 8px;
              background: #f5f5f5;
              border: 1px solid #e6f4ff;
            }

            .attachment-title {
              font-size: 12px;
              color: #8c8c8c;
              text-align: center;
              margin-top: 6px;
              line-height: 18px;
            }
          }

          // 文件行样式
          .file-row {
            .file-info {
              display: flex;
              align-items: center;
              padding: 12px;
              background: #ffffff;
              border: 1px solid #e6f4ff;
              border-radius: 8px;
              transition: all 0.3s;

              &:hover {
                border-color: #1989fa;
                box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
              }

              .file-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8faff;
                border-radius: 6px;
                margin-right: 10px;
              }

              .file-details {
                flex: 1;

                .file-name {
                  font-size: 13px;
                  color: #262626;
                  font-weight: 500;
                  margin-bottom: 2px;
                  line-height: 18px;
                }

                .file-type {
                  font-size: 11px;
                  color: #8c8c8c;
                  line-height: 14px;
                }
              }

              .file-action {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .no-files {
          font-size: 12px;
          color: #8c8c8c;
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px dashed #d9d9d9;
        }
      }
    }
  }
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}

// 查看地图按钮样式
.map-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}
</style>
