<template>
  <div class="container">
    <div class="nav-header">
      <div class="nav-content">
        <van-icon name="arrow-left" size="20px" @click="$router.go(-1)" />
        <span class="nav-title">处置单位基本资料变更审核</span>
      </div>
    </div>

    <!-- 申请信息概览 -->
    <div class="overview-section">
      <div class="overview-card">
        <div class="overview-header">
          <div class="site-name">{{ infoData?.absorptionYard?.yardName || infoData?.administrativeApply?.engineeringName || '处置单位名称' }}</div>
          <div class="apply-time">{{ formatDateTime(infoData?.applyDate) }}</div>
        </div>
        <div class="overview-content">
          <div class="info-item">
            <span class="label">单位地址</span>
            <span class="value">{{ infoData?.absorptionYard?.coordinateInfo || infoData?.administrativeApply?.coordinateInfo || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">原单位信息</span>
            <span class="value">{{ infoData?.originalCompanyInfo || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">申请变更为</span>
            <span class="value">{{ infoData?.newCompanyInfo || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">变更理由</span>
            <span class="value">{{ infoData?.content || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <div class="section-title">
        <van-icon name="photo-o" size="18px" color="#1989fa" />
        <span>申请材料</span>
      </div>
      <div class="attachments-grid">
        <div 
          v-for="(item, index) in infoData.attachment" 
          :key="index"
          class="attachment-item"
          @click="previewFile(item)"
        >
          <div class="attachment-preview">
            <div class="file-icon" v-if="!isImageFile(item.filePath)">
              <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
            </div>
            <img v-else :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
            <div class="attachment-overlay">
              <van-icon name="eye-o" size="24px" color="#fff" />
            </div>
          </div>
          <div class="attachment-title">{{ item.displayTitle }}</div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <div class="audit-section">
      <div class="section-title">
        <van-icon name="completed" size="18px" color="#1989fa" />
        <span>审核操作</span>
      </div>
      
      <div class="audit-card">
        <!-- 审核结果 -->
        <div class="audit-item">
          <div class="audit-label">审核结果</div>
          <div class="simple-radio-group">
            <van-radio-group v-model="form.state" direction="horizontal">
              <van-radio :name="1" class="radio-option">通过</van-radio>
              <van-radio :name="2" class="radio-option">不通过</van-radio>
            </van-radio-group>
          </div>
        </div>

        <!-- 确认单位名称 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认单位名称</div>
          <van-field
            v-model="form.companyName"
            placeholder="请输入确认的单位名称"
            class="business-field"
          />
        </div>

        <!-- 确认法定代表人 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认法定代表人</div>
          <van-field
            v-model="form.legalRepresentative"
            placeholder="请输入确认的法定代表人"
            class="business-field"
          />
        </div>

        <!-- 确认联系电话 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认联系电话</div>
          <van-field
            v-model="form.contactPhone"
            placeholder="请输入确认的联系电话"
            type="tel"
            class="business-field"
          />
        </div>

        <!-- 确认注册地址 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认注册地址</div>
          <van-field
            v-model="form.registeredAddress"
            type="textarea"
            rows="2"
            placeholder="请输入确认的注册地址"
            class="business-field"
          />
        </div>

        <!-- 审核备注 -->
        <div class="audit-item">
          <div class="audit-label">审核备注</div>
          <van-field
            v-model="form.remarks"
            type="textarea"
            rows="4"
            maxlength="100"
            placeholder="请输入审核备注..."
            show-word-limit
            class="remarks-field"
          />
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button 
          type="primary" 
          size="large" 
          block 
          @click="submitAudit()"
          :loading="isSubmitting"
          class="submit-btn"
        >
          {{ isSubmitting ? '提交中...' : '提交审核' }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { stationAuditAuditH5 } from "@/api/config";

export default {
  name: "DisposalCompanyApproval",
  components: {},
  data() {
    return {
      form: {
        state: "",
        remarks: "",
        companyName: "",
        legalRepresentative: "",
        contactPhone: "",
        registeredAddress: "",
      },
      isSubmitting: false,
      infoData: null,
    };
  },
  computed: {
    hasAttachments() {
      return this.infoData && this.infoData.attachment && this.infoData.attachment.length > 0;
    },
  },
  mounted() {
    // 从路径参数中获取数据
    const dataParam = this.$route.params.data;
    console.log("路由参数：", this.$route.params);

    try {
      if (typeof dataParam === 'string') {
        this.infoData = JSON.parse(decodeURIComponent(dataParam));
      } else {
        this.infoData = dataParam;
      }

      console.log("详情数据：", this.infoData);

      if (!this.infoData) {
        throw new Error("数据为空");
      }

      const att = this.infoData?.attachment;
      if (typeof att === "string") {
        try {
          this.infoData.attachment = JSON.parse(att);
        } catch (error) {
          console.error("解析附件数据失败：", error);
          this.infoData.attachment = [];
        }
      }
    } catch (error) {
      console.error("获取数据失败：", error);
      this.$toast.fail("数据获取失败");
      this.$router.go(-1);
    }
  },
  methods: {
    validateForm() {
      if (!this.form.state) {
        this.$toast.fail("请选择审核结果");
        return false;
      }
      if (!this.form.remarks) {
        this.$toast.fail("请输入审核备注");
        return false;
      }
      
      // 如果通过，需要验证单位资料相关信息
      if (this.form.state === 1) {
        if (!this.form.companyName) {
          this.$toast.fail("请输入确认的单位名称");
          return false;
        }
        if (!this.form.legalRepresentative) {
          this.$toast.fail("请输入确认的法定代表人");
          return false;
        }
        if (!this.form.contactPhone) {
          this.$toast.fail("请输入确认的联系电话");
          return false;
        }
        if (!this.form.registeredAddress) {
          this.$toast.fail("请输入确认的注册地址");
          return false;
        }
      }
      
      return true;
    },

    buildRequestData() {
      const reqData = {
        id: this.infoData.id,
        state: this.form.state,
        remarks: this.form.remarks,
      };
      
      // 只有通过时才传递业务特定参数
      if (this.form.state === 1) {
        reqData.companyName = this.form.companyName;
        reqData.legalRepresentative = this.form.legalRepresentative;
        reqData.contactPhone = this.form.contactPhone;
        reqData.registeredAddress = this.form.registeredAddress;
      }
      
      return reqData;
    },

    async submitAudit() {
      console.log("提交的数据：", this.form);
      
      if (!this.validateForm()) return;
      
      const reqData = this.buildRequestData();
      console.log("提交的请求数据：", reqData);
      
      this.isSubmitting = true;
      try {
        const res = await stationAuditAuditH5(reqData);
        if (res.data.success || res.data.state === "success") {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },

    // 文件预览相关方法
    getFileUrl(filePath) {
      return process.env.VUE_APP_BASE_API + filePath;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);
      
      if (this.isImageFile(item.filePath)) {
        this.$imagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      try {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  padding-bottom: 20px;
}

// 导航头样式
.nav-header {
  background: #1989fa;
  padding: 12px 16px;
  
  .nav-content {
    display: flex;
    align-items: center;
    
    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      margin-left: 12px;
    }
    
    .van-icon {
      color: #ffffff;
    }
  }
}

// 概览部分
.overview-section {
  padding: 16px;
  
  .overview-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      .site-name {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        flex: 1;
        line-height: 24px;
      }
      
      .apply-time {
        font-size: 12px;
        color: #8c8c8c;
        white-space: nowrap;
        margin-left: 12px;
      }
    }
    
    .overview-content {
      .info-item {
        display: flex;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          font-size: 14px;
          color: #8c8c8c;
          width: 80px;
          flex-shrink: 0;
        }
        
        .value {
          font-size: 14px;
          color: #262626;
          flex: 1;
          line-height: 20px;
        }
      }
    }
  }
}

// 通用标题样式
.section-title {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 0;
  
  span {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-left: 8px;
  }
}

// 附件部分
.attachments-section {
  .attachments-grid {
    padding: 0 16px 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
    
    .attachment-item {
      cursor: pointer;
      
      .attachment-preview {
        position: relative;
        width: 100%;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .file-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background: #fafafa;
        }
        
        .attachment-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
        }
        
        &:hover .attachment-overlay {
          opacity: 1;
        }
      }
      
      .attachment-title {
        font-size: 12px;
        color: #8c8c8c;
        text-align: center;
        margin-top: 8px;
        line-height: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

// 审核部分
.audit-section {
  .audit-card {
    background: #ffffff;
    border-radius: 12px;
    margin: 0 16px 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .audit-item {
      margin-bottom: 24px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .audit-label {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 12px;
      }
    }
  }
}

// 简化单选按钮样式
.simple-radio-group {
  .radio-option {
    margin-right: 24px;
    font-size: 16px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 业务字段样式
.business-field {
  /deep/ .van-field__control {
    background: #f8faff;
    border: 1px solid #e6f4ff;
    border-radius: 8px;
    padding: 12px;
  }
}

// 备注字段样式
.remarks-field {
  /deep/ .van-field__control {
    background: #f8faff;
    border: 1px solid #e6f4ff;
    border-radius: 8px;
    padding: 12px;
    min-height: 80px;
  }
}

// 提交按钮
.submit-section {
  padding: 0 16px;
  
  .submit-btn {
    height: 48px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
  }
}
</style>
