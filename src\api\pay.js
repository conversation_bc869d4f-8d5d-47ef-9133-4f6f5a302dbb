import request from '../utils/request'
//银行流水
export const getVolunteerAccountList = data =>
    request({
        url: '/site/v1/VolunteerAccountApi/getVolunteerAccountList',
        method: "POST",
        data
    });
export const addOrderPayApply = data =>
    request({
        url: '/site/v1/OrderPayApplyApi/addOrderPayApply',
        method: "POST",
        data
    });
export const getPayConfList = data =>
    request({
        url: '/site/v1/PayConfApi/getPayConfList',
        method: "POST",
        data
    });
export const getPayQr = data =>
    request({
        url: '/site/v1/PayOrderApi/getPayQr',
        method: "POST",
        data
    });
export const addPayRemind = data =>
    request({
        url: '/site/v1/RemindApi/addPayRemind',
        method: "POST",
        data
    });
export const getRechargeQr = data =>
    request({
        url: '/site/v1/PayOrderApi/getRechargeQr',
        method: "POST",
        data
    });
export const addRechargeRemind = data =>
    request({
        url: '/site/v1/RemindApi/addRechargeRemind',
        method: "POST",
        data
    });
export const addVolunteerPay = data =>
    request({
        url: '/site/v1/VolunteerPayApi/addVolunteerPay',
        method: "POST",
        data
    });
export const payOrder = data =>
    request({
        url: '/site/v1/OrderApi/payOrder',
        method: "POST",
        data
    });
export const addPayOrder = data =>
    request({
        url: '/site/v1/PayOrderApi/addPayOrder',
        method: "POST",
        data
    });
export const addPrePayOrder = data =>
    request({
        url: '/site/v1/PayOrderApi/addPrePayOrder',
        method: "POST",
        data
    });
export const getPayBankConfList = data =>
    request({
        url: '/site/v1/PayBankConfApi/getPayBankConfList',
        method: "POST",
        data
    });
export const getDrugProCostInfo = data =>
    request({
        url: '/site/v1/DrugProApi/getDrugProCostInfo',
        method: "POST",
        data
    });
export const getVolunteerPayList = data =>
    request({
        url: '/site/v1/VolunteerPayApi/getVolunteerPayList',
        method: "POST",
        data
    });
export const delPayOrder = data =>
    request({
        url: '/site/v1/PayOrderApi/delPayOrder',
        method: "POST",
        data
    });
export const delRechargeQr = data =>
    request({
        url: '/site/v1/PayOrderApi/delRechargeQr',
        method: "POST",
        data
    });
