<template>
  <div :class="['box', isChange ? 'active' : 'noactive', 'flex-colum']">
    <div class="activetips" v-if="isChange">
      <van-icon name="success" />
    </div>
    <p class="title">{{ bankName }}</p>
    <div
      class="flex-row"
      style="align-items: center; justify-content: space-between"
    >
      <div class="flex-row" style="align-items: center">
        <van-icon name="paid" size="24px" :color="isChange ? '#0f62f9' : ''" />
        <p>{{ cardNumber }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    bankName: {
      type: String,
      default: "",
    },
    cardNumber: {
      type: String,
      default: "",
    },
    isChange: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  mounted() {},
  methods: {
    goEdit() {},
  },
};
</script>

<style scoped lang="less">
.activetips {
  padding: 3px 8px;
  color: white;
  font-size: 11px;
  box-sizing: border-box;
  border-radius: 0 12px 0 12px;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  background-color: var(--color-zhuti);
}
.box {
  padding: 16px;
  position: relative;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 12px;
}
.active {
  background: #f2f6fe;
  border: 1px solid #9ebaed;
}
.noactive {
  background-color: #fff;
  border: 1px solid #f2f4f5;
}
p {
  font-weight: 600;
  font-size: 14px;
  color: #112950;
  line-height: 18px;
  margin-left: 10px;
}
.title {
  margin-bottom: 10px;
}
.change {
  color: var(--color-zhuti) !important;
}
</style>
