<template>
  <div class="card">
    <div class="state-box" :class="statusClass" v-if="false">
      {{ statusText }}
    </div>
    <div class="top flex-row">
      <!-- <img :src="indentData.pro?.projectImg" class="proimg" alt="" /> -->
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData.rule_name }}</div>
        </div>
        <!--<div class="text-ellipsis-2" v-if="indentData.pro?.proIntroduction">
          {{ indentData.pro?.proIntroduction }}
        </div>
        <div class="text-ellipsis-2" v-else>Chưa có hồ sơ</div>-->
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>运输企业: </i><i class="timedata">{{ indentData.agent_name }}</i>
        </div>
      </div>
      <div></div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="notes-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>车牌信息: </i><i class="timedata">{{ indentData.plate_no }}</i>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>去审核</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { trialOrder } from "@/api/pro";
import { addPrePayOrder } from "@/api/pay";
import { Dialog } from "vant";
export default {
  data() {
    return {
      isDialogShow: true,
    };
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}), // 默认启用一个空对象
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    // statusClass() {
    //   // 0-待受理 1-受理不通过 2-受理通过 3-审核不成功 4-审核成功 5-围栏绘制成功(生效中)
    //   switch (this.indentData.applyState) {
    //     case (1, 3):
    //       return "error";
    //     case (6, 7):
    //       return "end";
    //     case (2, 4, 5):
    //       return "success";
    //     default:
    //       return "end";
    //   }
    // },
    // statusText() {
    //   switch (this.indentData.applyState) {
    //     case 0:
    //       return "待受理";
    //     case 1:
    //       return "受理不通过";
    //     case 2:
    //       return "受理通过";
    //     case 3:
    //       return "审核不成功";
    //     case 4:
    //       return "审核成功";
    //     case 5:
    //       return "围栏已生效";
    //     case 6:
    //       return "已失效";
    //     default:
    //       return "未知";
    //   }
    // },
  },
  methods: {
    formatDateTime(isoString) {
      // 将 ISO 8601 字符串转换为 Date 对象
      const date = new Date(isoString);
      // 检查是否为有效日期
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      // 获取年、月、日、小时、分钟和秒
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      // 拼接成所需格式
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    },
    handleClick(e) {
      this.$router.push({
        name: "TransportViolationsApproval",
        params: { data: e },
      });
    },
    configAddress() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/set_show", true);
    },
    goPay() {
      this.$store.commit("pay/initData");
      this.$store.commit("pay/SET_type", "ZHIFU");
      var reqData = {
        orderNo: this.indentData.order.orderNo,
        volunteerId: this.userInfo.volunteerId,
      };
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      addPrePayOrder(reqData).then((res) => {
        if (res.data.data === null) {
          this.$toast({
            message: res.data.ynMsg,
            duration: 2000,
          });
          return;
        }
        if (res?.data?.data?.data !== null) {
          this.$store.commit("pay/SET_url", res.data.data.data.qrDataURL);
          this.$store.commit("pay/SET_dialogShow", true);
        } else {
          this.$toast({
            message: res.data.data.desc,
            duration: 2000,
          });
        }
      });
    },
    goRecord() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$store.commit("proturn/SET_orderData", this.indentData.order);
      this.$router.push("/drug/recordstate");
    },
    configAddressIsjion() {
      Dialog.confirm({
        title: "Xác nhận đã nhận thuốc",
        // message: "确认收到药品，开始试药？请仔细确认，这代表试药即将开始！！",
        message:
          "Xác nhận đã nhận thuốc? Vui lòng kiểm tra kỹ, điều này có nghĩa là thử nghiệm lâm sàng sẽ bắt đầu!",
        confirmButtonColor: "#0065ff",
        confirmButtonText: "Xác Nhận",
        cancelButtonText: "Hủy Bỏ",
      })
        .then(() => {
          var reqData = {
            orderNo: this.indentData.order.orderNo,
            volunteerId: this.userInfo.volunteerId,
          };
          trialOrder(reqData).then((res) => {
            this.$toast({
              message: res.data.ynMsg,
              duration: 2000,
            });
            if (res.data.state === 0) {
              this.$emit("getList");
            }
          });
        })
        .catch(() => {
          // on cancel
        });
    },
    sumBitGoSign() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.dispatch("proturn/resetCellData");
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$router.push("/pro/paydetils");
    },
  },
};
</script>

<style scoped lang="less">
.card {
  width: 100%;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #f2f4f5;
  position: relative;
  .state-box {
    position: absolute;
    right: 30px;
    border-radius: 10px;
    top: -10px;
    padding: 2px 12px;
    box-sizing: border-box;
    font-weight: 400;
    font-size: 12px;
    text-align: center;
    color: #ffffff;
    line-height: 18px;
  }
  .success {
    background-color: #1989fa;
  }
  .error {
    background-color: #f33060;
  }
  .end {
    background-color: #909399;
  }
  .top {
    align-items: center;
    padding: 10px 16px 5px;
    box-sizing: border-box;
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.proimg {
  width: 72px;
  height: 72px;
  border-radius: 12px 12px 12px 12px;
}

.pre {
  width: 100%;
  height: 30px;
  justify-content: space-around;
}
.pre div:nth-child(1) {
  width: 100%;
  font-weight: 600;
  font-size: 17px;
  color: #112950;
}

.pre div:nth-child(2) {
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #8e9aab;
  line-height: 18px;
}
.bottom {
  padding: 10px 16px;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  .timebox {
    align-items: center;
    .timeimg {
      width: 16px;
      height: 16px;
    }
    .time {
      margin-left: 5px;
      font-size: 14px;
      color: #6b7089;
      align-items: center;
      .timedata {
        width: 200px;
        margin-left: 10px;
        color: #353c5c !important;
        font-weight: 600;
      }
    }
  }
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}
</style>
