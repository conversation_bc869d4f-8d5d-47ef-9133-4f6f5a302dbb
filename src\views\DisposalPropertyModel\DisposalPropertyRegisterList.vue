<template>
  <div class="home">
    <NavHeader
      ref="navHeader"
      title="处置单位注册审核"
      :back="true"
    ></NavHeader>

    <!-- 标签页 -->
    <van-tabs v-model="active" @change="onTabChange" sticky>
      <van-tab
        v-for="(tab, index) in tabOptions"
        :key="index"
        :title="tab.text"
      >
      </van-tab>
    </van-tabs>

    <div class="information cell">
      <div class="indent-box">
        <StandardRegisterCard
          class="card"
          :indentData="item"
          :currentTabState="tabOptions[active].value"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></StandardRegisterCard>
      </div>
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { sysUserTempListH5 } from "@/api/config";
import StandardRegisterCard from "@/views/DisposalPropertyModel/StandardRegisterCard.vue";
import nullState from "@/components/com/NullCop.vue";
export default {
  name: "DisposalPropertyRegisterList",
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: 0, // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: 1 }, // typeState: 1
        { text: "已完成", value: 2 }, // typeState: 2
      ],
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        isDeleteNew: 11, // 处置单位注册审核
        typeState: 1, // 默认为待审核
      },
    };
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    StandardRegisterCard,
    nullState,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    // 标签页切换
    onTabChange(index) {
      this.active = index;
      this.formData.typeState = this.tabOptions[index].value;
      this.formData.pageNum = 1;
      this.dataList = [];
      this.getList();
    },

    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    // 获取列表数据
    async getList() {
      try {
        console.log("请求参数:", this.formData);
        const res = await sysUserTempListH5(this.formData);
        console.log("接口返回:", res);

        if (res.data.success || res.data.state === "success") {
          const newData = res.data.result || {};
          if (this.formData.pageNum === 1) {
            this.dataList = newData.list || [];
          } else {
            this.dataList = [...this.dataList, ...(newData.list || [])];
          }

          this.total = res.data.total || 0;
          this.loading = false;
          this.finished = (newData.list || []).length === 0;
        } else {
          this.$toast.fail(res.data.message || "获取数据失败");
          this.loading = false;
          this.finished = true;
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$toast.fail("获取数据失败");
        this.loading = false;
        this.finished = true;
      }
    },
  },
};
</script>

<style scoped lang="less">
.center {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
}
.imgPosi {
  position: relative;
}
.posi-tips {
  position: absolute;
  right: 0;
  top: 0;
  padding: 1px 6px;
  box-sizing: border-box;
  border-radius: 15px;
  color: white;
  background-color: #f56c6c;
}
.type-imgsize {
  width: 20px;
  height: 20px;
  margin: 5px 5px 0;
}
/deep/.van-dropdown-menu__bar {
  box-shadow: none;
}

.listContnet {
  .listHeader {
    display: flex;
    .listName {
      color: rgb(16, 39, 95);
      font-size: 18px;
      font-weight: 700;
      .listXb {
        font-size: 14px;
        font-weight: normal;
      }
    }

    .listText,
    .listTextZd {
      position: relative;
      color: #fff;
      font-size: 12px;
      transform-style: preserve-3d;
      text-align: center;
      padding: 0 5px;
      width: 40px;
      margin-left: 5px;
      margin-right: 15px;
    }

    .listText::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(1, 128, 254);
    }

    .listTextZd-y {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      padding: 3px 5px;
      box-sizing: border-box;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(255, 111, 0);
    }
  }

  .listSection {
    display: flex;
    flex-direction: flex-start;

    .img {
      padding-top: 10px;
      margin-right: 10px;
    }

    div.listDiv {
      display: flex;
      align-items: center;
      color: #314b8b;
      font-size: 15px;
      margin-top: 10px;

      svg {
        margin-right: 5px;
      }

      .blue {
        color: rgb(11, 110, 250);
      }
    }
  }

  .listFooter {
    margin-top: 10px;
    .van-col--12 {
      .van-button {
        background-color: rgb(229, 243, 254);
        color: rgb(1, 128, 254);
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        height: auto;
        width: 100%;
        border: none;
        font-weight: 700;
      }
    }
  }
}
.contian {
  width: 100%;
  min-height: 100vh;
  padding: 0 0 144px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  z-index: 0;
}
.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}
.indent-box {
  padding: 12px 14px 24px 14px;
}
.indent-box .card {
  margin-top: 20px;
}
.indent-box .card:nth-child(1) {
  margin-top: 0 !important;
}
.tips-cc {
  font-weight: 400;
  font-size: 14px;
  color: #ce0602;
  line-height: 25px;
}
</style>
