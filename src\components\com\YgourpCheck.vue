<template>
  <div class="custom-input">
    <div class="label">{{ label }}<i v-if="isWhite" class="isTrue"> *</i></div>
    <div class="input-container flex-row">
      <slot name="left-icon" v-if="hasLeftIcon"></slot>
      <div class="input-field" :style="inputFieldStyle">
        <van-radio-group
          v-model="radio"
          direction="horizontal"
          @change="handleInput"
        >
          <van-radio name="0">{{ ac1 }}</van-radio>
          <van-radio name="1">{{ ac2 }}</van-radio>
        </van-radio-group>
      </div>
      <div class="xian"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomInput",
  props: {
    ac1: {
      type: String,
      default: "Đúng",
    },
    isWhite: {
      type: Boolean,
      default: false,
    },
    ac2: {
      type: String,
      default: "KHÔNG",
    },
    isEnabled: {
      type: Boolean,
      default: true,
    },
    label: {
      type: String,
      default: "",
    },
    error: {
      type: Boolean,
      default: false,
    },
    YuXuan: {
      type: String,
      default: "-1",
    },
  },
  data() {
    return {
      radio: "-1",
      inputValue: "",
    };
  },
  mounted() {
    if (String(this.YuXuan) === "0") {
      this.radio = "0"; // Correct assignment operator
    } else if (String(this.YuXuan) === "1") {
      this.radio = "1"; // Correct assignment operator
    }
  },
  computed: {
    hasLeftIcon() {
      return !!this.$slots["left-icon"];
    },
    inputFieldStyle() {
      return {
        borderBottomColor: this.isEnabled
          ? this.radio !== -1
            ? "#0f62f9"
            : "grey"
          : "grey",
        paddingLeft: this.hasLeftIcon ? "30px" : "0",
      };
    },
  },
  methods: {
    handleInput(value) {
      this.$emit("input", value); // 将选中的值发送给父组件
    },
  },
};
</script>

<style scoped>
.isTrue {
  color: rgb(252, 85, 49);
}
.custom-input {
  width: 100%;
  display: inline-block;
}

.input-container {
  position: relative;
  align-items: center;
  width: 100%;
}
.label {
  font-weight: 600;
  font-size: 16px;
  color: #112950;
}
.input-field {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 15px;
  color: #112950;
  border-bottom: 2px solid grey;
  width: 100%;
}

/* 如果需要更多的样式，可以在这里添加 */
</style>
