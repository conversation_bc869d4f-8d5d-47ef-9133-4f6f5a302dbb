import axios from 'axios';
import md5 from 'md5';
import qs from 'qs'; // 新增：引入 qs 模块
import store from '@/store'; // 引入 Vuex store
export const API_BASE_URL = process.env.VUE_APP_API_URL;

//pro
const APP_KEY = 'e3e24540-10b9-4b93-82f1-0b6921c25ec7'; 
const appSecret = '03b78767b290fa42803ea5582fe39e68e00c74a0';
const salt = 'gmcx';
//test
// const APP_KEY = 'e3e24540-10b9-4b93-82f1-0b6921c25ec7';
// const appSecret = '03b78767b290fa42803ea5582fe39e68e00c74a0';


const service = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? '/api/' : API_BASE_URL,
  timeout: 600000,
  method: 'POST',
  headers: {
    // 'Content-Type': 'application/x-www-form-urlencoded', // ✅ 修改为表单格式
    'Content-Type': 'multipart/form-data',//文件流传输
    'userid': store.state.user.userInfo.id

  }

});
console.log("store",store);
const whitelist = ['/mobileNoLogin'];

// 请求拦截器
service.interceptors.request.use(
  async (config) => {
    const isInWhitelist = whitelist.some((path) => config.url.includes(path));
    if (!isInWhitelist) {
      const TOKEN = localStorage.getItem('TOKEN');
      if (TOKEN) {
        config.headers['WX-ACCESS-TOKEN'] = TOKEN;
      }
    }

    const timestamp = Date.now().toString();
    const signAppend = APP_KEY + appSecret + timestamp + salt;
    const sign = md5(signAppend).toUpperCase();

    const baseUrl = 'http://dummy-base';
    const urlObj = new URL(config.url, baseUrl);

    if (config.params) {
      Object.entries(config.params).forEach(([key, val]) => {
        urlObj.searchParams.set(key, val);
      });
    }

    urlObj.searchParams.set('appKey', APP_KEY);
    urlObj.searchParams.set('timestamp', timestamp);
    urlObj.searchParams.set('sign', sign);

    config.url = urlObj.pathname + urlObj.search;
    config.params = {};

    // ✅ 如果是 POST 并且 Content-Type 是 x-www-form-urlencoded，则转换 data
    if (
      config.method === 'post' &&
      config.headers['Content-Type'] === 'application/x-www-form-urlencoded' &&
      config.data &&
      typeof config.data === 'object'
    ) {
      config.data = qs.stringify(config.data);
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    if (response.data.state === '886') {
      localStorage.removeItem('TOKEN');
    }
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('TOKEN');
    }
    return Promise.reject(error);
  }
);

export default service;
