<template>
  <div class="custom-input">
    <div class="label">{{ label }} <i v-if="isWhite" class="isTrue"> *</i></div>
    <div class="input-container flex-row" :style="inputFieldStyle">
      <div class="item-box">
        <p class="tips">Mặt Trước</p>
        <van-uploader
          :deletable="deletable"
          v-model="fileListData.frontFileList"
          :max-count="1"
          :after-read="(file) => afterRead(file, 'front')"
        />
      </div>
      <div class="item-box">
        <p class="tips">Mặt Sau</p>
        <van-uploader
          :deletable="deletable"
          v-model="fileListData.backFileList"
          :max-count="1"
          :after-read="(file) => afterRead(file, 'back')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { IMG_BASE_URL } from "@/utils/globalConstants";
import axios from "axios";

export default {
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    isEnabled: {
      type: Boolean,
      default: true,
    },
    isWhite: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "",
    },
    fileType: {
      type: String,
      default: "",
    },
    borderBottomColor: {
      type: String,
      default: "grey",
    },
    error: {
      type: Boolean,
      default: false,
    },
    deletable: {
      type: Boolean,
      default: true,
    },
    //避免别的地方使用出现bug，现在就是在用户信息上传图片的时候进行处理
    teshu: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    value: {
      immediate: true, // 立即执行一次，以便在初始加载时设置数据
      handler() {
        this.initData(); // 当父组件的值更新时，重新初始化数据
      },
    },
  },
  mounted() {},
  data() {
    return {
      fileData: {
        fileType: this.fileType,
        front: "",
        back: "",
      },
      fileListData: {},
      dynamicBorderBottomColor: this.borderBottomColor,
    };
  },
  computed: {
    hasLeftIcon() {
      return !!this.$slots["left-icon"];
    },
    hasRightBox() {
      return !!this.$slots["right-box"];
    },
    inputFieldStyle() {
      return {
        borderBottomColor: this.isEnabled
          ? this.fileData.front !== "" && this.fileData.back !== ""
            ? "#0f62f9"
            : "#e5e5e5"
          : "#e5e5e5",
      };
    },
  },
  methods: {
    getExtension(mimeType) {
      const parts = mimeType.split("/");
      if (parts.length === 2) {
        return parts[1];
      } else {
        throw new Error("无效的MIME类型");
      }
    },
    initData() {
      //初始化数据
      this.fileListData = this.value;
      this.fileData.front = this.value?.frontFileList[0].url;
      this.fileData.back = this.value?.backFileList[0].url;
      if (this.teshu) {
        this.$emit("img-change", this.fileData);
      }
      if (this.fileData.front === "") {
        this.fileListData = {};
      }
      if (this.fileData.back === "") {
        this.fileListData = {};
      }
    },
    afterRead(file, type) {
      const reqData = {
        base64Str: file.content,
        busiFolder: "test",
        fileExtension: this.getExtension(file.file.type),
        fileType: type,
      };
      axios({
        url: IMG_BASE_URL,
        method: "post",
        data: reqData,
      }).then((res) => {
        var ImgUrl = res.data.url;
        if (type === "front") {
          this.fileData.front = ImgUrl;
        }
        if (type === "back") {
          this.fileData.back = ImgUrl;
        }
        if (this.fileData.front !== "" && this.fileData.back !== "") {
          this.$emit("img-change", this.fileData);
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.custom-input {
  width: 100%;
  display: inline-block;
}
.isTrue {
  color: rgb(252, 85, 49);
}
.input-container {
  position: relative;
  align-items: center;
  width: 100%;
  margin-top: 15px;
  border-bottom: 2px solid transparent;
  .item-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
  }
}
/deep/.van-uploader__upload {
  width: 120px !important;
  height: 120px;
}
.label {
  font-weight: 600;
  font-size: 16px;
  color: #112950;
}

.posi {
  position: absolute;
}
.tips {
  font-weight: 400;
  font-size: 14px;
  color: #b2bac6;
  line-height: 24px;
}
</style>
