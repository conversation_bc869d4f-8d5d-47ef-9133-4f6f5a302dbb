<template>
  <div class="bigbox flex-row">
    <div class="tab flex-row">
      <div
        v-for="(item, index) in tabData"
        :key="index + 'a'"
        :class="{ active: index === tabActive }"
        @click="goLink(item.path, index)"
      >
        <van-icon
          :name="item.icon"
          size="24px"
          :color="index === tabActive ? '#0065FF' : '#000000'"
        />
        <p v-if="index === tabActive">{{ item.label }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  computed: {
    ...mapState("config", ["tabActive"]),
  },
  data() {
    return {
      tabData: [
        {
          icon: "home-o",
          label: "Trang chủ",
          path: "/index",
        },
        {
          icon: "orders-o",
          label: "Lịch Sử",
          path: "/indent",
        },
        {
          icon: "manager-o",
          label: "Tôi",
          path: "/my",
        },
      ],
    };
  },
  methods: {
    goLink(path, index) {
      // 检查目标路径是否与当前路径相同
      if (this.$route.path !== path) {
        this.$router.push({ path: path });
        this.$store.dispatch("config/setActive", index);
      }
    },
  },
};
</script>

<style scoped lang="less">
.bigbox {
  width: 100%;
  padding: 24px 24px 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: transparent;
  align-items: center;
  // backdrop-filter: blur(3px);
  box-sizing: border-box;
}
.tab {
  width: 327px;
  height: 72px;
  background: #ffffff;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  border-radius: 99px 99px 99px 99px;
  justify-content: space-around;
  align-items: center;
  div {
    padding: 10px 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    p {
      font-size: 13px;
      color: #0065ff;
      margin-left: 5px;
    }
  }
  .active {
    background: rgba(0, 101, 255, 0.15);
    border-radius: 22px;
  }
}
</style>
