<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      :title="infoData?.pageTitle"
      :back="true"
    ></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.userName"
              name="content"
              label="姓名："
              label-width="100px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.mobilePhone"
              name="content"
              label="手机号码："
              label-width="100px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.addressDistrict?.fullAreaName"
              name="content"
              label="管辖区："
              label-width="100px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1"
              name="content"
              label="联系人："
              label-width="100px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1Tel"
              name="content"
              label="联系人电话："
              label-width="100px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.ucsCode"
              name="content"
              label="统一信用代码："
              label-width="150"
              :readonly="true"
            />
            <van-field
              :value="infoData?.createDate"
              name="content"
              label-width="100px"
              label="申请日期："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader
      left="附件查阅"
      v-if="infoData.attachments?.length !== 0"
    ></LabelHeader>
    <div class="information" v-if="infoData.attachments?.length !== 0">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              v-for="(item, index) in infoData.attachments"
              :key="index"
              :name="item.attachType"
              :label="item.displayTitle"
              label-width="120px"
            >
              <template #input>
                <img
                  class="uploadImgsize"
                  :src="`http://113.87.160.8:47025/upload/${item.filePath}`"
                  alt=""
                />
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader left="审核情况"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="applyState" label="状态：" label-width="120px">
              <template #input>
                <van-radio-group
                  v-model="form.userState"
                  direction="horizontal"
                >
                  <van-radio :name="1">通过</van-radio>
                  <van-radio :name="5">不通过</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.acceptedRemarks"
              name="acceptedRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="审核备注"
              type="textarea"
              maxlength="100"
              placeholder="请输入备注"
              show-word-limit
            />
          </van-form>
          <div style="width: 100%; height: 150px"></div>
          <div class="btn-box">
            <yButton title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { sysUserTempUpdateStateH5 } from "@/api/config";
export default {
  data() {
    return {
      form: {
        userState: "",
        acceptedRemarks: "",
      },
      state: "",
      showCalendar: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审批备注" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    this.infoData = this.$route.params.data;
    console.log("接收到的数据：", this.infoData.attachments);
  },
  methods: {
    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.form.sealDate = `${year}-${month}-${day}`;
      this.showCalendar = false;
    },
    sumBit() {
      console.log("提交的数据：", this.form);
      if (this.form.userState !== "") {
        var reqData = {
          ...this.form,
          id: this.infoData.id,
        };
        sysUserTempUpdateStateH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

/deep/ .van-radio__label {
  color: #262626;
  font-size: 14px;
  margin-left: 8px;
}

/deep/ .van-radio--checked .van-radio__label {
  color: #1989fa;
}

/deep/ .van-radio {
  margin-right: 24px;
  margin-bottom: 8px;
}

/deep/ .van-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .van-radio__icon {
  font-size: 16px;
}

/deep/ .van-radio__icon--checked {
  color: #1989fa;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
</style>
