<template>
  <div class="sty">
    <van-dialog
      confirmButtonColor="#0065ff"
      confirmButtonText="Đã Thanh Toán"
      cancelButtonText="Hủy"
      v-model="dialogShow"
      title="TRẢI NGHIỆM THUỐC KIẾM TIỀN"
      show-cancel-button
      @confirm="confirm"
      @cancel="cancel"
    >
      <div class="flex-colum mid-box">
        <p class="mid-box-1">Công Nghệ Sinh Học YT (TTS)</p>
        
        <img :src="url" alt="" />
      </div>
      <div class="mid-box-2"><PERSON>yển tiền xong, vui lòng nhấn vào "Đã Thanh toán". <PERSON>ệ thống sẽ tự động xác nhận trong vòng 3 phút.</div>
    </van-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { addPayRemind, delPayOrder } from "@/api/pay";
// import { joinDrugPro } from "@/api/pro";
export default {
  data() {
    return {};
  },
  components: {},
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState("proturn", ["proData", "idData"]),
    ...mapState("pay", ["payMoney", "bankInfo", "type", "url", "dialogShow"]),
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    confirm() {
      this.zfConfirm();
    },
    zfConfirm() {
      var reqData = {
        volunteerId: this.userInfo.volunteerId,
      };
      addPayRemind(reqData).then((res) => {
        this.$toast({
          message: res.data.ynMsg,
          duration: 2000,
        });
        this.$store.commit("pay/SET_dialogShow", false);
        if (res.data.state === 0) {
          if (this.$route.path !== "/indent") {
            this.$router.push({
              path: "/indent",
            });
            this.$store.commit("config/SET_tabActive", 1);
          }
        }
      });
    },
    cancel() {
      var reqData = {
        orderNo: this.idData.orderNo,
        volunteerId: this.userInfo.volunteerId,
      };
      delPayOrder(reqData);
      if (this.$route.path !== "/indent") {
        this.$router.push({
          path: "/indent",
        });
        this.$store.commit("config/SET_tabActive", 1);
      }
      this.$store.commit("pay/SET_dialogShow", false);
      this.$store.commit("proturn/set_Id", "");
    },
  },
};
</script>

<style lang="less" scoped>
.mid-box {
  margin: 0px 30px 30px 30px;
  box-sizing: border-box;
}
.mid-box-1 {
  height: 28px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
}
.mid-box-2 {
  height: 46px;
  width: 85%;
  margin: -20px 20px 0px 20px;
  font-size: 12px;
  font-style: italic;
  color: #aaa;
}
.sty {
  position: relative;
  z-index: 99999999;
}
.van-dialog {
  font-size: 16px;
}
</style>
