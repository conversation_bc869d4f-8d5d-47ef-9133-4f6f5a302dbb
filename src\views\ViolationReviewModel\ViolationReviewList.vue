<template>
  <div class="home">
    <NavHeader
      ref="navHeader"
      title="产生地业务报停及延期审核列表"
      :back="true"
    ></NavHeader>
    <!-- <van-dropdown-menu active-color="#1989fa">
          <van-dropdown-item v-model="formData.organId" :options="option1" />
          <van-dropdown-item v-model="formData.types" :options="columns" />
        </van-dropdown-menu> -->
    <van-sticky :offset-top="navHeight">
      <van-search
        v-model="formData.qkey"
        placeholder="请输入搜索关键词"
        @search="handleSearch()"
        show-action
      >
        <template #action>
          <div @click="handleSearch()">搜索</div>
        </template>
      </van-search>
    </van-sticky>
    <div class="information cell">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        style="margin: 10px"
      >
        <van-cell
          is-link
          v-for="(item, index) in dataList"
          :key="index"
          @click="handleClick(item)"
        >
          <div class="listContnet">
            <div class="listHeader">
              <span class="listName"> 审核内容：{{ item.content }} </span>
            </div>
            <div class="listSection">
              <div>
                <div class="listDiv">
                  创建日期：<span class="blue">{{ item.createDate }}</span>
                </div>
                <div class="listDiv blue y-text-ellipsis">
                  联系人：<span>{{ item.sysUser.mobilePhone }}</span>
                </div>
              </div>
            </div>
          </div>
        </van-cell>
      </van-list>
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { stationAuditFindListH5 } from "@/api/config";
import nullState from "@/components/com/NullCop.vue";
// import { initDataList } from "@/utils/init.js";
export default {
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      loading: false,
      finished: false,
      option1: [
        {
          text: "请选择渠道",
          value: "",
        },
      ],
      value1: "请选择",
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        type: 1,
        beginTime: "",
        endTime: "",
      },
    };
  },
  components: {
    nullState,
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    handleClick(e) {
      this.$router.push({
        name: "ProductionAuditApproval",
        params: { data: e },
      });
    },
    addValueAndText(dataArray) {
      return dataArray.map((item) => {
        return {
          ...item,
          value: item.dataCode,
          text: item.dataName,
        };
      });
    },
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSearch() {
      this.formData.page = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },
    getList() {
      if (this.loading) return;
      this.loading = true;
      this.$loadingU.show("请等待....", 5000);
      const reqData = {
        ...this.formData,
      };
      stationAuditFindListH5(reqData)
        .then((res) => {
          console.log("获取列表数据：", res);
          if (res.data.success) {
            const newData = res.data.result;
            this.dataList = newData.list;
            console.log("获取到的数据：", this.dataList);
          } else {
            if (this.formData.page === 1) {
              this.dataList = [];
            }
            this.finished = true;
          }
          this.loading = false;
          this.$loadingU.hide();
        })
        .catch(() => {
          this.$loadingU.hide();
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.center {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
}
.imgPosi {
  position: relative;
}
.posi-tips {
  position: absolute;
  right: 0;
  top: 0;
  padding: 1px 6px;
  box-sizing: border-box;
  border-radius: 15px;
  color: white;
  background-color: #f56c6c;
}
.type-imgsize {
  width: 20px;
  height: 20px;
  margin: 5px 5px 0;
}
/deep/.van-dropdown-menu__bar {
  box-shadow: none;
}

.listContnet {
  .listHeader {
    display: flex;
    .listName {
      color: rgb(16, 39, 95);
      font-size: 18px;
      font-weight: 700;
      .listXb {
        font-size: 14px;
        font-weight: normal;
      }
    }

    .listText,
    .listTextZd {
      position: relative;
      color: #fff;
      font-size: 12px;
      transform-style: preserve-3d;
      text-align: center;
      padding: 0 5px;
      width: 40px;
      margin-left: 5px;
      margin-right: 15px;
    }

    .listText::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(1, 128, 254);
    }

    .listTextZd-y {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      padding: 3px 5px;
      box-sizing: border-box;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(255, 111, 0);
    }
  }

  .listSection {
    display: flex;
    flex-direction: flex-start;

    .img {
      padding-top: 10px;
      margin-right: 10px;
    }

    div.listDiv {
      display: flex;
      align-items: center;
      color: #314b8b;
      font-size: 15px;
      margin-top: 10px;

      svg {
        margin-right: 5px;
      }

      .blue {
        color: rgb(11, 110, 250);
      }
    }
  }

  .listFooter {
    margin-top: 10px;
    .van-col--12 {
      .van-button {
        background-color: rgb(229, 243, 254);
        color: rgb(1, 128, 254);
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        height: auto;
        width: 100%;
        border: none;
        font-weight: 700;
      }
    }
  }
}
</style>
