<template>
  <div class="card">
    <div :class="['state-box', levelClass]">Level: {{ data.proLevel }}</div>
    <div class="top flex-row">
      <img :src="data.projectImg" class="proimg" alt="" />
      <div class="pre flex-colum">
        <div class="text text-ellipsis">{{ data.projectName }}</div>
        <div class="text-ellipsis-2" v-if="data.proIntroduction">
          {{ data.proIntroduction }}
        </div>
        <div class="text-ellipsis-2" v-else>Ch<PERSON><PERSON> c<PERSON> hồ sơ</div>
      </div>
    </div>
    <div class="flex-row timebox">
      <van-icon name="friends-o" size="16px" color="#1989fa" />
      <div class="flex-row time">
        <i style="margin-left: 5px">tuổi:</i
        ><i class="timedata">{{ data.starAge }}-{{ data.endAge }} </i>
      </div>
    </div>
    <div class="flex-row timebox">
      <van-icon name="manager-o" size="16px" color="#1989fa" />
      <div class="flex-row time">
        <i style="margin-left: 5px">nam giới:</i
        ><i class="timedata">{{ data.manNum }} </i>
        <i style="margin-left: 5px">nữ giới:</i
        ><i class="timedata">{{ data.womanNum }} </i>
      </div>
    </div>
    <div class="flex-row timebox">
      <van-icon name="underway-o" size="16px" color="#1989fa" />
      <div class="flex-row time">
        <i style="margin-left: 5px">thời gian phát hành:</i
        ><i class="timedata">{{ time(data.regTime) }}</i>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row">
      <div class="money">${{ data.subsidyMoney * 0.01 }}</div>
      <div
        class="flex-row pre-c"
        :class="!isAddPro ? 'gary' : ''"
        @click="handleApplyPro"
      >
        <p>đi tham dự</p>
        <van-icon name="arrow" :color="isAddPro ? '#1989fa' : '#909399'" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState("Permissions", ["isAddPro"]),
    ...mapState("user", ["userInfo"]),
    levelClass() {
      switch (this.data.proLevel) {
        case "A":
          return "level-a";
        case "B":
          return "level-b";
        case "C":
          return "level-c";
        case "D":
          return "level-d";
        case "E":
          return "level-e";
        default:
          return "default-level";
      }
    },
  },
  methods: {
    time(t) {
      const date = new Date(t);
      const year = date.getUTCFullYear();
      const month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
      const day = date.getUTCDate().toString().padStart(2, "0");
      const hours = date.getUTCHours().toString().padStart(2, "0");
      const minutes = date.getUTCMinutes().toString().padStart(2, "0");
      const seconds = date.getUTCSeconds().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} UTC`;
    },
    handleApplyPro() {
      // if (!this.isAddPro) {
      //   this.$toast({
      //     message: "Vui lòng điền đầy đủ thông tin",
      //     duration: 2000,
      //   });
      //   return;
      // }
      var idData = {
        projectId: this.data.projectId,
      };
      var a = JSON.stringify(idData);
      this.$router.push({
        path: "/prodetils",
        query: { idData: a },
      });
    },
  },
};
</script>

<style scoped lang="less">
.gary {
  color: #909399 !important;
}
.card {
  width: 327px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #f2f4f5;
  position: relative;
  .top {
    align-items: center;
    padding: 16px 16px 0;
    box-sizing: border-box;
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.proimg {
  width: 72px;
  height: 72px;
  border-radius: 12px 12px 12px 12px;
}
.timebox {
  padding: 3px 16px 5px;
  box-sizing: border-box;
  align-items: center;
  .timeimg {
    width: 16px;
    height: 16px;
  }
  .time {
    font-weight: 600;
    font-size: 12px;
    color: #b2bac6;
    .timedata {
      color: #5e6f88 !important;
    }
  }
}
.pre {
  width: 80%;
  margin-left: 10px;
  min-height: 70px;
  justify-content: space-around;
}

.pre div:nth-child(1) {
  width: 90%;
  font-weight: 600;
  font-size: 17px;
  color: #112950;
}

.pre div:nth-child(2) {
  font-weight: 400;
  font-size: 12px;
  color: #8e9aab;
  line-height: 18px;
}
.bottom {
  padding: 16px;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  .money {
    width: 56px;
    height: 18px;
    background: #0065ff;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    line-height: 18px;
    border-radius: 12px 12px 12px 12px;
    text-align: center;
  }
  .pre-c {
    align-items: center;
    font-weight: 600;
    font-size: 12px;
    color: #0065ff;
    line-height: 18px;
  }
}
.state-box {
  position: absolute;
  right: 30px;
  border-radius: 10px;
  top: -10px;
  padding: 2px 12px;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  color: #ffffff;
  line-height: 18px;
}
.level-a {
  background-color: #ff0000; // red
}
.level-b {
  background-color: #ffa500; // orange
}
.level-c {
  background-color: #bdbd17; // yellow
}
.level-d {
  background-color: #008000; // green
}
.level-e {
  background-color: #0000ff; // blue
}
.default-level {
  background-color: #909399; // grey
}
</style>
