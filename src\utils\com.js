export function go(path, isTime) {
  if (isTime) {
    setTimeout(() => {
      this.$router.push(path); // 跳转到指定路由
    }, 1000); // 延时1秒
  } else {
    this.$router.push(path); // 立即跳转到指定路由
  }
}

export function isLogin() {
    const TOKEN = localStorage.getItem('TOKEN');
    if (TOKEN) {
       return true
    }
    else{
        this.$router.push('/login')
        return false
    }
}
export function goback(isTime) {
      if (isTime) {
    setTimeout(() => {
    this.$router.go(-1); // 返回上一页
    }, 1000); // 延时1秒
  } else {
    this.$router.go(-1); // 返回上一页
  }
}
export function moneyGs(number) {
  if (typeof number !== 'number') {
    return '0'; // 或者你可以返回其他默认值或处理逻辑
  }
  return number.toLocaleString();
}
export function checkForEmptyValues(obj, whitelist = []) {
    const emptyKeys = [];
    for (const key in obj) {
        if (!whitelist.includes(key) && !obj[key]) {
            emptyKeys.push(key);
        }
    }
    return emptyKeys;
}
// utils/validator.js

export const validatePasswordMatch = (password, confirmPassword) => {
    return password === confirmPassword;
};

export const validatePassword = (password) => {
    // 密码基本规则示例：至少8个字符，包含至少一个数字、一个大写字母和一个特殊字符
    const passwordRegex = /^.{8,}$/; 
       return passwordRegex.test(password);
    // return password
};
// export const validatePassword = (password) => {
//     // 密码基本规则示例：至少8个字符，包含至少一个数字、一个大写字母和一个特殊字符
//     const passwordRegex = /^(?=.*\d)(?=.*[A-Z])(?=.*[!@#$%^&*()_+])(?=.*[a-zA-Z]).{8,}$/;
//     return passwordRegex.test(password);
//     // return password
// };

export const validateEmail = (email) => {
    // 简单的邮箱验证规则示例
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};


// /**
//  * 将ISO 8601时间字符串转换为指定格式的时间字符串
//  * @param {string} isoString - ISO 8601格式的时间字符串
//  * @returns {string} 格式化后的时间字符串
//  */
// export const formatIsoString = (isoString) => {
//   try {
//     // 解析时间字符串
//     const date = new Date(isoString);

//     // 转换为越南当地时间
//     const optionsDate = {
//       day: '2-digit',
//       month: '2-digit',
//       year: 'numeric',
//       timeZone: 'Asia/Ho_Chi_Minh'
//     };

//     const optionsTime = {
//       hour: '2-digit',
//       minute: '2-digit',
//       timeZone: 'Asia/Ho_Chi_Minh',
//       hour12: false
//     };

//     const formatterDate = new Intl.DateTimeFormat('vi-VN', optionsDate);
//     const formatterTime = new Intl.DateTimeFormat('vi-VN', optionsTime);

//     const formattedDate = formatterDate.format(date);
//     const formattedTime = formatterTime.format(date);

//     return `${formattedDate} ${formattedTime}`;
//   } catch (error) {
//     // 在这里处理捕获到的异常
//     console.error('Error occurred while formatting date:', error);
//     return isoString; // 返回原始的 ISO 字符串
//   }
// }

/**
 * @param {string} isoString - ISO 8601格式的时间字符串
 * @returns {string} 格式化后的时间字符串
 */
export const formatIsoString = (isoString) => {
  try {
    // 手动解析时间字符串
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    // 如果时、分、秒全为00，则不显示
    const timePart = (hours === '00' && minutes === '00' && seconds === '00') ? '' : ` ${hours}:${minutes}:${seconds}`;

    return `${day}/${month}/${year}${timePart}`;
  } catch (error) {
    // 在这里处理捕获到的异常
    console.error('Error occurred while formatting date:', error);
    return isoString; // 返回原始的 ISO 字符串
  }
};
export function  debounce(func, wait) {
      let timeout
      return function() {
        const context = this,
          args = arguments
        clearTimeout(timeout)
        timeout = setTimeout(() => func.apply(context, args), wait)
      }
    }
export function throttle(func, limit) {
    let lastFunc
    let lastRan
    return function () {
        const context = this,
            args = arguments
        if (!lastRan) {
            func.apply(context, args)
            lastRan = Date.now()
        } else {
            clearTimeout(lastFunc)
            lastFunc = setTimeout(function () {
                if (Date.now() - lastRan >= limit) {
                    func.apply(context, args)
                    lastRan = Date.now()
                }
            }, limit - (Date.now() - lastRan))
        }
    }
}