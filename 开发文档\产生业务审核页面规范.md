# 产生业务审核页面设计规范

## 1. 页面结构

### 1.1 基本布局
```vue
<template>
  <div class="container">
    <!-- 顶部导航 -->
    <NavHeader ref="navHeader" :title="审核页面标题" :back="true"></NavHeader>
    
    <!-- 审核数据区域 -->
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 数据字段 -->
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料区域 -->
    <div class="attachments-section">
      <LabelHeader left="申请材料"></LabelHeader>
      <!-- 材料内容 -->
    </div>

    <!-- 审核操作/结果区域 -->
    <LabelHeader :left="isViewMode ? '审核结果' : '审核情况'"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <!-- 审核表单或结果显示 -->
        </div>
      </div>
    </div>
  </div>
</template>
```

## 2. 审核数据区域规范

### 2.1 必须显示的字段（按顺序）
```vue
<!-- 产生地名称 -->
<van-field
  :value="infoData?.engineeringName"
  name="content"
  label="产生地名称："
  :readonly="true"
/>

<!-- 产生地地址（带查看地图按钮） -->
<van-field
  :value="infoData?.coordinateInfo"
  name="content"
  label="产生地地址："
  :readonly="true"
>
  <template #button>
    <van-button
      size="mini"
      type="primary"
      @click="goMap"
      icon="location-o"
      round
      class="map-btn"
    >
      地图
    </van-button>
  </template>
</van-field>

<!-- 建筑垃圾类型 -->
<van-field
  :value="infoData.moreGarbageTypeListStr"
  name="content"
  label="建筑垃圾类型："
  label-width="120"
  :readonly="true"
/>

<!-- 有效期 -->
<van-field
  :value="getValidityPeriod()"
  name="content"
  label="有效期："
  :readonly="true"
/>

<!-- 申报容量 -->
<van-field
  :value="`${infoData?.allCapacity ?? ''} 方`"
  name="content"
  label="申报容量："
  :readonly="true"
/>

<!-- 所属地区 -->
<van-field
  :value="infoData?.addressDistrict?.fullAreaName"
  name="content"
  label="所属地区:"
  :readonly="true"
/>

<!-- 主运单位 -->
<van-field
  :value="infoData?.mainCarrierUnitAgent?.agentName || '-'"
  name="content"
  label="主运单位："
  :readonly="true"
/>

<!-- 联运单位 -->
<van-field
  :value="getJointTransportUnits()"
  name="content"
  label="联运单位："
  :readonly="true"
/>
```

### 2.2 数据处理方法
```javascript
methods: {
  // 获取有效期显示文本
  getValidityPeriod() {
    if (!this.infoData) return '-';
    const start = this.infoData.beginValidityTime;
    const end = this.infoData.endValidityTime;
    if (start && end) {
      return `${start} 至 ${end}`;
    }
    return '-';
  },

  // 获取联运单位列表
  getJointTransportUnits() {
    if (!this.infoData || !this.infoData.uuitApplyAgents || this.infoData.uuitApplyAgents.length === 0) {
      return '-';
    }
    
    const unitNames = this.infoData.uuitApplyAgents
      .filter(agent => agent.agentName)
      .map(agent => agent.agentName);
    
    return unitNames.length > 0 ? unitNames.join('、') : '-';
  },

  // 查看地图功能
  goMap() {
    if (!this.infoData || !this.infoData.regionDef) {
      this.$toast.fail("缺少地图区域数据，无法查看地图");
      return;
    }

    const mapData = {
      ...this.infoData,
      pageCode: "当前页面标识",
    };

    this.$router.push({ name: "CheckMap", params: { data: mapData } });
  },
}
```

## 3. 审核操作区域规范

### 3.1 编辑模式（待审核状态）
```vue
<template v-if="!isViewMode">
  <van-field name="applyState" label="审核：" label-width="120px">
    <template #input>
      <van-radio-group v-model="form.applyState" direction="horizontal">
        <van-radio :name="8">通过</van-radio>
        <van-radio :name="3" style="margin-left: 20px">不通过</van-radio>
      </van-radio-group>
    </template>
  </van-field>
  
  <van-field
    v-model="form.ruralDevelopmentRemarks"
    name="ruralDevelopmentRemarks"
    label-width="120px"
    rows="3"
    autosize
    label="审核备注"
    type="textarea"
    maxlength="100"
    placeholder="请输入备注"
    show-word-limit
  />
</template>
```

### 3.2 查看模式（已完成状态）
```vue
<template v-if="isViewMode">
  <van-field name="applyState" label="审核结果：" label-width="120px">
    <template #input>
      <van-radio-group :value="infoData.applyState" direction="horizontal" disabled>
        <van-radio :name="8">通过</van-radio>
        <van-radio :name="3" style="margin-left: 20px">不通过</van-radio>
      </van-radio-group>
    </template>
  </van-field>
  
  <van-field
    :value="getAuditRemarks(infoData)"
    name="auditRemarks"
    label="审核备注："
    label-width="120px"
    type="textarea"
    rows="3"
    :readonly="true"
  />
  
  <van-field
    :value="infoData.applyDate || ''"
    name="auditDate"
    label="审核时间："
    label-width="120px"
    :readonly="true"
  />
</template>
```

## 4. 状态判断逻辑

### 4.1 查看模式判断
```javascript
computed: {
  isViewMode() {
    // 从已完成tab进入
    const fromCompletedTab = this.$route.query.from === 'completed';
    
    // 根据typeState判断（如果有）
    if (this.infoData && this.infoData.typeState !== undefined) {
      return fromCompletedTab || this.infoData.typeState === 17 || this.infoData.typeState === 18;
    }
    
    // 根据applyState判断：8-通过, 3-不通过
    const isCompletedStatus = this.infoData && (this.infoData.applyState === 8 || this.infoData.applyState === 3);
    return fromCompletedTab || isCompletedStatus;
  },
}
```

## 5. 提交审核逻辑

### 5.1 表单验证
```javascript
sumBit() {
  if (!this.form.applyState) {
    this.$toast.fail("请选择审核结果");
    return;
  }
  if (!this.form.ruralDevelopmentRemarks) {
    this.$toast.fail("请输入审核备注");
    return;
  }

  // 构建请求数据
  const reqData = {
    id: this.infoData.id,
    applyState: this.form.applyState,
    applyDate: this.getCurrentDate(),
    ruralDevelopmentRemarks: this.form.ruralDevelopmentRemarks,
  };

  // 调用API提交
  this.submitAudit(reqData);
}
```

### 5.2 API调用
```javascript
async submitAudit(reqData) {
  try {
    const res = await updateStateH5(reqData);
    if (res.data.state === "success") {
      this.$toast.success(res.data.message || "审核提交成功");
      setTimeout(() => {
        this.$router.go(-1);
      }, 1500);
    } else {
      this.$toast.fail(res.data.message || "审核提交失败");
    }
  } catch (error) {
    console.error("审核提交失败：", error);
    this.$toast.fail("审核提交失败");
  }
}
```

## 6. 样式规范

### 6.1 查看地图按钮样式
```less
.map-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}
```

### 6.2 容器样式
```less
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 10px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
```

## 7. 数据初始化

### 7.1 页面挂载时的数据处理
```javascript
mounted() {
  // 获取路由传递的数据
  if (this.pageData?.pageCode === "当前页面标识") {
    this.infoData = this.pageData;
  } else {
    this.infoData = this.$route.params.data;
  }

  // 数据验证
  if (!this.infoData) {
    console.error("页面数据为空，请检查路由传参");
    this.$toast.fail("页面数据获取失败");
    return;
  }

  // 打印调试信息
  console.log("=== 页面数据详情 ===");
  console.log("完整数据对象：", this.infoData);
  console.log("关键字段检查：", {
    id: this.infoData.id,
    engineeringName: this.infoData.engineeringName,
    applyState: this.infoData.applyState,
    typeState: this.infoData.typeState
  });

  // 设置表单初始值（如果是查看模式）
  if (this.infoData.applyState) {
    this.form.applyState = this.infoData.applyState;
  }
  if (this.infoData.ruralDevelopmentRemarks || this.infoData.acceptedRemarks) {
    this.form.ruralDevelopmentRemarks = this.infoData.ruralDevelopmentRemarks || this.infoData.acceptedRemarks;
  }
},
```

## 8. 申请材料展示

### 8.1 固定分类显示
按照《申请材料展示规范.md》实现，包含7个固定分类：
- 行政许可申请书 (`applyImgs1`)
- 营业执照或法人证书 (`applyImgs2`)
- 授权委托书 (`applyImgs3`)
- 信用承诺书 (`applyImgs4`)
- 运输合同 (`applyImgs5`)
- 处置合同 (`applyImgs6`)
- 建筑垃圾产生信息表 (`applyImgs7`)

### 8.2 预览功能
- **图片文件**：点击查看大图，支持缩放和滑动
- **PDF文档**：新窗口直接预览
- **Office文档**：使用在线预览服务
- **其他文件**：直接下载

### 8.3 实现要点
```javascript
// 必须包含的方法
getAttachmentsByType(attachType) // 根据类型获取附件
previewFile(item) // 文件预览
isImageFile(filePath) // 判断是否为图片
getFileUrl(filePath) // 获取文件完整URL
```

## 9. 注意事项

1. **数据字段映射**：不同业务类型可能字段名略有不同，需要根据实际API返回调整
2. **状态值**：审核状态值可能因业务而异，需要确认具体的状态值定义
3. **API接口**：不同业务使用不同的API接口，需要导入正确的接口方法
4. **路由配置**：确保路由参数传递方式一致，使用 `params: { data: object }`
5. **权限控制**：根据用户角色和数据状态控制页面的可编辑性
6. **图片预览**：确保项目中已引入 Vant 的 ImagePreview 组件
7. **文件服务**：确保 `FILE_BASE_URL` 配置正确，文件服务可访问
