# Production Environment Restore Guide

## Quick Restore (Automated)
Run the automated restore script:
```bash
restore-production.bat
```

## Manual Restore Steps

### 1. Restore User Store Module
File: `src/store/modules/user.js`

**Change from:**
```javascript
const state = {
    userInfo: {
        userName: '测试用户',
        id: '10',
        // userName: '',
        // id: '',
    },
    volunteerId:'10'
};
```

**Change to:**
```javascript
const state = {
    userInfo: {
        // userName: '测试用户',
        // id: '10',
        userName: '',
        id: '',
    },
    volunteerId:''
};
```

### 2. Restore Environment Configuration
File: `.env.development`

**Remove or comment out:**
```
VUE_APP_SKIP_LOGIN = true
```

**Change to:**
```
# VUE_APP_SKIP_LOGIN = false
```

### 3. Restore Index View Login Logic
File: `src/views/IndexView.vue`

**Find the else block around line 181:**
```javascript
} else {
  // 开发环境跳过登录
  if (process.env.VUE_APP_SKIP_LOGIN === 'true') {
    console.log("开发环境，跳过登录检查，使用测试用户");
    // 使用store中预设的测试用户信息，无需额外设置
  } else {
    this.goLogin();
  }
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**Replace with:**
```javascript
} else {
  this.goLogin();
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

## Verification Steps

### 1. Check Configuration Files
- Ensure `.env.production` has correct API URL
- Verify `vue.config.js` proxy settings
- Confirm router mode is set to 'history'

### 2. Test OAuth Flow
1. Build and deploy application
2. Access application URL
3. Verify automatic redirect to OAuth login
4. Test complete login flow with valid credentials

### 3. API Endpoints
Ensure these endpoints are accessible:
- `/minapi/zwlogin/initLogin` - Initialize OAuth login
- `/minapi/zwlogin/toLogin` - Process OAuth callback
- `/site/v1/VolunteerApi/getVolunteerInfo` - Get user info

## OAuth Configuration Details

### Client Configuration
- Client ID: `9e5f943d-8638-4ec9-8ee3-142f2135eb82`
- OAuth Provider: `https://www.xtdzzw.cn/xtsso/authz/oauth/v20/authorize`
- Response Type: `code`
- Approval Prompt: `auto`

### App Keys (from request.js)
- APP_KEY: `e3e24540-10b9-4b93-82f1-0b6921c25ec7`
- App Secret: `03b78767b290fa42803ea5582fe39e68e00c74a0`
- Salt: `gmcx`

## Troubleshooting

### Common Issues
1. **Infinite redirect loop**: Check if OAuth callback URL matches deployment URL
2. **API proxy errors**: Verify backend server accessibility
3. **Token not persisting**: Check localStorage and session storage

### Debug Steps
1. Check browser console for errors
2. Verify network requests in DevTools
3. Check localStorage for TOKEN value
4. Verify Vuex store state

## Security Notes
- Never commit production credentials to version control
- Ensure OAuth redirect URLs are properly configured
- Validate all API endpoints are secured
- Test token expiration and refresh logic
