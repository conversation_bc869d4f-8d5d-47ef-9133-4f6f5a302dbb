<template>
  <div class="cell" :class="sty(cellData.state)" @click="handleClick">
    <div class="top flex-row">
      <div class="flex-row" style="align-items: center">
        <img :src="cellData.icon" class="imgsize1" alt="" />
        <p :class="fongSty(cellData.state)">
          {{ cellData.label }}
        </p>
      </div>
      <van-icon
        v-if="cellData.state === 1"
        size="18"
        name="success"
        color="#95d475"
      />
      <img v-else src="@/assets/images/right.png" class="imgsize2" alt="" />
    </div>
    <div class="xian"></div>
  </div>
</template>
<script>
export default {
  props: {
    cellData: {
      type: Object,
      default: () => ({}), // 默认启用一个空对象
    },
  },
  data() {
    return {};
  },
  computed: {},
  methods: {
    handleClick() {
      // 当点击按钮时触发自定义的点击事件
      this.$emit("click");
    },
    sty(e) {
      if (e === -1) {
        return "gary";
      }
    },
    fongSty(e) {
      if (e === -1) {
        return "color-gary";
      }
    },
  },
};
</script>

<style scoped lang="less">
.cell {
  border-radius: 15px;
  padding: 0 10px;
  box-sizing: border-box;
  margin-top: 4px;
  .top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    box-sizing: border-box;
    .imgsize1 {
      width: 24px;
      height: 24px;
    }
    .imgsize2 {
      width: 16px;
      height: 16px;
    }
    p {
      font-weight: 400;
      font-size: 14px;
      color: #112950;
      line-height: 24px;
      margin-left: 10px;
    }
    .color-bule {
      color: #112950 !important;
    }
    .color-gary {
      color: #909399 !important;
    }
    .color-green {
      color: #529b2e !important;
    }
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.gary {
  background-color: rgb(245, 247, 250);
}
.bule {
  background-color: #a0cfff;
}
.green {
  background-color: #b3e19d;
}
</style>
