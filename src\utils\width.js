//解决因为屏幕尺寸不一样而导致的money组件样式变动1
export function calculateWidth() {
    const screenWidth = window.innerWidth;
    const baseWidth = 375;
    const baseUnit = 42;
    const percentage = (screenWidth / baseWidth) * 100;
    const calculatedWidth = (baseUnit * percentage) / 100;
    return calculatedWidth;
}
//计算按钮的宽高根据手机比例
export function btnwidth(width) {
    const screenWidth = window.innerWidth;
    const baseWidth = 375;
    const baseUnit = width;
    const percentage = (screenWidth / baseWidth) * 100;
    const btnwidth = (baseUnit * percentage) / 100;
    return btnwidth;
}
export function btnheight(height) {
    const screenheight = window.innerHeight;
    const baseWidth = 812;
    const baseUnit = height;
    const percentage = (screenheight / baseWidth) * 100;
    const btnheight = (baseUnit * percentage) / 100;
    return btnheight;
}
