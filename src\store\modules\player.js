
const state = {
    isShowAliPlayer: true,
    AliUrlData:[],
    FlvUrlData:[],
    selectedUrlData:[],
    url:''
};
const mutations = {
    SET_isShowAliPlayer: (state, isShowAliPlayer) => {
        state.isShowAliPlayer = isShowAliPlayer;
    },
    SET_AliUrlData: (state, AliUrlData) => {
        state.AliUrlData = AliUrlData;
    },
    SET_FlvUrlData: (state, FlvUrlData) => {
        state.FlvUrlData = FlvUrlData;
    },
    SET_selectedUrlData: (state, selectedUrlData) => {
        state.selectedUrlData=selectedUrlData
    },
    SELECT_URL: (state, data) => {
        state.url=data
    },
    INIT:(state)=>{
        state.AliUrlData=[]
        state.FlvUrlData=[]
        state.selectedUrlData=[]
        state.selectedUrlData=[],
        state.url=''
    }
};
const actions = {
    //判断什么浏览器，然后根据浏览器来判断用什么播放器
    async getPlayer({ commit }) {
        // var userAgent = window.navigator.userAgent.toLowerCase();
        // var isIOS = /(iphone|ipad|ipod|ios)/i.test(userAgent);
        // var isAndroid = /android/i.test(userAgent);
        // var browser = navigator.userAgent.toLowerCase();
        // var isChrome = browser.includes("chrome");
        // var isSafari = browser.includes("safari") && !browser.includes("chrome");
        var isShowAliPlayer = true
        // if ((isIOS && isSafari) || (isAndroid && isChrome)) {
   
        // } else {
        //     isShowAliPlayer = false;
        //     console.log('使用flv播放器');
        // }
        isShowAliPlayer = true;
        commit('SET_isShowAliPlayer', isShowAliPlayer)
    },
    async getPlayerUrl({ commit, rootState ,dispatch}) {
        return new Promise((resolve)=>{
            var videoList = rootState?.handlegame?.gameInfo?.gameRoomConfig?.videoList;
                    if(videoList){
                        var AliUrlData = [];
                        var FlvUrlData = [];
                        for (var i = 0; i < videoList.length; i++) {
                            if (videoList[i].type === "ARTC" ||
                                videoList[i].type === "M3U8") {
                                AliUrlData.push(videoList[i]);
                            }
                            if (videoList[i].type === "FLV" ||
                            videoList[i].type === "RTMP") {
                                FlvUrlData.push(videoList[i]);
                            }
                        }
                        commit('SET_AliUrlData', AliUrlData);
                        commit('SET_FlvUrlData', FlvUrlData);
                        dispatch('handleUrl');
                        resolve()
                    }
                })
            },
async handleUrl({ state, commit }) {
    var selectedUrlData = state.selectedUrlData;  // 存储选中的 URL 数据
    var videoList = state.isShowAliPlayer ? state.AliUrlData : state.FlvUrlData;
    var select_url = ''
    if (selectedUrlData.length === 0) {
        // 首次进入，没有选中的 URL 数据
        for (var i = 0; i < videoList.length; i++) {
            if(state.isShowAliPlayer){
                if (videoList[i].type === "ARTC") {
                    selectedUrlData.push(videoList[i]);
                    select_url = videoList[i].url
                    break;
                }
              }
   
            if(!state.isShowAliPlayer){
                if (videoList[i].type === "FLV") {
                    selectedUrlData.push(videoList[i]);
                    select_url = videoList[i].url
                    break;
                }
              }
        }
    } else {
        // 已有选中的 URL 数据，尝试切换下一条
        var lastSelectedUrl = selectedUrlData[selectedUrlData.length - 1];
        var lastSelectedType = lastSelectedUrl.type;
        var nextType;

        if (lastSelectedType === "ARTC") {
            nextType = "M3U8";
        } 
        if (lastSelectedType === "M3U8") {
            state.isShowAliPlayer = false;
        } 
        if (lastSelectedType === "RTMP") {
            state.isShowAliPlayer = true;
        }
        if (lastSelectedType === "FLV") {
            nextType = "RTMP";
        }
        // 如果不是切换到 FLV 播放器，继续查找下一条类型的 URL
        if (nextType) {
            var nextUrl = videoList.find(urlData => urlData.type === nextType);
            if (nextUrl) {
                selectedUrlData.push(nextUrl);
                select_url = nextUrl.url
            }
        }
    }
    // 提交选中的 URL 数据
    commit('SET_selectedUrlData', selectedUrlData);
    commit('SELECT_URL', select_url);
}
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
