<template>
  <div id="app">
    <router-view />
    <!-- 该业务需求不需要客服组件 -->
    <kefu v-if="!isRouteBlacklisted && false"></kefu>
  </div>
</template>
<script>
//客服不显示的黑名单
export const routeBlacklist = [
  "/",
  "/login",
  "/tel",
  "/register",
  "/email",
  "/changepwd",
  "/bank/addbank",
  "/emailadd",
  //价格明细
  "/pro/listfees",
  "/health",
  "/findpwd",
];
import kefu from "@/components/com/KeFu.vue";
export default {
  name: "App",
  data() {
    return {
      isRouteBlacklisted: false,
    };
  },
  components: {
    kefu,
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },
  methods: {
    checkRoute() {
      this.isRouteBlacklisted = routeBlacklist.includes(this.$route.path);
    },
  },
  mounted() {
    this.checkRoute();
    //首先我们获得视口高度并将其乘以1%以获得1vh单位的值
    let vh = window.innerHeight * 0.01;
    // 然后，我们将——vh自定义属性中的值设置为文档的根
    document.documentElement.style.setProperty("--vh", `${vh}px`);
    // 我们监听resize事件 视图大小发生变化就重新计算1vh的值
    window.addEventListener("resize", () => {
      // 我们执行与前面相同的脚本
      let vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    });
  },
};
</script>
<style lang="less">
@import "./assets/css/normalize.css";
@import "./assets/css/main.css";
@import "./assets/css/common.css";
@import "./assets/css/cz.css";

.text-ellipsis {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
@media only screen and (max-width: 768px) {
  .mobile-view {
    width: 375px !important; /* 模拟 iPhone 6/7/8 宽度 */
    margin: 0; /* 去除默认边距 */
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
  }
}

// .time {
//   margin-left: 5px;
//   font-size: 14px;
//   color: #6b7089;
//   align-items: center;
//   .timedata {
//     width: 200px;
//     margin-left: 10px;
//     color: #353c5c !important;
//     font-weight: 600;
//   }
// }
.new-text-sty {
  font-weight: 600;
  font-size: 12px;
  color: #1989fa;
  line-height: 18px;
}
.uploadImgsize {
  width: 80px;
  height: 80px;
}
.state-box {
  position: absolute;
  right: 20px !important;
  border-radius: 10px !important;
  top: 10px !important;
}
</style>
