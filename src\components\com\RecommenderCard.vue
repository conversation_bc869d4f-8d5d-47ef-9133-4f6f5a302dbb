<template>
  <div :class="['box', state ? 'active' : 'noactive', 'flex-colum']">
    <div
      class="flex-colum"
      style="align-items: center; justify-content: space-between"
    >
      <div
        class="flex-row"
        style="
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin: 5px;
        "
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="friends-o"
            size="24px"
            :color="state ? '#E6A23C' : '#c45656'"
          />
          <p>Lv1: {{ cardData.userName }} ({{ cardData.volunteerId }})</p>
        </div>
      </div>
      <div
        class="flex-row"
        style="
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin: 5px;
        "
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="phone-o"
            size="24px"
            :color="state ? '#E6A23C' : '#c45656'"
          />
          <p>SĐT: {{ cardData.mobileNo }}</p>
        </div>
      </div>
      <div
        class="flex-row"
        style="
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin: 5px;
        "
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="clock-o"
            size="24px"
            :color="state ? '#E6A23C' : '#c45656'"
          />
          <p>{{ $formatIsoString(cardData.regTime) }}</p>
        </div>
      </div>
      <div
        class="flex-row"
        style="
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin: 5px;
        "
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="like-o"
            size="24px"
            :color="state ? '#E6A23C' : '#c45656'"
          />
          <p>Đại Lý: {{ userInfo.userName }}</p>
        </div>
      </div>
      <div
        class="flex-row"
        style="
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin: 5px;
        "
        v-if="isShow"
      >
        <div class="flex-row" style="align-items: center"></div>
        <div class="flex-row" style="align-items: center">
          <p style="color: #e6a23c">Xem thêm</p>
          <van-icon name="arrow" color="#e6a23c" size="14" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      state: true,
    };
  },
  props: {
    isShow: {
      type: Boolean,
      default: true,
    },
    cardData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
  },
  components: {},
  mounted() {},
  methods: {
    goActingUserInfo() {
      this.$router.push({ path: "/acitingindent", param: this.cardData });
    },
  },
};
</script>

<style scoped lang="less">
.activetips {
  padding: 3px 8px;
  color: white;
  font-size: 11px;
  box-sizing: border-box;
  border-radius: 0 12px 0 12px;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  background-color: var(--color-zhuti);
}
.box {
  padding: 8px 16px;
  position: relative;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 12px;
}
.active {
  background: #fff9f1;
  border: 1px solid #ffd495;
}
.noactive {
  background-color: #fab6b6;
  border: 1px solid #c45656;
}
p {
  font-weight: 400;
  font-size: 14px;
  color: #112950;
  line-height: 18px;
  margin-left: 10px;
}
.title {
  margin-bottom: 10px;
}
.c-bottom {
  width: 100%;
  margin-top: 10px;
  align-items: center;
  justify-content: space-between;
  .change {
    align-items: center;
    font-weight: 600;
    font-size: 18px;
    color: #e6a23c;
  }
}
</style>
