<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      title="产生地延期审核"
      :back="true"
    ></NavHeader>

    <!-- 申请信息概览 -->
    <div class="overview-section">
      <div class="overview-card">
        <div class="overview-header">
          <div class="site-name">{{ getDisplayTitle() }}</div>
          <div class="apply-time">{{ formatDateTime(infoData?.applyDate) }}</div>
        </div>
        <div class="overview-content">
          <div class="info-item">
            <span class="label">产生地地址</span>
            <span class="value">{{ getAddress() }}</span>
          </div>
          <div class="info-item">
            <span class="label">变更理由</span>
            <span class="value">{{ infoData?.content || '产生地' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <div class="section-title">
        <van-icon name="photo-o" size="18px" color="#1989fa" />
        <span>申请材料</span>
      </div>
      <div class="attachments-grid">
        <div
          v-for="(item, index) in infoData.attachment"
          :key="index"
          class="attachment-item"
          @click="previewFile(item)"
        >
          <div class="attachment-preview">
            <div class="file-icon" v-if="!isImageFile(item.filePath)">
              <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
            </div>
            <img
              v-else
              :src="getFileUrl(item.filePath)"
              alt="附件预览"
              class="attachment-image"
            />
          </div>
          <div class="attachment-name">{{ item.fileName || 'test.png' }}</div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <div class="audit-section">
      <div class="section-title">
        <van-icon name="edit" size="18px" color="#1989fa" />
        <span>申请操作</span>
      </div>

      <div class="audit-form">
        <!-- 审核结果 -->
        <div class="audit-item">
          <div class="audit-label">审核结果</div>
          <van-radio-group v-model="form.state" direction="horizontal">
            <van-radio :name="1" class="audit-radio">
              <template #icon="props">
                <div class="custom-radio" :class="{ active: props.checked }">
                  <van-icon name="success" v-if="props.checked" />
                </div>
              </template>
              同意
            </van-radio>
            <van-radio :name="2" class="audit-radio">
              <template #icon="props">
                <div class="custom-radio error" :class="{ active: props.checked }">
                  <van-icon name="cross" v-if="props.checked" />
                </div>
              </template>
              不同意
            </van-radio>
          </van-radio-group>
        </div>

        <!-- 延长有效期限 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">延长有效期限</div>
          
          <div class="date-picker-item">
            <div class="date-label">开始日期</div>
            <van-field
              v-model="form.startTime"
              readonly
              placeholder="2025-08-27"
              @click="showStartDatePicker = true"
              class="date-field"
            >
              <template #right-icon>
                <van-icon name="calendar-o" color="#1989fa" />
              </template>
            </van-field>
          </div>

          <div class="date-picker-item">
            <div class="date-label">结束日期</div>
            <van-field
              v-model="form.endTime"
              readonly
              placeholder="2025-08-31"
              @click="showEndDatePicker = true"
              class="date-field"
            >
              <template #right-icon>
                <van-icon name="calendar-o" color="#1989fa" />
              </template>
            </van-field>
          </div>
        </div>

        <!-- 审核备注 -->
        <div class="audit-item">
          <div class="audit-label">审核备注</div>
          <van-field
            v-model="form.remarks"
            type="textarea"
            rows="4"
            maxlength="100"
            placeholder="哈哈哈"
            show-word-limit
            class="remarks-field"
          />
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button
          type="primary"
          size="large"
          block
          @click="submitAudit()"
          :loading="isSubmitting"
          class="submit-btn"
        >
          {{ isSubmitting ? '提交中...' : '提交审核' }}
        </van-button>
      </div>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentStartDate"
        type="date"
        title="选择开始日期"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentEndDate"
        type="date"
        title="选择结束日期"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { stationAuditAuditH5 } from "@/api/config";

export default {
  data() {
    return {
      form: {
        state: "",
        remarks: "",
        startTime: "",
        endTime: "",
      },
      showStartDatePicker: false,
      showEndDatePicker: false,
      currentStartDate: new Date(),
      currentEndDate: new Date(),
      isSubmitting: false,
      infoData: {},
    };
  },
  computed: {
    hasAttachments() {
      return this.infoData && this.infoData.attachment && this.infoData.attachment.length > 0;
    },
  },
  mounted() {
    this.infoData = this.$route.params.data || {};
    console.log("接收到的数据：", this.infoData);
    
    // 处理附件数据
    if (this.infoData.attachment && typeof this.infoData.attachment === "string") {
      try {
        this.infoData.attachment = JSON.parse(this.infoData.attachment);
      } catch (e) {
        console.error("解析附件数据出错：", e);
        this.infoData.attachment = [];
      }
    }
  },
  methods: {
    getDisplayTitle() {
      return this.infoData?.constructionSite?.siteName || 
             this.infoData?.siteName || 
             '测试20250828T024-10';
    },
    
    getAddress() {
      return this.infoData?.constructionSite?.coordinateInfo || 
             this.infoData?.coordinateInfo || 
             '岳塘区布塘街道东二环路辅路';
    },

    formatDateTime(isoString) {
      if (!isoString) return '2025-08-29 08:00';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return '2025-08-29 08:00';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '2025-08-29 08:00';
      }
    },

    // 日期选择器确认
    onStartDateConfirm(date) {
      this.form.startTime = this.formatDate(date);
      this.showStartDatePicker = false;
    },

    onEndDateConfirm(date) {
      this.form.endTime = this.formatDate(date);
      this.showEndDatePicker = false;
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 文件相关方法
    isImageFile(filePath) {
      if (!filePath) return false;
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      return imageExtensions.some(ext => filePath.toLowerCase().includes(ext));
    },

    getFileIcon(filePath) {
      if (!filePath) return 'description';
      const ext = filePath.toLowerCase();
      if (ext.includes('.pdf')) return 'description';
      if (ext.includes('.doc') || ext.includes('.docx')) return 'description';
      if (ext.includes('.xls') || ext.includes('.xlsx')) return 'description';
      return 'description';
    },

    getFileIconColor(filePath) {
      if (!filePath) return '#666';
      const ext = filePath.toLowerCase();
      if (ext.includes('.pdf')) return '#ff4757';
      if (ext.includes('.doc') || ext.includes('.docx')) return '#3742fa';
      if (ext.includes('.xls') || ext.includes('.xlsx')) return '#2ed573';
      return '#666';
    },

    getFileUrl(filePath) {
      if (!filePath) return '';
      return `http://************:47025/upload/${filePath}`;
    },

    previewFile(item) {
      if (this.isImageFile(item.filePath)) {
        // 图片预览
        this.$imagePreview([this.getFileUrl(item.filePath)]);
      } else {
        // 其他文件类型的处理
        window.open(this.getFileUrl(item.filePath), '_blank');
      }
    },

    // 表单验证
    validateForm() {
      if (!this.form.state) {
        this.$toast.fail("请选择审核结果");
        return false;
      }
      if (!this.form.remarks) {
        this.$toast.fail("请输入审核备注");
        return false;
      }
      
      // 如果同意延期，需要验证时间
      if (this.form.state === 1) {
        if (!this.form.startTime) {
          this.$toast.fail("请选择延长开始时间");
          return false;
        }
        if (!this.form.endTime) {
          this.$toast.fail("请选择延长结束时间");
          return false;
        }
        
        // 验证时间逻辑
        if (new Date(this.form.startTime) >= new Date(this.form.endTime)) {
          this.$toast.fail("结束时间必须大于开始时间");
          return false;
        }
      }
      
      return true;
    },

    // 提交审核
    async submitAudit() {
      if (!this.validateForm()) return;

      const reqData = {
        id: this.infoData.id,
        state: this.form.state,
        remarks: this.form.remarks,
      };

      // 只有同意时才传递时间参数
      if (this.form.state === 1) {
        reqData.startTime = this.form.startTime;
        reqData.endTime = this.form.endTime;
      }

      this.isSubmitting = true;
      try {
        const res = await stationAuditAuditH5(reqData);
        if (res.data.success || res.data.state === "success") {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => this.$router.go(-1), 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  min-height: 100vh;
  background: #f8faff;
  padding-bottom: 20px;
}

.overview-section {
  padding: 16px;
}

.overview-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .site-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .apply-time {
    font-size: 14px;
    color: #666;
  }
}

.overview-content {
  .info-item {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      font-size: 14px;
      color: #666;
      flex-shrink: 0;
    }

    .value {
      font-size: 14px;
      color: #333;
      flex: 1;
    }
  }
}

.attachments-section {
  padding: 0 16px 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;

  span {
    margin-left: 8px;
  }
}

.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.attachment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .attachment-preview {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    margin-bottom: 8px;

    .attachment-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .file-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .attachment-name {
    font-size: 12px;
    color: #666;
    text-align: center;
    word-break: break-all;
    line-height: 1.2;
  }
}

.audit-section {
  padding: 0 16px;
}

.audit-form {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}

.audit-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.audit-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.audit-radio {
  margin-right: 24px;

  .custom-radio {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;

    &.active {
      border-color: #52c41a;
      background: #52c41a;
      color: white;
    }

    &.error.active {
      border-color: #ff4d4f;
      background: #ff4d4f;
    }
  }
}

.date-picker-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .date-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .date-field {
    background: #f5f7fa;
    border-radius: 8px;

    :deep(.van-field__control) {
      background: transparent;
    }
  }
}

.remarks-field {
  background: #f5f7fa;
  border-radius: 8px;

  :deep(.van-field__control) {
    background: transparent;
    min-height: 100px;
  }
}

.submit-section {
  padding: 0 16px;
}

.submit-btn {
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1989fa 0%, #1890ff 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
}
</style>
