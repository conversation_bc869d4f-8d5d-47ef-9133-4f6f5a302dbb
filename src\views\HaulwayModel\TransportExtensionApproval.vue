<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      title="审核详情"
      :back="true"
    ></NavHeader>

    <!-- 申请信息概览 -->
    <div class="overview-section">
      <div class="overview-card">
        <div class="overview-header">
          <div class="site-name">{{ getDisplayTitle() }}</div>
          <div class="apply-time">{{ formatDateTime(infoData?.applyDate) }}</div>
        </div>
        <div class="overview-content">
          <div class="info-item">
            <span class="label">运输企业</span>
            <span class="value">{{ infoData?.transportCompany?.companyName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">原有效期</span>
            <span class="value">{{ getOriginalValidityPeriod() }}</span>
          </div>
          <div class="info-item">
            <span class="label">变更理由</span>
            <span class="value">{{ infoData?.content || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <div class="section-title">
        <van-icon name="photo-o" size="18px" color="#1989fa" />
        <span>申请材料</span>
      </div>

      <!-- 所有文件 - 每行一个 -->
      <div class="all-attachments">
        <div
          v-for="(item, index) in infoData.attachment"
          :key="index"
          class="attachment-row"
          @click="previewFile(item)"
        >
          <!-- 图片文件显示 -->
          <div v-if="isImageFile(item.filePath)" class="image-row">
            <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
            <div class="attachment-title">{{ item.displayTitle }}</div>
          </div>

          <!-- 非图片文件显示 -->
          <div v-else class="file-row">
            <div class="file-info">
              <div class="file-icon">
                <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
              </div>
              <div class="file-details">
                <div class="file-name">{{ item.displayTitle }}</div>
                <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
              </div>
              <div class="file-action">
                <van-icon name="eye-o" size="20px" color="#1989fa" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <div class="audit-section" v-if="!isReadOnly">
      <div class="section-title">
        <van-icon name="completed" size="18px" color="#1989fa" />
        <span>审核操作</span>
      </div>

      <!-- 审核结果 -->
      <div class="audit-card">
        <div class="audit-item">
          <div class="audit-label">审核结果</div>
          <div class="simple-radio-group">
            <van-radio-group v-model="form.state" direction="horizontal">
              <van-radio :name="1" class="radio-option">同意</van-radio>
              <van-radio :name="2" class="radio-option">不同意</van-radio>
            </van-radio-group>
          </div>
        </div>

        <!-- 延长有效期限 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">延长有效期限</div>
          <div class="date-range-container">
            <div class="date-picker-row">
              <span class="date-label">开始日期</span>
              <div class="date-input-field" @click="showStartDatePicker = true">
                <span class="date-text">{{ form.startTime || '请选择' }}</span>
                <van-icon name="calendar-o" size="16px" color="#1989fa" />
              </div>
            </div>
            <div class="date-picker-row">
              <span class="date-label">结束日期</span>
              <div class="date-input-field" @click="showEndDatePicker = true">
                <span class="date-text">{{ form.endTime || '请选择' }}</span>
                <van-icon name="calendar-o" size="16px" color="#1989fa" />
              </div>
            </div>
          </div>
        </div>

        <!-- 审核备注 -->
        <div class="audit-item">
          <div class="audit-label">审核备注</div>
          <van-field
            v-model="form.remarks"
            type="textarea"
            rows="4"
            maxlength="100"
            placeholder="请输入审核备注..."
            show-word-limit
            class="remarks-field"
          />
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button
          type="primary"
          size="large"
          block
          @click="sumBit()"
          :loading="isSubmitting"
          class="submit-btn"
        >
          {{ isSubmitting ? '提交中...' : '提交审核' }}
        </van-button>
      </div>
    </div>

    <!-- 已完成状态的审核结果展示 -->
    <div class="audit-result-section" v-if="isReadOnly">
      <div class="section-title">
        <van-icon name="completed" size="18px" color="#1989fa" />
        <span>审核结果</span>
      </div>

      <div class="audit-card">
        <!-- 审核状态 -->
        <div class="audit-item">
          <div class="audit-label">审核状态</div>
          <div class="audit-value">
            <span :class="['status-tag', infoData.state === 1 ? 'status-approved' : 'status-rejected']">
              {{ infoData.state === 1 ? '已同意' : '已拒绝' }}
            </span>
          </div>
        </div>

        <!-- 延长有效期限（已完成状态显示） -->
        <div class="audit-item" v-if="infoData.state === 1 && (infoData.startTime || infoData.endTime)">
          <div class="audit-label">延长有效期限</div>
          <div class="audit-value">
            {{ infoData.startTime || '-' }} 至 {{ infoData.endTime || '-' }}
          </div>
        </div>

        <!-- 审核备注 -->
        <div class="audit-item" v-if="infoData.remarks">
          <div class="audit-label">审核备注</div>
          <div class="audit-value">{{ infoData.remarks }}</div>
        </div>

        <!-- 审核时间 -->
        <div class="audit-item" v-if="infoData.auditTime">
          <div class="audit-label">审核时间</div>
          <div class="audit-value">{{ formatDateTime(infoData.auditTime) }}</div>
        </div>
      </div>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentStartDate"
        type="date"
        title="选择开始日期"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentEndDate"
        type="date"
        title="选择结束日期"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { stationAuditAuditH5 } from "@/api/config";
import { FILE_BASE_URL } from "@/utils/globalConstants";

export default {
  name: "TransportExtensionApproval",
  data() {
    return {
      form: {
        state: "",
        remarks: "",
        startTime: "",
        endTime: "",
      },
      showStartDatePicker: false,
      showEndDatePicker: false,
      currentStartDate: new Date(),
      currentEndDate: new Date(),
      isSubmitting: false,
      isReadOnly: false, // 是否为只读模式
      rules: {
        state: [{ required: true, message: "请选择审核结果" }],
        remarks: [{ required: true, message: "请输入审核备注" }],
        startTime: [{ required: true, message: "请选择延长开始时间" }],
        endTime: [{ required: true, message: "请选择延长结束时间" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {
    hasAttachments() {
      return this.infoData && this.infoData.attachment && this.infoData.attachment.length > 0;
    },
  },
  created() {
    // 初始化逻辑可以在这里添加
  },
  mounted() {
    console.log("路由参数：", this.$route.params);
    
    // 解析路由参数
    let data = this.$route.params.data;
    if (typeof data === 'string') {
      try {
        this.infoData = JSON.parse(decodeURIComponent(data));
      } catch (e) {
        console.error("解析路由参数失败：", e);
        this.$toast.fail("页面参数错误");
        return;
      }
    } else {
      this.infoData = data;
    }

    console.log("解析后的数据：", this.infoData);

    // 判断是否为只读模式（已完成状态）
    this.isReadOnly = this.infoData.state === 1 || this.infoData.state === 2;

    // 处理附件数据
    const att = this.infoData.attachment;
    if (typeof att === "string") {
      try {
        this.infoData.attachment = JSON.parse(att);
      } catch (e) {
        console.error("解析附件 JSON 出错：", e);
      }
    }
  },

  methods: {
    getDisplayTitle() {
      return this.infoData?.transportCompany?.companyName ||
             this.infoData?.administrativeApply?.engineeringName ||
             '运输企业延期申请';
    },

    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return '-';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    getOriginalValidityPeriod() {
      if (!this.infoData || !this.infoData.transportCompany) return '-';
      const company = this.infoData.transportCompany;
      const start = company.startworkDate;
      const end = company.endworkDate;
      if (start && end) {
        return `${start} 至 ${end}`;
      }
      return '-';
    },

    getNewValidityPeriod() {
      if (!this.infoData) return '-';
      const start = this.infoData.startTime;
      const end = this.infoData.endTime;
      if (start && end) {
        return `${start} 至 ${end}`;
      }
      return '-';
    },

    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      return `${FILE_BASE_URL}${filePath}`;
    },

    previewImage(url) {
      this.$imagePreview([url]);
    },

    // 判断是否为图片文件
    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    // 获取文件图标
    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf':
          return 'description';
        case '.doc':
        case '.docx':
          return 'description';
        case '.xls':
        case '.xlsx':
          return 'description';
        case '.ppt':
        case '.pptx':
          return 'description';
        default:
          return 'description';
      }
    },

    // 获取文件图标颜色
    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf':
          return '#ff4d4f';
        case '.doc':
        case '.docx':
          return '#1890ff';
        case '.xls':
        case '.xlsx':
          return '#52c41a';
        case '.ppt':
        case '.pptx':
          return '#fa8c16';
        default:
          return '#8c8c8c';
      }
    },

    // 获取文件类型文本
    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PowerPoint演示文稿';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    // 预览文件
    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        // 图片预览
        this.$imagePreview([fileUrl]);
      } else {
        // 其他文件类型，在新窗口打开
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));

        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          // 使用在线预览服务或直接下载
          this.openFilePreview(fileUrl, extension);
        } else {
          // 直接下载
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    // 打开文件预览
    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        // PDF可以直接在浏览器中预览
        window.open(fileUrl, '_blank');
      } else {
        // Office文档使用在线预览服务
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    // 下载文件
    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    onStartDateConfirm(date) {
      this.form.startTime = this.formatDate(date);
      this.showStartDatePicker = false;
    },

    onEndDateConfirm(date) {
      this.form.endTime = this.formatDate(date);
      this.showEndDatePicker = false;
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    sumBit() {
      console.log("提交的数据：", this.form);

      // 验证必填字段
      if (!this.form.state) {
        this.$toast.fail("请选择审核结果");
        return;
      }
      if (!this.form.remarks) {
        this.$toast.fail("请输入审核备注");
        return;
      }

      // 如果同意延期，需要验证时间
      if (this.form.state === 1) {
        if (!this.form.startTime) {
          this.$toast.fail("请选择延长开始时间");
          return;
        }
        if (!this.form.endTime) {
          this.$toast.fail("请选择延长结束时间");
          return;
        }

        // 验证时间逻辑
        if (new Date(this.form.startTime) >= new Date(this.form.endTime)) {
          this.$toast.fail("结束时间必须大于开始时间");
          return;
        }
      }

      const reqData = {
        id: this.infoData.id,
        state: this.form.state,
        remarks: this.form.remarks,
      };

      // 只有同意时才传递时间参数
      if (this.form.state === 1) {
        reqData.startTime = this.form.startTime;
        reqData.endTime = this.form.endTime;
      }

      console.log("提交的请求数据：", reqData);

      this.isSubmitting = true;
      stationAuditAuditH5(reqData).then((res) => {
        this.isSubmitting = false;
        if (res.data.success || res.data.state === "success") {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      }).catch((error) => {
        this.isSubmitting = false;
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  padding-bottom: 20px;
}

// 概览部分
.overview-section {
  padding: 16px;

  .overview-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .site-name {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        flex: 1;
        line-height: 24px;
      }

      .apply-time {
        font-size: 12px;
        color: #8c8c8c;
        white-space: nowrap;
        margin-left: 12px;
      }
    }

    .overview-content {
      .info-item {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: #8c8c8c;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          font-size: 14px;
          color: #262626;
          flex: 1;
          line-height: 20px;
        }
      }
    }
  }
}

// 通用标题样式
.section-title {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 0;

  span {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-left: 8px;
  }
}

// 附件部分
.attachments-section {
  padding: 0 16px 16px;

  // 所有附件 - 每行一个
  .all-attachments {
    .attachment-row {
      margin-bottom: 16px;
      cursor: pointer;

      // 图片行样式
      .image-row {
        img {
          width: 100%;
          max-height: 300px;
          object-fit: contain;
          border-radius: 8px;
          background: #f5f5f5;
          border: 1px solid #e6f4ff;
        }

        .attachment-title {
          font-size: 14px;
          color: #8c8c8c;
          text-align: center;
          margin-top: 8px;
          line-height: 20px;
        }
      }

      // 文件行样式
      .file-row {
        .file-info {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #ffffff;
          border: 1px solid #e6f4ff;
          border-radius: 8px;
          transition: all 0.3s;

          &:hover {
            border-color: #1989fa;
            box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
          }

          .file-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8faff;
            border-radius: 8px;
            margin-right: 12px;
          }

          .file-details {
            flex: 1;

            .file-name {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              margin-bottom: 4px;
              line-height: 20px;
            }

            .file-type {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 16px;
            }
          }

          .file-action {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 审核部分
.audit-section {
  .audit-card {
    background: #ffffff;
    border-radius: 12px;
    margin: 0 16px 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .audit-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .audit-label {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 12px;
      }
    }
  }
}

// 简化单选按钮样式
.simple-radio-group {
  .radio-option {
    margin-right: 24px;
    font-size: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 新的日期选择器样式
.date-range-container {
  .date-picker-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .date-label {
      font-size: 14px;
      color: #262626;
      width: 80px;
      flex-shrink: 0;
    }

    .date-input-field {
      flex: 1;
      padding: 12px 16px;
      background: #f8faff;
      border: 1px solid #e6f4ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #1989fa;
      }

      .date-text {
        font-size: 14px;
        color: #262626;
      }
    }
  }
}

// 备注字段样式
.remarks-field {
  /deep/ .van-field__control {
    background: #f8faff;
    border: 1px solid #e6f4ff;
    border-radius: 8px;
    padding: 12px;
    min-height: 80px;
  }
}

// 提交按钮
.submit-section {
  padding: 0 16px;

  .submit-btn {
    height: 48px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
  }
}

// 只读模式样式
.audit-result-section {
  padding: 0 16px 16px;

  .audit-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .audit-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .audit-label {
        font-size: 14px;
        color: #262626;
        font-weight: 500;
        margin-bottom: 12px;
        line-height: 20px;
      }

      .audit-value {
        font-size: 14px;
        color: #595959;
        line-height: 20px;
      }

      .status-tag {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;

        &.status-approved {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.status-rejected {
          background: #fff2f0;
          color: #ff4d4f;
          border: 1px solid #ffccc7;
        }
      }
    }
  }
}
</style>

