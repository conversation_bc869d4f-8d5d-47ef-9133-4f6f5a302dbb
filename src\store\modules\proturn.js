import { getDrugProInfo, getProOrderAttaList } from "@/api/pro";
import { getNewVolunteerAddr } from "@/api/material";
import deepClone from "lodash/cloneDeep"; // 使用 lodash 库进行深拷贝

const state = {
  //项目一开始所有的数据以及初始状态都是-1
  resetData: [
    {
      cn: "试药合同",
      code: "contractPdf",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Hợp Đồng Tình Nguyện",
      pdfLink: "",
      dataType: 'ATTA_TYPE_01'
    },
    {
      cn: "知情同意书",
      code: "informedPdf",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Giấy Cam Kết Đồng Ý",
      dataType: 'ATTA_TYPE_02'
    },
    {
      cn: "保险单",
      code: "insurancePdf",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Hợp Đ<PERSON>",
      dataType: 'ATTA_TYPE_03'
    },
    {
      cn: "个人健康调查表",
      code: "HealthReport",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Tài Liệu Khảo Sát Sức Khỏe",
      dataType: 'ATTA_TYPE_04'
    },
    {
      cn: "个人资料签名",
      code: "userInfo",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Xác nhận thông tin cá nhân",
      dataType: 'ATTA_TYPE_05'
    },
    {
      cn: "CCCD正反面",
      code: "sfzInfo",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Hình Ảnh CCCD",
      dataType: 'ATTA_TYPE_06'
    },
    {
      cn: "药品介绍",
      code: "drugInfoPdf",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Giới Thiệu Thuốc",
      dataType: 'ATTA_TYPE_07'
    },
    {
      cn: "药品认证书",
      code: "drugInfoPdf",
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Chứng Nhận Thuốc",
      dataType: 'ATTA_TYPE_08'
    },
    {
      cn: "药品不良症报告",
      code: 'adversePdf',
      state: -1,
      icon: require("@/assets/images/pdf.png"),
      label: "Báo Cáo Bệnh Có Hại Của Thuốc",
      dataType: 'ATTA_TYPE_09'
    },
  ],
  cellData: '',
  proData: '',
  orderData: '',
  idData: '',
  //当前选中的数据
  item: '',
  //他选择的协议
  address: '',
  //他选择的协议
  sumBitData: {},
  isReadingState: false,
  isSignState: false,
  isPermissionsApply: false,
  //确认地址的弹窗
  show: false,
  recordType: ''
};
const mutations = {
  //重置cellData单独
  reset_newcellData: (state) => {
    state.cellData = deepClone(state.resetData); // 使用深拷贝
  },
  reset_cellData: (state) => {
    state.cellData = deepClone(state.resetData); // 使用深拷贝
    state.proData = '';
    state.isPermissionsApply = false
  },
  //重置签名的这一类
  reset_signData: (state) => {
    state.isReadingState = false;
    state.isSignState = false;
    state.sumBitData = {};
    state.item = '';
  },
  set_Id: (state, e) => {
    state.idData = e;
  },
  set_isPermissionsApply: (state, e) => {
    state.isPermissionsApply = e;
  },
  set_recordType: (state, e) => {
    state.recordType = e;
  },
  set_show: (state, e) => {
    state.show = e;
  },
  set_address: (state, e) => {
    state.address = e;
  },
  set_isReadingState: (state, e) => {
    state.isReadingState = e;
  },
  set_item: (state, e) => {
    state.item = e;
  },
  set_url: (state, e) => {
    state.sumBitData.url = e;
    state.isSignState = true;
  },
  //给它赋值
  SET_cellData: (state, e) => {
    state.cellData = e;
  },
  //载入项目数据
  SET_proData: (state, e) => {
    state.proData = e;
  },
  //载入订单数据
  SET_orderData: (state, e) => {
    state.orderData = e;
  },
};
function matchAndUpdatePdfLinks(proData, resetData) {
  // 遍历 resetData 数组
  resetData.forEach(item => {
    // 获取 proData 中与 item.code 相对应的属性值
    if (proData[item.code]) {
      // 更新 pdfLink
      item.pdfLink = proData[item.code];
    } else {
      item.pdfLink = ''
    }
  });
  return resetData;
}
function removeDuplicateDataTypes(arr) {
  // 使用一个对象来跟踪已处理过的 dataType
  const dataTypeSet = new Set();

  // 使用 reduce 方法来迭代数组并过滤掉重复的 dataType
  return arr.reduce((acc, current) => {
    if (!dataTypeSet.has(current.dataType)) {
      dataTypeSet.add(current.dataType);
      acc.push(current);
    }
    return acc;
  }, []);
}
function matchAndUpdatePdfState(proData, resetData) {
  // 遍历 resetData 数组
  proData.forEach(item => {
    resetData.forEach(ctem => {
      //更新状态
      if (item.dataType === ctem.dataType) {
        ctem.state = 1;
      }
    });
  });
  return resetData;
}
const actions = {
  //拿去项目详情页面数据
  resetCellData({ commit }) {
    commit('reset_cellData');
  },
  async getProInfo({ commit }, e) {
    var reqData = {
      projectId: e.projectId,
    };
    const drugProInfoRes = await getDrugProInfo(reqData);
    if (drugProInfoRes.data.state === 0) {
      var proData = drugProInfoRes.data.data;
      commit('SET_proData', proData);
      return drugProInfoRes
    }
  },
  //拿去list数据
  async getAttaList({ commit, state }) {
    if (state.idData === '') {
      return
    }
    var reqData2 = {
      projectId: state
        .idData.projectId,
      orderNo: state
        .idData.orderNo,
    };
    var pdfData = [];
    var newData = matchAndUpdatePdfLinks(state.proData, deepClone(state.cellData)); // 使用深拷贝
    const proOrderAttaListRes = await getProOrderAttaList(reqData2);
    if (proOrderAttaListRes.data.state === 0) {
      pdfData = removeDuplicateDataTypes(proOrderAttaListRes.data.data);
      if (pdfData.length === 0) {
        newData[0].state = 0;
        // 如果里面没有数据代表项目开始，从第一条开始
      } else {
        var c = pdfData.length;
        newData = matchAndUpdatePdfState(pdfData, newData);
        //确保后面一个的窗口打开
        if (pdfData.length < 9) {
          newData[c].state = 0;
        } else {
          state.isPermissionsApply = true
        }
      }
    }
    commit('SET_cellData', newData);
  },
  //暂时废弃因为发现youbug
  updateState({ state }) {
    var array = state.cellData;
    var i = state.item;
    // 找到匹配的元素索引
    const index = array.findIndex(element => element.code === i.code);
    // 如果找到了匹配的元素
    if (index !== -1) {
      // 将匹配元素的 state 改为 1
      array[index].state = 1;
      // 如果后面还有元素，则将下一个元素的 state 改为 0
      if (index < array.length - 1) {
        array[index + 1].state = 0;
      }
    }
  },
  getAdfalueAddress({ commit, rootState }) {
    var reqData = {
      volunteerId: rootState.user.userInfo.volunteerId
    }
    getNewVolunteerAddr(reqData).then(res => {
      if (res.data.state === 0) {
        commit('set_address', res.data.data);
      }
    })

  }

};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
