<template>
  <div class="loading-box" v-if="Loading">
    <van-loading :color="color" size="24px" vertical>{{ tips }} </van-loading>
  </div>
</template>
<script>
export default {
  props: {
    tips: {
      type: String,
      default: "Đang tải...",
      //   加载中,
    },
    Loading: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      default: "#0065ff", // 默认颜色为蓝色
    },
  },
  methods: {},
};
</script>

<style scoped lang="less">
.loading-box {
  width: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
