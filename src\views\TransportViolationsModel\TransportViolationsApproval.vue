<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      title="违规报警数据审核"
      :back="true"
    ></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.rule_name"
              name="content"
              label="违规名称："
              :readonly="true"
            />
            <van-field
              :value="infoData?.agent_name"
              name="content"
              label="运输企业:"
              :readonly="true"
            />

            <van-field
              :value="infoData.content"
              name="content"
              label="违规内容:"
              :readonly="true"
            />
            <van-field
              :value="infoData.plate_no"
              name="content"
              label="车牌信息:"
              :readonly="true"
            />
            <van-field
              label-width="120px"
              clickable
              name="startTime"
              :value="infoData.start_time"
              label="报警开始时间："
              :readonly="true"
              placeholder="请选择报警开始时间"
            />
            <van-field
              label-width="120px"
              clickable
              name="endTime"
              :readonly="true"
              :value="infoData.end_time"
              label="报警结束时间："
              placeholder="请选择报警结束时间"
            />
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader
      left="附件查阅"
      v-if="infoData.attachments?.length !== 0"
    ></LabelHeader>
    <div class="information" v-if="infoData.attachments?.length !== 0">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              v-for="(item, index) in infoData.attachments"
              :key="index"
              :name="item.attachType"
              :label="item.displayTitle"
              label-width="120px"
            >
              <template #input>
                <img
                  class="uploadImgsize"
                  :src="`http://113.87.160.8:47025/upload/${item.filePath}`"
                  alt=""
                />
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </div>
    <LabelHeader left="审核情况"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- <van-popup position="bottom" v-model="showCalendar3">
              <van-datetime-picker
                type="datetime"
                v-model="form.illegalDate"
                @confirm="onConfirm3"
            /></van-popup> -->
            <van-field
              label-width="120px"
              v-model="form.deductionNum"
              name="扣除分值"
              type="number"
              label="扣除分值"
              placeholder="扣除分值"
            />
            <van-field name="applyState" label="上传附件：" label-width="120px">
              <template #input>
                <van-uploader
                  :deletable="false"
                  v-model="imgPatrolList"
                  :max-count="1"
                  :after-read="(file) => afterRead(file, 'front')"
                />
              </template>
            </van-field>
          </van-form>
          <div class="btn-box">
            <yButton title="提交" :top150="true" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { addCheckH5, upload } from "@/api/config";
export default {
  data() {
    return {
      form: {
        agentName: "",
        violationTime: "",
        deductionNum: "",
        loadAttachment: "",
        isAttachmentModify: false,
      },
      imgPatrolList: [],
      showCalendar: false,
      showCalendar2: false,
      showCalendar3: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: "",
      value: "",
    };
  },
  computed: {},
  created() {
    // this.init();
  },
  computed: {},
  mounted() {
    this.infoData = this.$route.params.data;
  },

  methods: {
    afterRead(fileObj, fileType) {
      const file = fileObj.file;
      const formData = new FormData();
      formData.append("file", file);
      upload(formData).then((res) => {
        if (res.data.state === "success") {
          this.form.imgPatrolList = res.data.data;
          var url = `http://113.87.160.8:47025/upload/${res.data.data}`;
          this.imgPatrolList.push(url);
          this.form.loadAttachment = `[{"displayTitle":"违规依据附件1","attachType":"onlineAlarmExamine","relatedId":null,"attachMark":1,"filePath":"${res.data.data}"}]`;
          this.$toast({
            message: "图片上传成功",
            duration: 2000,
          });
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始
      const day = String(now.getDate()).padStart(2, "0");
      const hour = String(now.getHours()).padStart(2, "0");
      const minute = String(now.getMinutes()).padStart(2, "0");
      const second = String(now.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    sumBit() {
      console.log("提交的数据：", this.form);
      if (this.form.startTime !== "") {
        var reqData = {
          ...this.form,
          illegalDate: this.getCurrentTime(),
          id: this.infoData.id,
          alarmId: this.infoData.alarm_id,
          agentId: this.infoData.agent_id,
          agentName: this.infoData?.agent_name,
          checkNormId: this.infoData?.check_norm_id,
          alarmZtcId: this.infoData?.alarm_id,
          alarmType: this.infoData?.alarm_type,
          id: this.infoData?.id,
          // truckId: this.infoData?.truck_id,
          // agentCheckType: this?.infoData.agent_check_type,
        };
        addCheckH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}
</style>
