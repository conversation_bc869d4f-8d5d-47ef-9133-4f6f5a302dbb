<template>
  <div class="card">
    <div class="state-box" :class="statusClass">
      {{ statusText }}
    </div>
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData?.userName }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="friends-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>姓名： </i><i class="timedata">{{ indentData?.contract1 }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>手机号码: </i><i class="timedata">{{ indentData?.mobilePhone }}</i>
        </div>
      </div>
      <div></div>
    </div>
    <!-- //年龄与人数 -->
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="notes-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>管辖区: </i
          ><i class="timedata">{{
            indentData?.addressDistrict?.fullAreaName
          }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="new-text-sty">申请日期：{{ indentData.createDate }}</div>
      <div class="flex-row pre-c" @click="handleClick(indentData)">
        <p>去审核</p>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "IndentCardRegister",
  data() {
    return {
      isDialogShow: true,
    };
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}), // 默认启用一个空对象
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    statusClass() {
      return "success";
    },
    statusText() {
      return "企业注册";
    },
  },
  methods: {
    formatDateTime(isoString) {
      // 将 ISO 8601 字符串转换为 Date 对象
      const date = new Date(isoString);
      // 检查是否为有效日期
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      // 获取年、月、日、小时、分钟和秒
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      // 拼接成所需格式
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    },
    handleClick(e) {
      this.$router.push({
        name: "HaulwayTransportRegisterApproval",
        params: { data: e },
      });
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.state-box {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
  
  &.success {
    background: #07c160;
  }
  
  &.error {
    background: #ee0a24;
  }
  
  &.pending {
    background: #ff976a;
  }
}

.top {
  margin-bottom: 12px;
  
  .pre {
    flex: 1;
    
    .text {
      .text-ellipsis {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.xian {
  height: 1px;
  background: #f0f0f0;
  margin: 12px 0;
}

.bottom {
  margin-bottom: 8px;
  align-items: center;
  
  &:last-child {
    margin-bottom: 0;
    justify-content: space-between;
  }
  
  .timebox {
    align-items: center;
    
    .time {
      margin-left: 8px;
      align-items: center;
      
      i {
        font-style: normal;
        font-size: 14px;
        
        &:first-child {
          color: #666;
        }
        
        &.timedata {
          color: #333;
          margin-left: 4px;
        }
      }
    }
  }
  
  .new-text-sty {
    font-size: 12px;
    color: #999;
  }
  
  .pre-c {
    align-items: center;
    color: #1989fa;
    font-size: 14px;
    cursor: pointer;
    
    p {
      margin: 0 4px 0 0;
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
