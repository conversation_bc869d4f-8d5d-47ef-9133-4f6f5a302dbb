<template>
  <div v-if="visible" class="loading-overlay">
    <div class="loading-spinner"></div>
    <div class="loading-text">{{ message }}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      message: "Loading...",
    };
  },
  methods: {
    show(message = "Loading...") {
      this.message = message;
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
  },
};
</script>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999999999;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #fff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #fff;
  margin-top: 16px;
  font-size: 16px;
}
</style>
