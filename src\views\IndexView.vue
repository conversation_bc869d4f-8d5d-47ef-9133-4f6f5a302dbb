<template>
  <div class="contian">
    <div class="top flex-row">
      <img src="@/assets/images/tx.png" class="tx" alt="" />
      <div class="prebox flex-colum" v-if="userInfo.userName">
        <div class="title">欢迎, {{ userInfo.userName }}</div>
        <div class="sub-title">ID:{{ userInfo.id }}</div>
        <!-- 欢迎回来 -->
      </div>
      <div class="prebox flex-colum" v-else>
        <div class="title" @click="goLogin()">去登陆</div>
        <div class="sub-title">登录后查看完整信息</div>
        <!-- 欢迎回来 -->
      </div>
    </div>
    <div class="cardbox">
      <IndexCard class="card"></IndexCard>
      <div class="card-sty">
        <img src="../assets/images/cg/tab.png" class="tab-img" alt="" />
        <div class="info-box">
          <van-notice-bar
            :scrollable="false"
            color="#181818"
            background="transparent"
          >
            <template #left-icon>
              <img
                src="@/assets/images/cg/tz.png"
                style="width: 30px; height: 30px"
              />
            </template>
            <van-swipe
              vertical
              class="notice-swipe"
              :autoplay="3000"
              :show-indicators="false"
            >
              <van-swipe-item>欢迎来到湘潭市建筑垃圾监管平台</van-swipe-item>
            </van-swipe>
          </van-notice-bar>
          <van-divider />
          <!-- <van-notice-bar vertical color="#181818" :scrollable="false">
            <template #left-icon>
              <img
                src="@/assets/images/cg/tz.png"
                style="width: 20px; height: 20px"
              />
            </template>
            <van-swipe
              vertical
              class="notice-swipe"
              :autoplay="3000"
              :show-indicators="false"
            >
              <van-swipe-item>内容 1</van-swipe-item>
              <van-swipe-item>内容 2</van-swipe-item>
              <van-swipe-item>内容 3</van-swipe-item>
            </van-swipe>
          </van-notice-bar> -->
        </div>
        <LabelHeader left="业务审核"></LabelHeader>
        <FuncMenu :cardItems="cardItems1"></FuncMenu>
        <LabelHeader left="违规审核"></LabelHeader>
        <FuncMenu :cardItems="cardItems2"></FuncMenu>
        <div class="n-info-box flex-row">
          <div @click="$go('/PatrolModel/list')" class="posi-red">
            <p class="text">现场执法</p>
            <div class="homepage-badge" v-if="statisticsZTC?.patrolCount > 0">
              {{ statisticsZTC.patrolCount > 99 ? '99+' : statisticsZTC.patrolCount }}
            </div>
          </div>
          <!-- <div>我的代办</div> -->
        </div>
      </div>
      <div class="pro-box" v-if="!Loading && proData.length !== 0">
        <ProCard v-for="(item, index) in proData" :data="item" :key="index" />
      </div>
      <nullState v-if="proData.length === 0 && !Loading"></nullState>
      <!-- <div class="loading-box">
        <van-loading color="#0065ff" v-if="Loading" size="24px" vertical
          >Đang tải...</van-loading
        >
      </div> -->
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import IndexCard from "@/components/IndexCard.vue";
import FuncMenu from "@/components/FuncMenu.vue";
import LabelHeader from "@/components/com/LabelHeader.vue";
import ProCard from "@/components/com/ProCard.vue";
import nullState from "@/components/com/NullCop.vue";
import { getDrugProCostAppList } from "@/api/pro";
import { initLogin, toLogin, statisticsZTC } from "@/api/config";
export default {
  data() {
    return {
      Loading: true,
      inputValue: "",
      proData: [],
      cardItems1: [
        {
          title: "产生业务",
          img: require("@/assets/images/cg/a1.png"),
          action: "goPlaceOrigin",
          hasNewNotifications: false,
          code: "chanCount;yChanCount;bChanCount;cPendingApprovalCount;cFinalPendingApprovalCount;chanChangeAgentCount;chuChangeInfoCount;chanChangeGarbageCount;chanGarbageCycleCount",
        },
        {
          title: "处置业务",
          img: require("@/assets/images/cg/a2.png"),
          action: "goDisposalPropertyList",
          hasNewNotifications: false,
          code: "aPendingApprovalCount;aFinalPendingApprovalCount;chuCount;bChuCount;cFinalPendingApprovalCount;yChuCount;chuChangeInfoCount;chuChangeGarbageCount",
        },
        {
          title: "运输业务",
          img: require("@/assets/images/cg/a3.png"),
          action: "goHaulwayList",
          hasNewNotifications: false,
          code: "transportApprovalCount;agentFinalApprovalCount;haulwayCount;agentPendingApprovalCount;yunCount;yAgentCount;agentChangeCount;agentChangeCarCount",
        },
      ],
      cardItems2: [
        {
          title: "运输违规审核",
          img: require("@/assets/images/cg/a4.png"),
          action: "goTransportViolationsModelList",
          hasNewNotifications: false,
          code: "alarmAentCheckCount;",
        },
        {
          title: "闲置地违规审核",
          img: require("@/assets/images/cg/a5.png"),
          action: "goIdleViolationList",
          hasNewNotifications: false,
          code: "agentCheckCount",
        },
        {
          title: "投诉处理审核",
          img: require("@/assets/images/cg/a6.png"),
          // action: "goAddress",
          hasNewNotifications: false,
          code: "",
        },
      ],
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState("config", ["statisticsZTC"]),
  },
  components: {
    IndexCard,
    LabelHeader,
    ProCard,
    nullState,
    FuncMenu,
  },
  created() {},
  mounted() {
    let code;
    if (window.location.search) {
      // ?code=xxx
      const searchParams = new URLSearchParams(window.location.search);
      code = searchParams.get("code");
    } else if (window.location.hash.includes("?")) {
      // #/?code=xxx
      const hashParams = new URLSearchParams(
        window.location.hash.split("?")[1]
      );
      code = hashParams.get("code");
    }
    // console.log("this.statisticsZTC", this.statisticsZTC);
    if (code) {
      this.code = code;
      console.log("获取到 code:", code);
      if (this.userInfo.id === "") {
        this.loginWithCode(code);
      }
    } else {
      this.goLogin();
      // this.$go("/tipsShow");
      console.warn("没有获取到 code 参数");
    }
    this.Zts().then((data) => {
      this.updateNotificationStatus([this.cardItems1, this.cardItems2], data);
    });
  },
  methods: {
    updateNotificationStatus(cardGroups, dataMap) {
      debugger;
      cardGroups.forEach((group) => {
        group.forEach((card) => {
          if (!card.code) return; // 如果没有 code 字段，跳过
          const codes = card.code.split(";").filter(Boolean); // 去除空字符串
          const sum = codes.reduce((total, key) => {
            return total + (dataMap[key] || 0);
          }, 0);

          card.hasNewNotifications = sum > 0;
          card.notificationCount = sum; // 保存具体的数字
        });
      });
    },
    Zts() {
      var reqData = {};
      return statisticsZTC(reqData).then((res) => {
        if (res.data.success) {
          this.$store.commit("config/SET_statisticsZTC", res.data.result);
        } else {
          this.$store.commit("config/remove_statisticsZTC", res.data.result);
        }
        // 这里可以直接返回最新的数据
        return this.statisticsZTC;
      });
    },
    async fetchUserAndPermissions() {
      this.$loadingU.show("请等待....", 5000);
      await this.$store.dispatch("user/getInfo");
      this.$loadingU.hide();
    },

    loginWithCode(e) {
      var reqData = {
        code: e,
      };
      this.$loadingU.show("请等待....", 5000);
      toLogin(reqData).then((res) => {
        if (res.data.success) {
          var aData = res.data.result;
          var userInfoData = {
            userName: aData.userName,
            id: aData.id,
          };

          this.$store.commit("user/SET_USER_INFO", userInfoData);
        }
        // else {
        //   //失败了就初始化
        //   var userInfoData = {
        //     userName: "",
        //     id: "",
        //   };
        //   this.$loadingU.hide();
        //   this.$store.commit("user/SET_USER_INFO", userInfoData);
        //   // this.$store.commit("user/INIT_USER_INFO", res.data.result);
        // }
      });
    },
    goLogin() {
      initLogin().then((res) => {
        if (res.data.success) {
          window.location.replace(res.data.result);
        } else {
          this.$toast(
            res.data.message || "登录失败，请稍后再试,或联系工作人员"
          );
        }
      });
    },
    getInfo() {
      var reqData = {};
      getAppConf(reqData).then((res) => {
        if (res.data.state === 0) {
          var arr = res.data.data;
          var dataCode = "FB";
          const result = arr.find((item) => item.dataCode === dataCode);
          var url = result?.extendField || "";
          this.$store.commit("config/SET_kefuLink", url);
        }
      });
    },
    async getList() {
      // 设置初始加载状态
      this.Loading = true;
      try {
        var reqData = {
          pageNum: 1,
          rownum: 99,
        };
        // 发送请求并等待响应
        const res = await getDrugProCostAppList(reqData);
        if (res.data.state === 0) {
          // 请求成功，存储数据
          this.proData = res.data.data.map((item) => {
            // 使用对象解构和展开运算符合并 obj1 和 obj2
            return { ...item.cost, ...item.pro };
          });
        } else {
          // 请求失败，清空数据
          this.proData = [];
        }
      } catch (error) {
        // 捕获并处理错误
        this.proData = [];
      } finally {
        // 确保加载状态总是被设置为 false
        this.Loading = false;
      }
    },
    handleCustomClick() {
      console.log("查看更多");
    },
  },
};
</script>

<style scoped lang="less">
.contian {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #f3f7ff;
  position: relative;
  z-index: 0;
}
.notice-swipe {
  height: 40px;
  line-height: 40px;
}

.top {
  width: 100%;
  height: 214px;
  background: url("@/assets/images/index-bgi3.png") no-repeat;
  background-size: 100% 80%;
  padding: 40px 24px;
  box-sizing: border-box;
}
.cardbox {
  padding: 0 14px;
  box-sizing: border-box;
  margin-top: -130px;
}
.cardbox .card {
  margin-top: 20px;
}
.cardbox.card:nth-child(1) {
  margin-top: 0 !important;
}
.tx {
  width: 48px;
  height: 48px;
}
.prebox {
  margin-left: 10px;
}
.title {
  font-weight: 600;
  font-size: 17px;
  color: #ffffff;
  line-height: 22px;
}
.sub-title {
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 24px;
  margin-top: 5px;
}
.info-box {
  margin-top: 15px;
}
.n-info-box {
  width: 100%;
  justify-content: space-between;
  margin-top: 15px;
  div {
    width: 100%;
    height: 90px;
    padding: 15px;
    box-sizing: border-box;
    font-size: 16px;
    background-color: #f2f9ff;
    line-height: 90px;
    color: #11609f;
    font-weight: 800;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    .text {
      margin-left: 40px;
    }
  }
  div:nth-child(1) {
    background-image: url("@/assets/images/cg/bh.png");
    background-repeat: no-repeat;
    background-size: 65px 65px;
    background-position: 60px 10px;
  }
  div:nth-child(2) {
    background-image: url("@/assets/images/cg/dh.png");
    background-repeat: no-repeat;
    background-size: 65px 65px;
    background-position: 60px 10px;
  }
}
.card-sty {
  margin-top: 40px;
  padding: 5px 15px 15px 15px;
  border-radius: 30px;
  box-sizing: border-box;
  background-color: #ffffff;
  position: relative;
}
.tab-img {
  width: 90%;
  border-radius: 30px;
  position: absolute;
  left: 50%;
  top: -17px;
  transform: translateX(-50%);
}
.red {
  position: absolute;
  top: 10px;
  right: 20px;
  width: 10px;
  height: 10px;
  background-color: #ff0000;
  border-radius: 50%;
}
.posi-red {
  position: relative;
}
/* 首页专用数字徽章样式 - 统一所有红点 */
.homepage-badge,
::v-deep .homepage-badge {
  position: absolute !important;
  top: 5px !important;
  right: 12px !important;
  width: 22px !important;
  height: 18px !important;
  background: #ff0000 !important;
  border-radius: 9px !important;
  color: #ffffff !important;
  font-size: 10px !important;
  font-weight: bold !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  line-height: 1 !important;
  z-index: 999 !important;
  pointer-events: none !important;
  white-space: nowrap !important;
  border: 2px solid #ffffff !important;
  margin: 0 !important;
  text-align: center !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
  overflow: hidden !important;
}

/* 现场执法特殊位置调整 */
.n-info-box .posi-red .homepage-badge {
  position: absolute !important;
  top: 10px !important;
  right: 15px !important;
  width: 22px !important;
  height: 18px !important;
  background: #ff0000 !important;
  border-radius: 9px !important;
  color: #ffffff !important;
  font-size: 10px !important;
  font-weight: bold !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  line-height: 1 !important;
  z-index: 999 !important;
  pointer-events: none !important;
  white-space: nowrap !important;
  border: 2px solid #ffffff !important;
  margin: 0 !important;
  text-align: center !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
  overflow: hidden !important;
}
</style>
