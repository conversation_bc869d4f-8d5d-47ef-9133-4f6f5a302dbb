<template>
  <div class="home">
    <!-- 顶部导航栏 -->
    <NavHeader ref="navHeader" :title="pageTitle" :back="true"></NavHeader>

    <!-- 标签页 -->
    <van-tabs v-model="active" @change="onTabChange" sticky>
      <van-tab
        v-for="(tab, index) in tabOptions"
        :key="index"
        :title="tab.text"
      >
      </van-tab>
    </van-tabs>

    <!-- 列表内容区域 -->
    <div class="information cell">
      <div class="indent-box">
        <StandardIndentCard
          class="card"
          :indentData="item"
          :currentTabState="tabOptions[active].value"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></StandardIndentCard>
      </div>
      
      <!-- 空状态 -->
      <div v-if="dataList.length === 0" class="center">
        <NullCop msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { findListH5 } from "@/api/config";
import StandardIndentCard from "@/views/PlaceOriginModel/StandardIndentCard.vue";
import NullCop from "@/components/com/NullCop.vue";
import NavHeader from "@/components/com/NavHeader.vue";

export default {
  name: "PlaceOriginList",
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: 0, // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: 15 }, // 待初审
        { text: "已完成", value: 17 }, // 初审已完成
      ],
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        applyType: 1,
        typeState: 15, // 默认为待初审
      },
    };
  },
  computed: {
    pageTitle() {
      return "产生业务列表";
    },
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    StandardIndentCard,
    NullCop,
    NavHeader,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    // 标签页切换
    onTabChange(index) {
      this.active = index;
      this.formData.typeState = this.tabOptions[index].value;
      this.formData.pageNum = 1;
      this.finished = false;
      this.getList();
    },

    addValueAndText(dataArray) {
      return dataArray.map((item) => {
        return {
          ...item,
          value: item.dataCode,
          text: item.dataName,
        };
      });
    },
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSearch() {
      this.formData.page = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },
    // 获取列表数据
    async getList() {
      try {
        console.log("请求参数:", this.formData);
        const res = await findListH5(this.formData);
        console.log("接口返回:", res);

        if (res.data.success || res.data.state === "success") {
          const newData = res.data.result || {};
          if (this.formData.pageNum === 1) {
            this.dataList = newData.list || [];
          } else {
            this.dataList = [...this.dataList, ...(newData.list || [])];
          }
          
          this.total = res.data.total || 0;
          this.loading = false;
          this.finished = (newData.list || []).length === 0;
        } else {
          this.$toast.fail(res.data.message || "获取数据失败");
          this.loading = false;
          this.finished = true;
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$toast.fail("获取数据失败");
        this.loading = false;
        this.finished = true;
      }
    },
    nextPage(item) {
      this.$router.push({ name: "eTjbxx", params: { id: item.etId } });
    },
  },
};
</script>

<style scoped lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px;
  
  .indent-box {
    padding-top: 16px;
    
    .card {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
}

// 标签页样式规范
/deep/ .van-tabs__wrap {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .van-tab {
  font-size: 16px;
  font-weight: 500;
}

/deep/ .van-tab--active {
  color: #1989fa;
}

/deep/ .van-tabs__line {
  background: #1989fa;
}
</style>
    }

    .listText,
    .listTextZd {
      position: relative;
      color: #fff;
      font-size: 12px;
      transform-style: preserve-3d;
      text-align: center;
      padding: 0 5px;
      width: 40px;
      margin-left: 5px;
      margin-right: 15px;
    }

    .listText::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(1, 128, 254);
    }

    .listTextZd-y {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      padding: 3px 5px;
      box-sizing: border-box;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(255, 111, 0);
    }
  }

  .listSection {
    display: flex;
    flex-direction: flex-start;

    .img {
      padding-top: 10px;
      margin-right: 10px;
    }

    div.listDiv {
      display: flex;
      align-items: center;
      color: #314b8b;
      font-size: 15px;
      margin-top: 10px;

      svg {
        margin-right: 5px;
      }

      .blue {
        color: rgb(11, 110, 250);
      }
    }
  }

  .listFooter {
    margin-top: 10px;
    .van-col--12 {
      .van-button {
        background-color: rgb(229, 243, 254);
        color: rgb(1, 128, 254);
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        height: auto;
        width: 100%;
        border: none;
        font-weight: 700;
      }
    }
  }
}
.contian {
  width: 100%;
  min-height: 100vh;
  padding: 0 0 144px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  z-index: 0;
}
.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}

/deep/.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}
.indent-box {
  padding: 12px 14px 24px 14px;
}
.indent-box .card {
  margin-top: 20px;
}
.indent-box .card:nth-child(1) {
  margin-top: 0 !important;
}
.tips-cc {
  font-weight: 400;
  font-size: 14px;
  color: #ce0602;
  line-height: 25px;
}
</style>
