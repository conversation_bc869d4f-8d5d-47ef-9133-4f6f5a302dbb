# 去审核按钮设计规范

## 📋 概述
本文档定义了系统中"去审核"按钮的统一设计规范，确保所有页面的审核按钮样式和交互保持一致。

## 🎯 适用页面
- 产生单位注册审核列表：`/PlaceOrigin/register/list`
- 处置单位注册审核列表：`/DisposalProperty/register/list`
- 运输单位注册审核列表：`/Haulway/register/list`
- 产生业务审核列表：`/PlaceOrigin/list`
- 产生业务终审列表：`/PlaceOrigin/final/list`
- 运输业务审核列表：`/Haulway/list`
- 运输业务终审列表：`/Haulway/final/list`
- 处置业务审核列表：`/DisposalProperty/list`
- 处置业务终审列表：`/DisposalProperty/final/list`

## 🎨 视觉设计规范

### 按钮位置
- **位置**：卡片底部右下角
- **布局**：独立的底部区域，右对齐
- **分割线**：顶部有1px的分割线

### 按钮样式
- **背景色**：透明 (`background: transparent`)
- **边框**：无边框 (`border: none`)
- **文字颜色**：蓝色 (`#1989fa`)
- **字体大小**：14px
- **字体粗细**：500 (medium)
- **内边距**：8px 16px
- **圆角**：6px
- **图标**：右箭头图标，颜色 `#1989fa`

### 交互效果
- **悬停状态**：淡蓝色背景 (`rgba(25, 137, 250, 0.05)`)
- **点击状态**：轻微缩放效果 (`transform: scale(0.98)`)
- **过渡动画**：0.2秒缓动效果

## 💻 代码实现

### HTML 结构
```vue
<template>
  <div class="card">
    <!-- 卡片内容区域 -->
    <div class="card-content">
      <!-- 信息内容 -->
    </div>

    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(data)">
        <span>{{ getActionText() }}</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>
```

### CSS 样式
```less
// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}
```

### JavaScript 逻辑
```javascript
methods: {
  handleClick(data) {
    // 根据不同页面跳转到对应的审核页面
    this.$router.push({
      name: "审核页面路由名称",
      params: { data },
    });
  },

  getActionText() {
    // 根据当前状态返回按钮文字
    return this.currentTabState === 1 ? "去审核" : "查看详情";
  },
}
```

## 🔧 按钮文字规范

### 不同状态的按钮文字
- **待审核状态**：显示 "去审核"
- **已完成状态**：显示 "查看详情"
- **待初审状态**：显示 "去初审"
- **待终审状态**：显示 "去终审"

### 判断逻辑示例
```javascript
getActionText() {
  const currentState = parseInt(this.currentTabState);
  
  if (currentState === 1 || currentState === 15) {
    return '去审核'; // 或 '去初审'
  }
  
  if (currentState === 16) {
    return '去终审';
  }
  
  if (currentState === 2 || currentState === 17 || currentState === 18) {
    return '查看详情';
  }
  
  return '查看详情'; // 默认
}
```

## 📱 响应式设计

### 移动端适配
- 按钮大小保持不变
- 点击区域足够大，便于触摸操作
- 在小屏幕上保持右对齐布局

## ⚠️ 注意事项

1. **一致性**：所有审核列表页面必须使用相同的按钮样式
2. **可访问性**：确保按钮有足够的对比度和点击区域
3. **状态管理**：根据数据状态正确显示按钮文字
4. **路由跳转**：确保点击后正确跳转到对应的审核页面
5. **数据传递**：通过 `params` 正确传递数据对象

## 🔄 更新记录

- **2025-09-02**：初始版本，定义基础规范
- 基于产生单位注册审核列表页面的实现制定

## 📞 联系方式

如有疑问或需要修改规范，请联系前端开发团队。
