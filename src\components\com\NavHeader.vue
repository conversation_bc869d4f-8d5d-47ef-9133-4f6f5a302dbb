<template>
  <div>
    <div class="header flex-colum" ref="header" :class="fixed ? 'flexed' : ''">
      <div class="blue-box"></div>
      <div class="title-box flex-row">
        <BackCop v-if="back" class="margin"></BackCop>
        <div v-else style="width: 24px; height: 24px"></div>
        <p class="ellipsis">{{ title }}</p>
      </div>
      <div class="xian"></div>
    </div>
    <div
      class="nullbox"
      v-if="fixed"
      :style="{ height: headerHeight + 'px' }"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "Hồ Sơ Tiến Độ", // 默认灰色
    },
    back: {
      type: Boolean,
      default: true, // 默认灰色
    },
    fixed: {
      type: Boolean,
      default: true, // 默认灰色
    },
  },
  data() {
    return {
      headerHeight: 0,
    };
  },
  mounted() {
    // 在 mounted 钩子中获取 header 的高度并赋值给 headerHeight
    this.headerHeight = this.$refs.header.clientHeight;
  },
  computed: {},
  methods: {},
};
</script>

<style scoped lang="less">
.header {
  width: 100%;
  .blue-box {
    width: 100%;
    height: 30px;
    background-color: var(--color-zhuti-bg);
  }
  .title-box {
    width: 100%;
    height: 52px;
    margin-top: -16px;
    background-color: #fff;
    border-radius: 0px 24px 0px 0px;
    font-weight: 600;
    font-size: 18px;
    color: #112950;
    line-height: 52px;
    p {
      margin-left: 10px;
    }
  }
}
.margin {
  margin-left: 10px;
}
.xian {
  width: 100%;
  height: 1px;
  background: #f2f4f5;
  border-radius: 0px 0px 0px 0px;
}
.flexed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
}
.ellipsis {
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏超出内容 */
  text-overflow: ellipsis; /* 超出部分显示为 ... */
  max-width: 100%; /* 设置最大宽度，可根据需要调整 */
}
</style>
