# 测试环境恢复手册

## 概述
本文档提供详细的步骤说明，用于将项目恢复到测试环境配置，启用登录跳过功能以便开发。

## 测试环境配置
- **API地址**: `http://**************:47025/tpss`
- **登录方式**: 跳过OAuth（使用测试用户）
- **测试用户**: userName: '测试用户', id: '10'

## 详细操作步骤

### 步骤1: 创建备份文件（重要）
在进行修改之前，备份当前的生产文件:

```cmd
copy .env.production .env.production.prod.backup
copy src\store\modules\user.js src\store\modules\user.js.prod.backup
copy src\views\IndexView.vue src\views\IndexView.vue.prod.backup
```

### 步骤2: 恢复环境配置
打开 `.env.production` 文件，将所有内容替换为:

```
NODE_ENV = 'production'
VUE_APP_PUBLIC_PATH = './'

# 测试环境 - 跳过登录
VUE_APP_SKIP_LOGIN = true

# 使用相对API URL进行Nginx代理（推荐）
VUE_APP_API_URL = /api/

# 直接API URL（如果没有代理）
# VUE_APP_API_URL = http://**************:47025/tpss

# 直接API URLs（备用选项）
# VUE_APP_API_URL = https://jzljgl.xtzhcg.com:47015/tpss
# VUE_APP_API_URL = http://**************:5511/tpss
# VUE_APP_API_URL = http://**************:47025/tpss
# VUE_APP_API_URL = http://************:9091/care-cms
#测试环境构建
```

**关键修改:**
- 添加 `VUE_APP_SKIP_LOGIN = true` 启用登录跳过
- 设置API URL为测试服务器或使用代理

### 步骤3: 恢复用户存储模块
打开 `src\store\modules\user.js` 文件。

**找到这个部分（大约第3-11行）:**
```javascript
const state = {
    userInfo: {
        // userName: '测试用户',
        // id: '10',
        userName: '',
        id: '',
    },
    volunteerId:''
};
```

**替换为:**
```javascript
const state = {
    userInfo: {
        userName: '测试用户',
        id: '10',
        // userName: '',
        // id: '',
    },
    volunteerId:'10'
};
```

**这样做的作用:**
- 启用测试用户数据
- 注释掉空用户状态
- 设置测试志愿者ID

### 步骤4: 恢复开发登录逻辑
打开 `src\views\IndexView.vue` 文件。

**找到这个部分（大约第182-192行）:**
```javascript
} else {
  this.goLogin();
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**替换为:**
```javascript
} else {
  // 开发环境跳过登录
  if (process.env.VUE_APP_SKIP_LOGIN === 'true') {
    console.log("开发环境，跳过登录检查，使用测试用户");
    // 使用store中预设的测试用户信息，无需额外设置
  } else {
    this.goLogin();
  }
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**这样做的作用:**
- 恢复开发环境登录跳过功能
- 当 `VUE_APP_SKIP_LOGIN = true` 时使用测试用户
- 如果跳过功能被禁用则回退到OAuth

### 步骤5: 更新开发代理（可选）
如果你想确保开发代理指向测试服务器，打开 `vue.config.js`:

**找到这个部分（大约第10-14行）:**
```javascript
proxy: {
  '/api': {
     target: 'http://**************:47025/tpss/',
    // target: 'https://jzljgl.xtzhcg.com:47015/tpss',
    //target: 'http://**************:5511/tpss/',
```

**确保测试服务器处于活动状态:**
```javascript
proxy: {
  '/api': {
     target: 'http://**************:47025/tpss/',
    // target: 'https://jzljgl.xtzhcg.com:47015/tpss',
    // target: 'http://**************:5511/tpss/',
```

### 步骤6: 安装依赖（如果需要）
如果你修改了依赖项:
```cmd
npm install
```

### 步骤7: 构建测试环境
运行构建命令:
```cmd
npm run build
```

**预期输出:**
- 构建应该成功完成
- 登录跳过应该被启用
- 测试用户应该被预加载

## 开发服务器测试

### 启动开发服务器
```cmd
npm run serve
```

**预期行为:**
- 服务器在 `http://localhost:8080` 启动
- 登录被自动跳过
- 测试用户 '测试用户' 已登录
- API调用通过代理到测试服务器

### 验证测试配置
1. **检查控制台**: 应该看到 "开发环境，跳过登录检查，使用测试用户"
2. **检查用户信息**: 应该显示测试用户名和ID
3. **检查API调用**: 应该通过代理到测试服务器

## 生产构建测试

### 构建和测试
```cmd
npm run build
```

**测试构建文件:**
- 使用本地服务器提供 `dist\` 文件夹服务
- 应该自动使用测试用户
- 应该跳过OAuth登录

## 配置总结

### 测试环境设置
- **环境**: `.env.production` 包含 `VUE_APP_SKIP_LOGIN = true`
- **用户存储**: 启用测试用户数据
- **登录逻辑**: 启用跳过功能并有回退机制
- **API目标**: 测试服务器 `http://**************:47025/tpss`

### 修改的文件
1. `.env.production` - 环境变量
2. `src\store\modules\user.js` - 包含测试数据的用户状态
3. `src\views\IndexView.vue` - 登录跳过逻辑
4. `vue.config.js` - 开发代理（可选）

## 故障排除

### 仍然需要登录
- 检查 `.env.production` 是否包含 `VUE_APP_SKIP_LOGIN = true`
- 验证 `IndexView.vue` 是否有跳过逻辑
- 清除浏览器缓存并重新构建

### 测试用户未加载
- 检查 `user.js` 是否取消注释了测试用户数据
- 验证志愿者ID是否设置为 '10'
- 检查浏览器控制台是否有错误

### API错误
- 验证测试服务器可访问: `http://**************:47025/tpss`
- 检查 `vue.config.js` 中的代理配置
- 手动测试API端点

### 构建错误
- 清除 `node_modules` 并重新安装: `npm install`
- 检查修改文件中的语法错误
- 验证Node.js版本兼容性

## 从备份快速恢复

如果你有生产更改之前的备份文件:

```cmd
copy .env.production.backup .env.production
copy src\store\modules\user.js.backup src\store\modules\user.js
copy src\views\IndexView.vue.backup src\views\IndexView.vue
```

然后重新构建:
```cmd
npm run build
```

## 环境切换

### 切换到生产环境
参考 `PRODUCTION_BUILD_MANUAL.md`

### 切换到测试环境（当前文档）
参考本文档

### 快速环境检查
- **测试环境**: 登录被跳过，测试用户可见
- **生产环境**: 需要OAuth登录，真实认证

## 支持信息
- **测试API地址**: `http://**************:47025/tpss`
- **测试用户**: userName: '测试用户', id: '10'
- **开发服务器**: `http://localhost:8080`
- **构建输出**: `dist\` 文件夹
