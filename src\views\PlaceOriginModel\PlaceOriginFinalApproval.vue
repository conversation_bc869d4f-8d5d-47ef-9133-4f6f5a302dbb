<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="isViewMode ? '产生地终审详情' : '产生地终审'" :back="true"></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.engineeringName"
              name="content"
              label="产生地名称："
              :readonly="true"
            />
            <!-- <van-field
              :value="infoData?.agent?.agentName"
              name="content"
              label="处置地名称："
              :readonly="true"
            />-->
            <van-field
              :value="infoData?.coordinateInfo"
              name="content"
              label="产生地地址："
              :readonly="true"
            >
              <template #button>
                <van-button
                  size="mini"
                  type="primary"
                  @click="showMap"
                  icon="location-o"
                  round
                  class="map-btn"
                >
                  地图
                </van-button>
              </template>
            </van-field>
            <van-field
              :value="infoData.moreGarbageTypeListStr"
              name="content"
              label="建筑垃圾类型："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="
                (infoData?.beginValidityTime || '') +
                ' 至 ' +
                (infoData?.endValidityTime || '')
              "
              name="content"
              label="有效期："
              :readonly="true"
            />
            <!--
            <van-field
              :value="`${infoData?.allCapacity ?? ''} 方`"
              name="content"
              label="申报容量："
              :readonly="true"
            />-->
            <van-field
              :value="infoData?.addressDistrict?.fullAreaName"
              name="content"
              label="所属地区:"
              :readonly="true"
            />
            <van-field
              :value="infoData?.administrativeApplyAgent?.agentName"
              name="content"
              label="单位名称："
              label-width="120"
              :readonly="true"
            />
            <van-field
              :value="infoData?.mainCarrierUnitAgent?.agentName || '-'"
              name="content"
              label="主运单位："
              :readonly="true"
            />
            <van-field
              :value="getJointTransportUnits()"
              name="content"
              label="联运单位："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section">
      <LabelHeader left="申请材料"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <!-- 按固定分类显示申请材料 -->
            <div class="categorized-attachments">
              <!-- 行政许可申请书 -->
              <div class="attachment-category">
                <div class="category-title">行政许可申请书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs1')"
                    :key="'applyImgs1-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs1').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 营业执照或法人证书 -->
              <div class="attachment-category">
                <div class="category-title">营业执照或法人证书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs2')"
                    :key="'applyImgs2-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs2').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 授权委托书 -->
              <div class="attachment-category">
                <div class="category-title">授权委托书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs3')"
                    :key="'applyImgs3-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs3').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 信用承诺书 -->
              <div class="attachment-category">
                <div class="category-title">信用承诺书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs4')"
                    :key="'applyImgs4-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs4').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 运输合同 -->
              <div class="attachment-category">
                <div class="category-title">运输合同</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs5')"
                    :key="'applyImgs5-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs5').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 处置合同 -->
              <div class="attachment-category">
                <div class="category-title">处置合同</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs6')"
                    :key="'applyImgs6-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs6').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 建筑垃圾产生信息表 -->
              <div class="attachment-category">
                <div class="category-title">建筑垃圾产生信息表</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs7')"
                    :key="'applyImgs7-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs7').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LabelHeader left="初审意见"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.firstAuditResult === 2 ? '通过' : '不通过'"
              name="content"
              label="初审结果："
              :readonly="true"
            />
            <van-field
              :value="getAuditRemarks(infoData)"
              name="content"
              label="初审备注："
              :readonly="true"
              type="textarea"
              rows="2"
            />
            <van-field
              :value="infoData?.firstAuditTime || ''"
              name="content"
              label="初审时间："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>



    <!-- 终审情况 -->
    <LabelHeader :left="isViewMode ? '审核结果' : '审核情况'"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 证件信息 -->
            <template v-if="isViewMode">
              <!-- 查看模式：显示证件信息 -->
              <van-field
                :value="infoData.certNum || ''"
                name="certNum"
                label="证件编号："
                label-width="120px"
                :readonly="true"
              />
              <van-field name="validityPeriod" label="有效期" label-width="120px" :readonly="true">
                <template #input>
                  <div class="validity-text">
                    <span class="date-text">{{ infoData.beginValidityTime || '2025-08-01' }}</span>
                    <span class="to-text">至</span>
                    <span class="date-text">{{ infoData.endValidityTime || '2025-08-28' }}</span>
                  </div>
                </template>
              </van-field>
            </template>
            <template v-else>
              <!-- 编辑模式：证件信息输入 -->
              <van-field
                v-model="form.certNum"
                name="certNum"
                label="证件编号"
                label-width="120px"
                placeholder="请输入证件编号"
              />
              <van-field name="validityPeriod" label="有效期" label-width="120px" :readonly="true">
                <template #input>
                  <div class="validity-text" @click="showStartDatePicker = true">
                    <span class="date-text">{{ form.beginValidityTime || '2025-08-01' }}</span>
                    <span class="to-text">至</span>
                    <span class="date-text">{{ form.endValidityTime || '2025-08-28' }}</span>
                  </div>
                </template>
              </van-field>
            </template>

            <!-- 核准证文件展示 - 紧凑网格布局 -->
            <div class="cert-files-section" v-if="approvalCertFiles.length > 0">
              <div class="cert-files-grid">
                <div
                  v-for="(file, index) in approvalCertFiles"
                  :key="index"
                  class="cert-file-card"
                  @click="previewFile(file)"
                >
                  <div class="cert-image-wrapper">
                    <img :src="getFileUrl(file.filePath)" :alt="file.displayTitle" class="cert-image" />
                    <div class="cert-overlay">
                      <div class="cert-type">{{ getCertTypeLabel(file, index) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 终审结果 -->
            <template v-if="isViewMode">
              <!-- 查看模式：显示终审结果 -->
              <van-field name="applyState" label="审核结果：" label-width="120px">
                <template #input>
                  <van-radio-group
                    :value="infoData.applyState"
                    direction="horizontal"
                    disabled
                  >
                    <van-radio :name="5">通过</van-radio>
                    <van-radio :name="6">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                :value="getAuditRemarks(infoData)"
                name="finalAuditRemarks"
                label="备注："
                label-width="120px"
                type="textarea"
                rows="1"
                autosize
                :readonly="true"
              />
            </template>
            <template v-else>
              <!-- 编辑模式：终审操作 -->
              <van-field name="applyState" label="审核结果：" label-width="120px">
                <template #input>
                  <van-radio-group
                    v-model="form.applyState"
                    direction="horizontal"
                  >
                    <van-radio :name="5">通过</van-radio>
                    <van-radio :name="6">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                v-model="form.ruralDevelopmentRemarks"
                name="ruralDevelopmentRemarks"
                label="备注："
                label-width="120px"
                type="textarea"
                rows="1"
                autosize
                placeholder="请输入内容"
                show-word-limit
                maxlength="100"
              />
            </template>
          </van-form>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="btn-box" v-if="!isViewMode">
      <yButton :top150="true" title="提交" @click="sumBit()"></yButton>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentStartDate"
        type="date"
        title="选择开始日期"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        v-model="currentEndDate"
        type="date"
        title="选择结束日期"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>

    <!-- 提交中的动画弹窗 -->
    <van-overlay :show="isSubmitting" class="submit-overlay">
      <div class="submit-loading-container">
        <div class="submit-loading-content">
          <van-loading size="40px" color="#1989fa" vertical>
            <template #icon>
              <div class="custom-loading-icon">
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
                <div class="loading-circle"></div>
              </div>
            </template>
            正在提交终审结果...
          </van-loading>

          <div class="submit-progress">
            <div class="progress-text">
              <span>已用时：{{ submitElapsedTime }}秒</span>
            </div>
            <div class="progress-tip">
              服务器正在处理您的请求，请耐心等待
            </div>
          </div>

          <div class="submit-actions">
            <van-button
              type="default"
              size="small"
              @click.stop="handleBackDuringSubmit"
              class="back-button"
            >
              返回
            </van-button>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { auditH5, saveOrUpdateH5 } from "@/api/config";
import { FILE_BASE_URL, IMG_BASE_URL } from "@/utils/globalConstants";
import { ImagePreview } from 'vant';

import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  components: {
    LabelHeader,
  },
  data() {
    return {
      form: {
        applyState: "",
        ruralDevelopmentRemarks: "",
        certNum: "", // 证件编号
        beginValidityTime: "", // 有效期开始
        endValidityTime: "", // 有效期结束
      },
      rules: {
        applyState: [{ required: true, message: "请选择终审结果" }],
        ruralDevelopmentRemarks: [{ required: true, message: "请输入终审备注" }],
        certNum: [{ required: true, message: "请输入证件编号" }],
        beginValidityTime: [{ required: true, message: "请选择有效期开始时间" }],
        endValidityTime: [{ required: true, message: "请选择有效期结束时间" }],
      },
      infoData: "",

      showStartDatePicker: false,
      showEndDatePicker: false,
      currentStartDate: new Date(),
      currentEndDate: new Date(),
      isSubmitting: false, // 是否正在提交
      submitStartTime: null, // 提交开始时间
      submitTimer: null, // 提交计时器
      submitElapsedSeconds: 0, // 已用时间（秒）

      submitTimer: null, // 提交计时器
    };
  },
  computed: {
    // 判断是否为查看模式（已审核完成）
    isViewMode() {
      // 如果是从已完成tab进入，则为查看模式
      const fromCompletedTab = this.$route.query.from === 'completed';

      // 如果有typeState字段，使用typeState判断
      if (this.infoData && this.infoData.typeState !== undefined) {
        return fromCompletedTab || this.infoData.typeState === 18;
      }
      // 否则根据applyState判断：5和6表示终审已完成
      const isCompletedStatus = this.infoData && (this.infoData.applyState === 5 || this.infoData.applyState === 6);
      return fromCompletedTab || isCompletedStatus;
    },



    // 计算提交已用时间
    submitElapsedTime() {
      return this.submitElapsedSeconds;
    },

    hasAttachments() {
      // 总是显示申请材料部分，因为我们要显示固定的分类标题
      return true;
    },

    // 检查是否有核准证文件 - 总是显示核准证部分
    hasApprovalCert() {
      return true;
    },

    // 获取核准证文件列表
    approvalCertFiles() {
      if (!this.infoData || !this.infoData.approvalCertFileUrl) {
        return [];
      }
      
      try {
        const certData = JSON.parse(this.infoData.approvalCertFileUrl);
        return Array.isArray(certData) ? certData : [];
      } catch (error) {
        console.error('解析核准证文件数据失败:', error);
        return [];
      }
    },
  },
  created() {
    // this.init();
  },
  mounted() {
    try {
      this.infoData = this.$route.params.data;
      
      // 数据验证
      if (!this.infoData) {
        console.error("页面数据为空，请检查路由传参");
        this.$toast.fail("页面数据获取失败");
        return;
      }
      
      console.log("终审页面数据：", this.infoData);

      // 打印申请材料JSON数据
      console.log("=== 终审页面申请材料数据 ===");
      console.log("attachments 完整数据：", JSON.stringify(this.infoData.attachments, null, 2));
      if (this.infoData.attachments && this.infoData.attachments.length > 0) {
        this.infoData.attachments.forEach((item, index) => {
          console.log(`附件${index + 1}:`, {
            attachType: item.attachType,
            displayTitle: item.displayTitle,
            filePath: item.filePath
          });
        });
      }

      // 打印核准证数据
      console.log("=== 核准证数据 ===");
      console.log("approvalCertFileUrl 原始数据：", this.infoData.approvalCertFileUrl);
      console.log("解析后的核准证文件：", this.approvalCertFiles);
      if (this.approvalCertFiles && this.approvalCertFiles.length > 0) {
        this.approvalCertFiles.forEach((file, index) => {
          console.log(`核准证文件${index + 1}:`, {
            displayTitle: file.displayTitle,
            fileName: file.fileName,
            filePath: file.filePath
          });
        });
      }
    } catch (error) {
      console.error("终审页面初始化失败：", error);
      this.$toast.fail("页面初始化失败");
    }



    // 如果数据中已有审核结果，设置到表单中（用于显示已选择的状态）
    if (this.infoData && this.infoData.applyState) {
      this.form.applyState = this.infoData.applyState;
    }

    // 如果数据中已有审核备注，设置到表单中
    if (this.infoData) {
      const remarks = this.infoData.ruralDevelopmentRemarks || this.infoData.acceptedRemarks;
      if (remarks) {
        this.form.ruralDevelopmentRemarks = remarks;
      }
    }
  },
  methods: {
    // 获取联运单位列表
    getJointTransportUnits() {
      if (!this.infoData || !this.infoData.uuitApplyAgents || this.infoData.uuitApplyAgents.length === 0) {
        return '-';
      }

      // 提取所有联运单位的名称
      const unitNames = this.infoData.uuitApplyAgents
        .filter(agent => agent.agentName) // 过滤掉没有名称的
        .map(agent => agent.agentName);

      return unitNames.length > 0 ? unitNames.join('、') : '-';
    },

    // 根据attachType获取对应的附件
    getAttachmentsByType(attachType) {
      if (!this.infoData || !this.infoData.attachments) {
        return [];
      }

      // 只返回指定类型且有实际文件路径的附件
      return this.infoData.attachments.filter(item =>
        item.attachType === attachType &&
        item.filePath &&
        item.filePath.trim() !== '' &&
        item.filePath !== null &&
        item.filePath !== undefined
      );
    },

    // 获取核准证类型标签
    getCertTypeLabel(file, index) {
      const title = (file.displayTitle || file.fileName || '').toLowerCase();

      // 如果文件名包含正本/副本关键词，使用关键词
      if (title.includes('正本') || title.includes('original')) {
        return '正本';
      }
      if (title.includes('副本') || title.includes('copy')) {
        return '副本';
      }

      // 如果没有明确标识，按索引分配：第一个为正本，第二个为副本
      return index === 0 ? '正本' : '副本';
    },

    // 获取审核结果文本
    getAuditResultText(applyState) {
      switch (applyState) {
        case 8:
          return '通过';
        case 3:
          return '不通过';
        case 5:
          return '通过';
        case 6:
          return '不通过';
        default:
          return '未知';
      }
    },

    // 获取审核备注（兼容不同字段名）
    getAuditRemarks(data) {
      return data.ruralDevelopmentRemarks || data.acceptedRemarks || '无';
    },

    // 文件预览相关方法
    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      // 核准证文件使用 IMG_BASE_URL
      return `${IMG_BASE_URL}${filePath}`;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PPT演示';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        // 图片预览 - 使用直接导入的 ImagePreview 组件
        ImagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 开始日期确认
    onStartDateConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      this.form.beginValidityTime = `${year}-${month}-${day}`;
      this.showStartDatePicker = false;
    },

    // 结束日期确认
    onEndDateConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      this.form.endValidityTime = `${year}-${month}-${day}`;
      this.showEndDatePicker = false;
    },



    // 获取有效期文本
    getValidityPeriodText() {
      if (!this.infoData) return '无';

      const start = this.infoData.beginValidityTime;
      const end = this.infoData.endValidityTime;

      if (start && end) {
        return `${start} 至 ${end}`;
      } else if (start) {
        return `${start} 起`;
      } else if (end) {
        return `至 ${end}`;
      } else {
        return '无';
      }
    },

    // 预览图片
    previewImage(url) {
      this.$imagePreview([url]);
    },

    showMap() {
      this.$router.push({
        name: "CheckMap",
        params: { data: this.infoData },
      });
    },
    
    sumBit() {
      this.$refs.form
        .validate()
        .then(() => {
          // 开始提交动画
          this.startSubmitAnimation();

          const reqData = {
            id: this.infoData.id,
            applyState: this.form.applyState, // 5-通过, 6-不通过
            certNum: this.form.certNum,
            beginValidityTime: this.form.beginValidityTime,
            endValidityTime: this.form.endValidityTime,
            finalApprovalRemark: this.form.ruralDevelopmentRemarks
          };

          console.log("终审提交的请求数据：", reqData);

          saveOrUpdateH5(reqData)
            .then((res) => {
              console.log("终审提交结果：", res);
              this.stopSubmitAnimation();

              if (res.data.success) {
                this.$toast.success("终审提交成功");
                this.$router.go(-1);
              } else {
                this.$toast.fail(res.data.message || "终审提交失败");
              }
            })
            .catch((error) => {
              console.error("终审提交失败：", error);
              this.stopSubmitAnimation();
              this.$toast.fail("终审提交失败");
            });
        })
        .catch((error) => {
          console.log("表单验证失败：", error);
        });
    },

    // 开始提交动画
    startSubmitAnimation() {
      this.isSubmitting = true;
      this.submitStartTime = Date.now();
      this.submitElapsedSeconds = 0;

      // 启动计时器，每秒更新一次时间显示
      this.submitTimer = setInterval(() => {
        if (this.submitStartTime) {
          this.submitElapsedSeconds = Math.floor((Date.now() - this.submitStartTime) / 1000);
        }
      }, 1000);
    },

    // 停止提交动画
    stopSubmitAnimation() {
      this.isSubmitting = false;
      this.submitStartTime = null;
      this.submitElapsedSeconds = 0;

      if (this.submitTimer) {
        clearInterval(this.submitTimer);
        this.submitTimer = null;
      }
    },

    // 提交过程中用户点击返回
    handleBackDuringSubmit() {
      console.log('点击了返回按钮');

      // 先测试简单的确认对话框
      if (confirm('数据还在提交中，确定要返回吗？返回后提交将继续进行。')) {
        console.log('用户确认返回');
        this.$router.go(-1);
      } else {
        console.log('用户选择继续等待');
      }

      // 如果上面的简单确认框工作，再使用 vant 的 Dialog
      /*
      this.$dialog.confirm({
        title: '确认返回',
        message: '数据还在提交中，确定要返回吗？返回后提交将继续进行。',
        confirmButtonText: '确定返回',
        cancelButtonText: '继续等待'
      }).then(() => {
        console.log('用户确认返回');
        this.$router.go(-1);
      }).catch(() => {
        console.log('用户选择继续等待');
      });
      */
    },
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.submitTimer) {
      clearInterval(this.submitTimer);
    }
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

// 附件部分
.attachments-section {
  .categorized-attachments {
    .attachment-category {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
        padding: 0 4px;
      }

      .category-files {
        .attachment-row {
          margin-bottom: 12px;
          cursor: pointer;

          &:last-child {
            margin-bottom: 0;
          }

          // 图片行样式
          .image-row {
            img {
              width: 100%;
              max-height: 200px;
              object-fit: contain;
              border-radius: 8px;
              background: #f5f5f5;
              border: 1px solid #e6f4ff;
            }

            .attachment-title {
              font-size: 12px;
              color: #8c8c8c;
              text-align: center;
              margin-top: 6px;
              line-height: 18px;
            }
          }

          // 文件行样式
          .file-row {
            .file-info {
              display: flex;
              align-items: center;
              padding: 12px;
              background: #ffffff;
              border: 1px solid #e6f4ff;
              border-radius: 8px;
              transition: all 0.3s;

              &:hover {
                border-color: #1989fa;
                box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
              }

              .file-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8faff;
                border-radius: 6px;
                margin-right: 10px;
              }

              .file-details {
                flex: 1;

                .file-name {
                  font-size: 13px;
                  color: #262626;
                  font-weight: 500;
                  margin-bottom: 2px;
                  line-height: 18px;
                }

                .file-type {
                  font-size: 11px;
                  color: #8c8c8c;
                  line-height: 14px;
                }
              }

              .file-action {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .no-files {
          font-size: 12px;
          color: #8c8c8c;
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px dashed #d9d9d9;
        }
      }
    }
  }
}

// 核准证文件网格样式
.cert-files-section {
  margin: 16px 0;

  .cert-files-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .cert-file-card {
      position: relative;
      cursor: pointer;

      .cert-image-wrapper {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        background: #f5f5f5;
        border: 1px solid #e6f4ff;
        transition: all 0.3s;

        &:hover {
          border-color: #1989fa;
          box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
        }

        .cert-image {
          width: 100%;
          height: 120px;
          object-fit: cover;
          display: block;
        }

        .cert-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
          color: white;
          padding: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .cert-type {
            font-size: 12px;
            font-weight: 500;
          }


        }
      }
    }
  }
}

// 有效期文本样式
.validity-text {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;

  .date-text {
    color: #262626;
    font-size: 14px;
    font-weight: 400;
  }

  .to-text {
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }
}

// 备注字段优化
.van-field {
  &.van-field--textarea {
    .van-field__control {
      min-height: auto !important;
    }

    .van-field__body {
      textarea {
        min-height: 40px !important;
        line-height: 1.4 !important;
        padding: 8px 0 !important;
      }
    }
  }
}

// 有效期简洁横排样式
.validity-simple {
  display: flex;
  align-items: center;
  gap: 8px;

  .date-text {
    color: #262626;
    font-size: 14px;
    font-weight: 500;
  }

  .dash {
    color: #8c8c8c;
    font-size: 14px;
    margin: 0 4px;
  }
}



.information {
  padding: 0 16px 16px;
  
  .y-card-box {
    .y-card {
      background: #ffffff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
      border: 1px solid #f0f2f5;
    }
  }
}

.btn-box {
  margin-top: 24px;
  padding: 0 16px;
}



// 表单样式
:deep(.van-field) {
  padding: 12px 0;
  
  .van-field__label {
    color: #262626;
    font-weight: 500;
  }
  
  .van-field__value {
    color: #595959;
  }
}

:deep(.van-radio-group) {
  .van-radio {
    margin-right: 16px;

    .van-radio__label {
      color: #262626;
      font-size: 14px;
    }
  }
}

// 查看地图按钮样式
.map-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}





// 提交动画样式
.submit-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.submit-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.submit-loading-content {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 320px;
  width: 90%;
}

.custom-loading-icon {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.loading-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #1989fa;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-circle:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-circle:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.submit-progress {
  margin: 24px 0;

  .progress-text {
    font-size: 16px;
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .progress-tip {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
  }
}

.submit-actions {
  margin-top: 24px;

  .back-button {
    min-width: 100px;
    border-color: #d9d9d9;
    color: #666;

    &:hover {
      border-color: #1989fa;
      color: #1989fa;
    }
  }
}

:deep(.van-loading__text) {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-top: 12px;
}

// 单选按钮样式优化
/deep/ .van-radio__label {
  color: #262626;
  font-size: 14px;
  margin-left: 8px;
}

/deep/ .van-radio--checked .van-radio__label {
  color: #1989fa;
}

/deep/ .van-radio {
  margin-right: 24px;
  margin-bottom: 8px;
}

/deep/ .van-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .van-radio__icon {
  font-size: 16px;
}

/deep/ .van-radio__icon--checked {
  color: #1989fa;
}

// 响应式适配
@media (max-width: 375px) {
  .information {
    padding: 0 12px 12px;
  }

  .btn-box {
    padding: 0 12px;
  }

  .submit-loading-content {
    padding: 30px 20px;
    max-width: 280px;
  }
}


</style>
