<template>
  <div class="container">
    <NavHeader ref="navHeader" title="案件上传" :back="true"></NavHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              label-width="120px"
              clickable
              name="violationTime"
              :value="form.violationTime"
              label="违规时间："
              placeholder="点击选择违规时间"
              @click="showCalendar = true"
            />
            <van-popup position="bottom" v-model="showCalendar">
              <van-datetime-picker
                type="datetime"
                v-model="form.violationTime"
                @confirm="onConfirm"
            /></van-popup>
            <van-field
              name="violationType"
              label="巡查类型："
              label-width="120px"
            >
              <template #input>
                <van-radio-group
                  v-model="form.violationType"
                  direction="horizontal"
                >
                  <van-radio :name="1">产生地</van-radio>
                  <van-radio :name="2" style="margin-left: 20px"
                    >处置地
                  </van-radio>
                  <van-radio :name="3">车辆、其他 </van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.plateNo"
              name="content"
              label="违规对象："
              placeholder="请输入违规对象"
              label-width="120px"
            />
            <van-field
              v-model="form.content"
              name="acceptedRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="违规内容:"
              type="textarea"
              maxlength="100"
              placeholder="请输入违规内容"
              show-word-limit
            />
            <!-- <van-field
              readonly
              clickable
              label="违规类型："
              label-width="120px"
              :value="ViolationTypeStr"
              placeholder="选择违规类型"
              @click="showPickerViolationType = true"
            />
            <van-popup
              v-model="showPickerViolationType"
              round
              position="bottom"
            >
              <van-picker
                show-toolbar
                :columns="columns"
                :columns-field-names="{ text: 'text', value: 'value' }"
                @cancel="showPicker = false"
                @confirm="onConfirm3"
              />
            </van-popup> -->
            <van-field name="applyState" label="违规图片：" label-width="120px">
              <template #input>
                <van-uploader
                  :deletable="false"
                  v-model="imgPatrolList"
                  :max-count="1"
                  :after-read="(file) => afterRead(file, 'front')"
                />
              </template>
            </van-field>
            <van-field
              v-model="form.violationLoation"
              name="content"
              label="违规位置："
              placeholder="请输入违规位置"
              label-width="120px"
            />

            <van-field
              v-model="form.patrolRemarks"
              name="patrolRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="巡查备注:"
              type="textarea"
              maxlength="100"
              placeholder="请输入巡查备注"
              show-word-limit
            />
          </van-form>
          <div class="btn-box">
            <yButton title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { patrolSaveOrUpdateH5, upload } from "@/api/config";
import axios from "axios";
export default {
  data() {
    return {
      form: {
        // lat: "22.23154",
        // lng: "113.21254",
        lat: "",
        lng: "",
        violationType: 1,
        type: 3,
        regulationState: 0,
        violationLoation: "",
      },
      ViolationTypeStr: "",
      columns: [
        { text: "沿途抛洒滴漏", value: 1 },
        { text: "带泥上路", value: 2 },
        { text: "带泥上路", value: 2 },
      ],
      showPickerViolationType: false,
      showCalendar: false,
      rules: {
        explorationData: [{ required: true, message: "请选择预约勘查日期" }],
        applyState: [{ required: true, message: "请选择产生地状态" }],
        acceptedRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: "",
      imgPatrolList: [],
      value: "",
      key: "S7WBZ-USV6T-B4TX5-LZD6V-YAWO2-6EBKW",
    };
  },
  computed: {},
  created() {},
  computed: {},
  mounted() {
    this.getLocation();
  },
  methods: {
    onConfirm3(selected) {
      this.form.violationType = selected.value;
      this.ViolationTypeStr = selected.text; // 实际提交值
      this.showPickerViolationType = false;
    },
    afterRead(fileObj, fileType) {
      const file = fileObj.file;
      const formData = new FormData();
      formData.append("file", file);
      upload(formData).then((res) => {
        if (res.data.state === "success") {
          this.form.imgPatrolList = res.data.result;
          var url = `http://************:47025/upload/${res.data.result}`;
          this.imgPatrolList.push(url);
          this.$toast({
            message: "图片上传成功",
            duration: 2000,
          });
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      });
    },
    getAddressByLocation() {
      const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${this.form.lat},${this.form.lng}&key=${this.key}`;
      axios
        .get(url)
        .then((res) => {
          if (res.data.status === 0) {
            this.address = res.data.result.address;
          } else {
            console.log("地址解析失败：" + res.data.message);
          }
        })
        .catch((err) => {
          console.log("请求地址失败", err);
        });
    },
    getLocation() {
      return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
          // 浏览器不支持
          resolve({ success: false, message: "当前浏览器不支持定位功能" });
          return;
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            this.form.lat = lat;
            this.form.lng = lng;
            // vpn问题先不更新;
            console.log("获取到的经纬度：", lat, lng);
            this.getAddressByLocation();
            resolve({
              success: true,
              latitude: lat,
              longitude: lng,
            });
          },
          (error) => {
            // 不抛出异常，而是返回失败信息
            let message = "";
            switch (error.code) {
              case 1:
                message = "用户拒绝了定位请求";
                break;
              case 2:
                message = "位置信息不可用";
                break;
              case 3:
                message = "获取定位超时";
                break;
              default:
                message = "未知错误";
            }
            resolve({ success: false, message });
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          }
        );
      });
    },

    onConfirm(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      const second = String(date.getSeconds()).padStart(2, "0");
      this.form.violationTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      this.showCalendar = false;
    },
    sumBit() {
      console.log("提交的数据：", this.form);
      if (
        this.form.imgPatrolList !== "" &&
        this.form.plateNo !== "" &&
        this.form.content !== "" &&
        this.form.patrolRemarks !== ""
      ) {
        var reqData = {
          ...this.form,
        };
        patrolSaveOrUpdateH5(reqData).then((res) => {
          if (res.data.state === "success") {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
            this.$goback();
          } else {
            this.$toast({
              message: res.data.message,
              duration: 2000,
            });
          }
        });
        return;
      } else {
        this.$toast({
          message: "请补全信息",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.contian {
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}
.information {
  padding: 0 0 100px 0;
  box-sizing: border-box;
}
.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
// .grid-box {
//   display: flex;
//   background-color: #f7f8fa;
//   padding: 30px;
//   border-radius: 15px;
//   box-sizing: border-box;
// }
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
.van-radio {
  margin: 5px 3px;
}
</style>
