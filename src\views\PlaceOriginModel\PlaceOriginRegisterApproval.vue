<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="pageTitle" :back="true"></NavHeader>
    
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.loginId"
              name="loginId"
              label="登录账号："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.mobilePhone"
              name="mobilePhone"
              label="手机号码："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.agentName"
              name="agentName"
              label="单位名称："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1"
              name="contract1"
              label="联系人："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1Tel"
              name="contract1Tel"
              label="联系电话："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.ucsCode"
              name="ucsCode"
              label="统一信用代码："
              label-width="140px"
              :readonly="true"
            />
            <van-field
              :value="infoData?.area?.fullAreaName"
              name="area"
              label="所属地区："
              label-width="120px"
              :readonly="true"
            />
            <van-field
              :value="formatDateTime(infoData?.createDate)"
              name="createDate"
              label="注册时间："
              label-width="120px"
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 附件展示 -->
    <LabelHeader left="附件信息" v-if="infoData?.attachments?.length > 0"></LabelHeader>
    <div class="information" v-if="infoData?.attachments?.length > 0">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="attachments" label="附件：" label-width="100px">
              <template #input>
                <div class="attachment-list">
                  <div 
                    v-for="(item, index) in infoData.attachments"
                    :key="index"
                    class="attachment-item"
                    @click="previewImage(item.filePath)"
                  >
                    <img
                      :src="`http://113.87.160.8:47025/upload/${item.filePath}`"
                      :alt="item.displayTitle || '附件'"
                      class="attachment-image"
                    />
                    <div class="attachment-title">{{ item.displayTitle || '附件' }}</div>
                  </div>
                </div>
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <LabelHeader left="审核情况" v-if="!isViewMode"></LabelHeader>
    <div class="information" v-if="!isViewMode">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="userState" label="审核结果：" label-width="120px">
              <template #input>
                <van-radio-group
                  v-model="form.userState"
                  direction="horizontal"
                >
                  <van-radio :name="1">通过</van-radio>
                  <van-radio :name="5">不通过</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.acceptedRemarks"
              name="acceptedRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="审核备注"
              type="textarea"
              maxlength="100"
              placeholder="请输入备注"
              show-word-limit
            />
          </van-form>
          <div style="width: 100%; height: 150px"></div>
          <div class="btn-box">
            <yButton 
              title="提交" 
              @click="submitAudit()"
              :loading="isSubmitting"
            ></yButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 查看模式 - 显示审核结果 -->
    <LabelHeader left="审核结果" v-if="isViewMode"></LabelHeader>
    <div class="information" v-if="isViewMode">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="userState" label="审核结果：" label-width="120px">
              <template #input>
                <span class="audit-result" :class="getAuditResultClass(infoData.userState)">
                  {{ getAuditResultText(infoData.userState) }}
                </span>
              </template>
            </van-field>
            <van-field
              :value="infoData?.auditRemarks || '无'"
              name="auditRemarks"
              label="审核备注："
              label-width="120px"
              type="textarea"
              rows="3"
              :readonly="true"
            />
            <van-field
              :value="formatDateTime(infoData?.auditDate)"
              name="auditDate"
              label="审核时间："
              label-width="120px"
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sysUserTempUpdateStateH5 } from "@/api/config";
import NavHeader from "@/components/com/NavHeader.vue";
import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  name: "PlaceOriginRegisterApproval",
  components: {
    NavHeader,
    LabelHeader,
  },
  data() {
    return {
      form: {
        userState: "",
        acceptedRemarks: "",
      },
      infoData: "",
      isSubmitting: false,
    };
  },
  computed: {
    pageTitle() {
      return this.isViewMode ? "产生单位注册详情" : "产生单位注册审核";
    },
    isViewMode() {
      // 如果userState为1(通过)或5(不通过)，则为查看模式
      return this.infoData?.userState === 1 || this.infoData?.userState === 5;
    },
  },
  mounted() {
    this.infoData = this.$route.params.data;
    console.log("接收到的数据：", this.infoData);
  },
  methods: {
    // 提交审核
    async submitAudit() {
      console.log("提交的数据：", this.form);
      
      if (this.form.userState === "") {
        this.$toast("请选择审核结果");
        return;
      }

      if (this.isSubmitting) return;
      this.isSubmitting = true;

      try {
        const reqData = {
          ...this.form,
          id: this.infoData.id,
        };
        
        const res = await sysUserTempUpdateStateH5(reqData);
        if (res.data.state === "success") {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
          this.$goback();
        } else {
          this.$toast({
            message: res.data.message,
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },

    // 图片预览
    previewImage(filePath) {
      const imageUrl = `http://113.87.160.8:47025/upload/${filePath}`;
      this.$imagePreview([imageUrl]);
    },

    // 格式化时间
    formatDateTime(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 获取审核结果文本
    getAuditResultText(userState) {
      switch (userState) {
        case 1:
          return '通过';
        case 5:
          return '不通过';
        default:
          return '待审核';
      }
    },

    // 获取审核结果样式类
    getAuditResultClass(userState) {
      switch (userState) {
        case 1:
          return 'success';
        case 5:
          return 'error';
        default:
          return 'pending';
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px 16px;
  
  .y-card-box {
    .y-card {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  
  .attachment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    
    .attachment-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #eee;
    }
    
    .attachment-title {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      text-align: center;
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.btn-box {
  padding: 20px 0;
}

.audit-result {
  font-weight: bold;
  
  &.success {
    color: #07c160;
  }
  
  &.error {
    color: #ee0a24;
  }
  
  &.pending {
    color: #ff976a;
  }
}
</style>
