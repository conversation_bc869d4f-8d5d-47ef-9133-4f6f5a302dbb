<template>
  <div>
    <div class="qianming-container">
      <!--v-show="reset",用来判断是不是需要签名的，如果你不需要用，可以去掉判断条件-->
      <vue-esign
        class="sign"
        style="width: 100%; height: 600px"
        ref="esign"
        :isCrop="isCrop"
        :height="600"
        :lineWidth="lineWidth"
        :lineColor="lineColor"
        :bgColor.sync="bgColor"
      ></vue-esign>
      <div class="contro-container">
        <p type="danger" class="danger" @click="handleReset">Xóa Bỏ</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {},
      lineWidth: 6, //画笔粗细
      lineColor: "#000000", //画笔颜色
      bgColor: "", //画布背景色，为空时画布背景透明
      imgurl: "", //签名生成的图片
      isCrop: false, //是否裁剪，在画布设定尺寸基础上裁掉四周空白部分
      reset: true, //清空画布的状态
    };
  },
  methods: {
    //记录获取当前连接的后台地址：window._CONFIG['domianURL']
    //清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.imgurl = "";
      this.reset = true;
    },
    //生成签名图片..
    handleGenerate() {
      this.$refs.esign
        .generate()
        .then((res) => {
          this.imgurl = res;
          this.model.signaturePicture = res;
          this.reset = false;
          this.$emit("back", res);
        })
        .catch((err) => {
          console.log(err);
          this.$toast({
            message: "Vui lòng nhập đúng chữ ký",
            duration: 2000,
          });
        });
    },
    //将base64转换为文件..
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },
  },
};
</script>
<style scoped>
.show-img {
  width: 30%;
}
.show-info {
  width: 100%;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contro-container {
  margin-top: 30px;
  width: 100%;
  height: 24px;
  display: flex;
  flex-direction: row-reverse;
}
.qianming-container {
  width: 100%;
  height: 250px;
  margin: 20px auto;
  position: relative;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.box-card {
  width: 95%;
  margin-left: 2.5%;
}
.sign {
  border-top: 1px solid #f2f4f5;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.12); /* 修改阴影位置 */
  border-bottom: 1px solid #f2f4f5;
}
.danger {
  color: red;
  display: block;
  font-weight: 400;
  font-size: 14px;
  margin-right: 50px;
  border-bottom: 2px solid red;
}
</style>
