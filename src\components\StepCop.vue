<template>
  <div class="step-tree flex-colum">
    <div
      v-for="(step, index) in steps"
      :key="index"
      class="flex-row abox"
      @click="setCurrentStep(index)"
    >
      <div>
        <p class="title">Ngày <br />{{ addZeroIfSingleDigit(index + 1) }}</p>
      </div>
      <div class="ischang flex-colum">
        <img
          src="@/assets/images/aictive.png"
          v-if="step.isFillIn && step.trialDate"
          class="imgsize"
          alt=""
        />
        <img
          src="@/assets/images/aictive2.png"
          v-if="step.isFillIn && step.vievingDate"
          class="imgsize"
          alt=""
        />
        <img
          v-if="!step.isFillIn"
          src="@/assets/images/noacitve.png"
          class="imgsize"
          alt=""
        />
        <div v-if="!isLast(index)" class="shuxian"></div>
      </div>
      <div class="prebox" v-if="step.isFillIn && step.trialDate">
        <p class="time">{{ timeZh1(step.trialDate) }}</p>
        <p class="pre text-ellipsis-2 margin">
          {{ step.recordContent }}
        </p>
      </div>
      <div class="prebox" v-if="step.isFillIn && step.vievingDate">
        <p class="time">{{ timeZh1(step.vievingDate) }}</p>
        <p class="pre text-ellipsis-2 margin">
          <!--{{ step.contentStr }}-->Hoàn thành! Xin lưu ý, có thể sẽ có bác sĩ gọi điện điều tra!
        </p>
      </div>
      <div class="prebox" v-if="!step.isFillIn">
        <p class="time1">Hãy hoàn thành!</p>
        <div
          class="btn flex-row margin"
          :class="step.isWhite ? activeClassName : 'noaActive'"
          @click="goWhite(step)"
        >
          <p>Nhấp vào</p>
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
    <div style="font-size: 16px;margin-top: 30px;font-style: italic; color: #999;">Lưu ý: Trong giai đoạn quan sát, bác sĩ sẽ tiến hành các cuộc gọi kiểm tra không định kỳ.</div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      change: true,
    };
  },
  props: {
    steps: {
      type: Array,
      required: true,
    },
    activeClassName: {
      type: String,
      default: "aActive", // 默认类名
    },
    currentStep: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    ...mapState("proturn", ["recordType"]),
  },
  mounted() {
    console.log("steps", this.steps);
  },
  methods: {
    timeZh1(e) {
      if (e) {
        return this.$formatIsoString(e);
      } else {
        return e;
      }
    },

    timeRegis(dateTimeStr) {
      // 创建一个新的 Date 对象
      const date = new Date(dateTimeStr);
      // 获取年份、月份、日期、小时、分钟和秒
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要加1
      const day = String(date.getDate()).padStart(2, "0");
      // 拼接成所需的格式
      return `${day}/${month}/${year} `;
    },
    goWhite(e) {
      if (e.isWhite) {
        this.$store.commit("record/res_daysData");
        setTimeout(() => {
          this.$store.commit("record/SET_daysData", e);
          if (this.recordType === "SHIYAO") {
            // this.$go("/drug/record");
            //2024/6/27试药记录修改 问卷 => 视频
            this.$go("/drug/videorecord");
          }
          if (this.recordType === "GUANCHA") {
            this.$go("/drug/gcqdurgrecord");
          }
        }, 200);
      } else {
        this.$toast({
          message: "Vui lòng quay lại vào ngày mai!",
          //还没有到记录的时间
          duration: 2000,
        });
      }
    },
    setCurrentStep(index) {
      this.$emit("update:currentStep", index);
    },
    //如果是最后一个就不显示那个按钮
    isLast(index) {
      return index === this.steps.length - 1;
    },
    addZeroIfSingleDigit(index) {
      // 将数字转换为字符串
      const numStr = index.toString();
      // 如果数字只有一位，则在前面补零
      if (numStr.length === 1) {
        return "0" + numStr;
      } else {
        return numStr;
      }
    },
  },
};
</script>

<style scoped lang="less">
@mainColor: #e6a23c;
.step-tree {
  display: flex;
}
.step-tree div.active {
  font-weight: bold;
}
.imgsize {
  width: 32px;
  height: 32px;
}
.abox {
  width: 100%;
  justify-content: space-between;
  margin-top: 15px;
}
.ischang {
  margin: 0 10px;
  align-items: center;
}
.shuxian {
  width: 2px;
  height: 56px;
  background: #e5e5e5;
  border-radius: 99px 99px 99px 99px;
}
.title {
  font-weight: 400;
  font-size: 14px;
  color: #112950;
  line-height: 24px;
}
.time {
  font-weight: 600;
  font-size: 17px;
  color: #777;
  line-height: 22px;
}
.time1 {
  font-weight: 600;
  font-size: 17px;
  color: #f95e43;
  line-height: 22px;
}
.prebox {
  width: 80%;
  text-align: left;
  position: relative;
  .pre {
    font-weight: 400;
    font-size: 14px;
    color: #8e9aab;
    line-height: 24px;
  }
}
.btn {
  width: 120px;
  padding: 3px;
  box-sizing: border-box;
  border-radius: 12px 12px 12px 12px;

  font-weight: 400;
  font-size: 15px;

  line-height: 22px;
  justify-content: center;
  align-items: center;
}
.aActive-2 {
  border: 1px solid @mainColor;
  color: @mainColor;
}
.aActive {
  border: 1px solid var(--color-zhuti);
  color: var(--color-zhuti);
}
.noaActive {
  border: 1px solid #8e9aab;
  color: #8e9aab;
}
.margin {
  margin-top: 10px;
}
</style>
