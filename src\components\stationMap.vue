<template>
  <div class="flushMarkPage">
    <!-- 弹窗组件 -->
    <van-dialog
      v-model="dialogVisible"
      :message="message"
      :type="msgType"
      width="300px"
    >
      <span>{{ message }}</span>
    </van-dialog>

    <div class="flushMarkContent">
      <van-map
        style="width: 100%; height: 100%"
        :longitude="longitude"
        :latitude="latitude"
        :scale="scale"
        :markers="markers"
        :polylines="polylines"
        @tap="tapMap"
      >
        <div class="mapContent">
          <div class="mainMapTest">
            <div class="mainTest">
              <div class="mainTestTitle">电子围栏</div>
              <div class="mainTestBtn" @click="startClick">开始绘制</div>
              <div class="mainTestBtn" @click="endClick">结束绘制</div>
            </div>
            <div class="mainTest">
              <div class="mainTestTitle">围栏经纬度</div>
              <div class="mainTestRight">{{ formTemp.regionDef }}</div>
            </div>
            <div class="mainTest">
              <div class="mainTestTitle">坐标经纬度</div>
              <div class="mainTestRight">{{ formTemp.location }}</div>
            </div>
          </div>
          <div class="btnClass">
            <div
              class="button"
              @click="removeAll"
              style="color: #e6a23c; width: 25%"
            >
              清除
            </div>
            <div
              class="button"
              @click="save"
              style="background-color: #e6a23c; width: 75%"
            >
              保存
            </div>
          </div>
        </div>
      </van-map>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import showUtils from "../utils/showUtils";
import {
  gcj02towgs84,
  wgs84togcj02,
} from "@/api/rubbishExamine/rubbishExamine";
import { Dialog } from "vant"; // 引入 Vant 的 Dialog 组件

export default {
  name: "flushMark",
  components: {
    [Dialog.name]: Dialog,
  },
  computed: {
    ...mapGetters(["openid", "wxLogin", "userLogin", "userId", "miniUser"]),
  },
  data() {
    return {
      dialogVisible: false,
      msgType: "",
      message: "",
      scale: 15,
      latitude: 39.9042,
      longitude: 116.4074,
      markers: [],
      polylines: [
        {
          points: [],
          arrowLine: true,
          dottedLine: false,
          width: 3,
          color: "#409EFF",
        },
      ],
      formTemp: {
        location: "", // 经纬度逗号拼接
        coordinateInfo: "", // 位置中文
        regionDef: "", // 电子围栏经纬度逗号和分号拼接
      },
      isShowFirst: null,
      points: [],
    };
  },
  mounted() {
    this.mainInit();
  },
  methods: {
    guideToAuth() {
      showUtils.showLoading("微信登录中");
      // 微信登陆逻辑代码
    },
    authcode2Session(code) {
      // 获取 openid 和登录状态
    },
    toggleMessage(type, message) {
      this.dialogVisible = true;
      this.msgType = type;
      this.message = message || "消息提示";
    },
    mainInit() {
      this.notReady = false;
      // 初始化地图和其他相关内容
    },
    toUserAdd() {
      this.$router.push("/user-add");
    },
    tapMap(e) {
      if (this.isShowFirst === null) {
        this.toggleMessage("warn", "请先绘制电子围栏区域");
        return;
      } else if (this.isShowFirst === true) {
        // 删除多边形
        if (this.polylines[0].points.length > 0) {
          this.polylines[0].points = [];
        }
        const detailTemp = e.detail;
        this.points.push({
          longitude: detailTemp.longitude, //经度
          latitude: detailTemp.latitude, //纬度
        });
        this.markers.push({
          longitude: detailTemp.longitude,
          latitude: detailTemp.latitude,
          iconPath: "../static/location.png",
          width: 20,
          height: 20,
        });
      } else if (this.isShowFirst === false) {
        // 删除标记点
        if (this.markers.length > 0) {
          this.markers = [];
        }
        const detailTemp = e.detail;
        this.markers.push({
          longitude: detailTemp.longitude,
          latitude: detailTemp.latitude,
          iconPath: "../static/location.png",
          width: 20,
          height: 20,
        });
        var LngLat = gcj02towgs84(detailTemp.longitude, detailTemp.latitude);
        this.formTemp.location = LngLat[0] + "," + LngLat[1];
        const dataResult = {
          longitude: LngLat[0],
          latitude: LngLat[1],
          location: null,
        };
        this.getRegeo(dataResult, true, "detail");
      }
    },
    startClick() {
      this.isShowFirst = true;
    },
    endClick() {
      if (this.points.length >= 3) {
        this.isShowFirst = false;
        this.polylines[0].points = this.points;
        this.polylines[0].points.push({
          longitude: this.polylines[0].points[0].longitude, //经度
          latitude: this.polylines[0].points[0].latitude, //纬度
        });
        this.$nextTick(() => {
          this.$refs.mapContext.includePoints({
            padding: [70],
            points: this.polylines[0].points,
          });
        });
        let arrTemp = [];
        for (let i = 0; i < this.polylines[0].points.length; i++) {
          const arr = this.polylines[0].points[i];
          let wgs84 = gcj02towgs84(arr.longitude, arr.latitude);
          arrTemp.push({ lng: wgs84[0], lat: wgs84[1] });
        }
        let rectangleLngLat = arrTemp
          .map((pt) => pt.lng + "," + pt.lat)
          .join(";");
        this.formTemp.regionDef = rectangleLngLat;
      } else {
        this.toggleMessage("warn", "绘制电子围栏区域最少由3个点组成");
      }
    },
    removeAll() {
      this.isShowFirst = null;
      this.formTemp.location = "";
      this.formTemp.coordinateInfo = "";
      this.formTemp.regionDef = "";
      this.points = [];
      this.removeNewOverlay();
    },
    save() {
      if (!this.formTemp.regionDef) {
        this.toggleMessage("warn", "请绘制电子围栏区域");
        return;
      }
      if (!this.formTemp.location || !this.formTemp.coordinateInfo) {
        this.toggleMessage("warn", "请选择坐标点");
        return;
      }
      const isArea = this.isPointInPolygon(
        this.markers[0].longitude,
        this.markers[0].latitude,
        this.polylines[0].points
      );
      if (!isArea) {
        this.toggleMessage("warn", "坐标点必须在电子围栏区域内");
        return;
      }
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="less">
.flushMarkPage {
  min-height: 100vh;
  background: #fff;
}
.flushMarkContent {
  width: 100%;
  height: 100vh;
}
.mapContent {
  background: #fff;
  position: fixed;
  bottom: 20px;
  z-index: 999;
  width: 100%;
  padding: 20px;
  margin: 0 30px;
  border-radius: 6px;
}
.mainMapTest {
  .mainTest {
    .mainTestTitle {
      width: 140px;
      margin-right: 15px;
      color: #666;
      line-height: 56px;
      text-align: right;
      height: 56px;
    }
    .mainTestBtn {
      background: #ff9602;
      color: #fff;
      border-radius: 30px;
      padding: 0 40px;
      cursor: pointer;
      line-height: 56px;
      height: 56px;
      margin-right: 10px;
    }
    .mainTestRight {
      width: calc(100% - 155px);
      line-height: 56px;
      height: 56px;
    }
  }
}
.btnClass {
  .button {
    color: #fff;
    height: 70px;
    line-height: 70px;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    font-size: 32px;
    display: inline-block;
  }
}
</style>
