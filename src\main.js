import Vue from 'vue'
import App from './App.vue'
import router from './router'
import 'vant/lib/index.css';
import i18n from './lang'
import md5 from 'js-md5';
//loding插件
import LoadingU from './components/loadingUtils/loading';
Vue.use(LoadingU);
Vue.prototype.$md5 = md5;
//返回组件
import { Sticky } from 'vant';
Vue.use(Sticky);
import { Picker } from 'vant';
Vue.use(Picker);
import { DatetimePicker } from 'vant';
Vue.use(DatetimePicker);
import BackCop from './components/com/BackCop.vue'; 
Vue.component('BackCop', BackCop);
//页面中——loading页面
import { Calendar } from 'vant';
Vue.use(Calendar);
import LodingView from './components/com/LodingView.vue'; 
Vue.component('LodingView', LodingView);
import YInput from './components/com/YInput.vue'; 
Vue.component('YInput', YInput);
import YgourpCheck from './components/com/YgourpCheck.vue'; 
Vue.component('NavHeader', NavHeader);
import NavHeader from './components/com/NavHeader.vue'; 
Vue.component('YgourpCheck', YgourpCheck);
import LabelHeader from './components/com/LabelHeader.vue'; 
Vue.component('LabelHeader', LabelHeader);
import yButton from './components/com/ComBtn.vue'; 
Vue.component('yButton', yButton);
import YtextareaCop from './components/com/YtextareaCop.vue'; 
Vue.component('YtextareaCop', YtextareaCop);
import NullCop from './components/com/NullCop.vue'; 
Vue.component('NullCop', NullCop);
import { List } from 'vant';
Vue.use(List);
// 添加Fastclick移除移动端点击延迟
import FastClick from 'fastclick'
//FastClick的ios点击穿透解决方案
FastClick.prototype.focus = function (targetElement) {
    let length;
    if (targetElement.setSelectionRange && targetElement.type.indexOf('date') !== 0 && targetElement.type !== 'time' && targetElement.type !== 'month') {
        length = targetElement.value.length;
        targetElement.focus();
        targetElement.setSelectionRange(length, length);
    } else {
        targetElement.focus();
    }
};
FastClick.attach(document.body)
import { Form } from 'vant';
Vue.use(Form);
//引入公共的方法
import { calculateWidth } from './utils/width';
Vue.prototype.$calculateWidth = calculateWidth;
import { go, goback,isLogin,moneyGs,formatIsoString } from './utils/com';
Vue.prototype.$go = go;
Vue.prototype.$formatIsoString = formatIsoString;
Vue.prototype.$moneyGs = moneyGs;
Vue.prototype.$isLogin = isLogin;
Vue.prototype.$goback = goback;
import store from './store'
Vue.config.productionTip = false
import { sha256 } from 'js-sha256'
Vue.prototype.$sha256 = sha256
//引入vue的组件
import { Tab, Tabs } from 'vant';
Vue.use(Tab);
Vue.use(Tabs);
import { Cascader } from 'vant';
Vue.use(Cascader);
import { Button } from 'vant';
Vue.use(Button);
import { Tabbar, TabbarItem } from 'vant';
Vue.use(Tabbar);
Vue.use(TabbarItem);
import { NavBar } from 'vant';
Vue.use(NavBar);
import { Col, Row } from 'vant';
Vue.use(Col);
Vue.use(Row);
import { Swipe, SwipeItem } from 'vant';
Vue.use(Swipe);
Vue.use(SwipeItem);
import { Cell, CellGroup } from 'vant';
Vue.use(Cell);
Vue.use(CellGroup);
import { Field } from 'vant';
Vue.use(Field);
import { Icon } from 'vant';
Vue.use(Icon);
import 'amfe-flexible';
import { Grid, GridItem } from 'vant';
Vue.use(Grid);
Vue.use(GridItem);
import { Image as VanImage } from 'vant';
Vue.use(VanImage);
import { Search } from 'vant';
Vue.use(Search);
import { Checkbox, CheckboxGroup } from 'vant';
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
import { Switch } from 'vant';
Vue.use(Switch);
import { Popup } from 'vant';
Vue.use(Popup);
import { NoticeBar } from 'vant';
Vue.use(NoticeBar);
import { Divider } from 'vant';
Vue.use(Divider);
import { Loading } from 'vant';
Vue.use(Loading);
import { Overlay } from 'vant';
Vue.use(Overlay);
import { RadioGroup, Radio } from 'vant';
Vue.use(Radio);
Vue.use(RadioGroup);
import { Tag } from 'vant';
Vue.use(Tag);
import { Toast } from 'vant';
Vue.use(Toast);
import { Uploader} from 'vant';
Vue.use(Uploader);
import { Dialog } from 'vant';
import { ImagePreview } from 'vant';
import { CouponCell, CouponList } from 'vant';
Vue.use(CouponCell);
Vue.use(CouponList);
// 全局注册
Vue.use(Dialog);
Vue.use(ImagePreview);
import { debounce,throttle} from './utils/com';
import { FILE_BASE_URL } from './utils/globalConstants';
Vue.prototype.$debounce = debounce;
Vue.prototype.$throttle = throttle;
Vue.prototype.$fileBaseUrl = FILE_BASE_URL;
// import websocket from 'vue-native-websocket';
// Vue.use(websocket, '', {
//   connectManually: true, // 手动连接
//   format: 'json', // json格式
//   reconnection: true, // 是否自动重连
//   reconnectionAttempts: 5, // 自动重连次数
//   reconnectionDelay: 2000, // 重连间隔时间
// });
//引入签名文件
import vueEsign from 'vue-esign'
Vue.use(vueEsign)
new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
