<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ getDisplayTitle(indentData) }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="calendar-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>创建日期: </i><i class="timedata">{{ formatDateTime(indentData.createDate) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="location-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>管辖区: </i><i class="timedata">{{ getAddressInfo(indentData) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>坐标信息: </i><i class="timedata">{{ indentData?.coordinateInfo || '-' }}</i>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>{{ getButtonText(indentData) }}</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: "StandardIndentCard",
  data() {
    return {};
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
    currentTabState: {
      type: [String, Number],
      default: 15,
    },
  },

  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
          return '-';
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    getDisplayTitle(data) {
      // 显示工程名称或默认标题
      if (data?.engineeringName) {
        return data.engineeringName;
      }
      return '处置地申请';
    },

    getAddressInfo(data) {
      // 获取地址信息
      const parts = [];
      if (data?.addressProv?.areaName) {
        parts.push(data.addressProv.areaName);
      }
      if (data?.addressCity?.areaName) {
        parts.push(data.addressCity.areaName);
      }
      if (data?.addressDistrict?.areaName) {
        parts.push(data.addressDistrict.areaName);
      }
      return parts.length > 0 ? parts.join('-') : '-';
    },

    getButtonText() {
      // 使用当前tab状态而不是数据中的typeState
      const currentState = parseInt(this.currentTabState);

      if (currentState === 15) {
        return '去审核'; // 待初审，统一显示为"去审核"
      }

      if (currentState === 16) {
        return '去终审'; // 待终审
      }

      if (currentState === 17 || currentState === 18) {
        return '查看详情'; // 已完成
      }

      return '查看详情'; // 默认
    },

    handleClick(e) {
      // 根据当前路由判断跳转到初审还是终审
      const currentRoute = this.$route.path;

      if (currentRoute.includes('/final/')) {
        // 终审页面
        this.$router.push({
          name: "DisposalPropertyFinalApproval",
          params: { data: e },
        });
      } else {
        // 初审页面
        this.$router.push({
          name: "DisposalPropertyApproval",
          params: { data: e },
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.top {
  padding: 16px;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.pre {
  flex: 1;
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 8px 16px;
  
  &:last-child {
    padding-bottom: 16px;
  }
}

.timebox {
  align-items: center;
  width: 100%;
}

.time {
  margin-left: 8px;
  align-items: center;
  
  i {
    font-style: normal;
    font-size: 14px;
    color: #8c8c8c;
    
    &.timedata {
      color: #262626;
      font-weight: 500;
    }
  }
}

.pre-c {
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 0;
  
  p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #1989fa;
    margin-right: 4px;
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
