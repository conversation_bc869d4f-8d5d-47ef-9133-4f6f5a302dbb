<template>
  <div @click="goback">
    <img v-if="color === 'black'" src="../../assets/images/back2.png" alt="" />
    <img v-else src="../../assets/images/back.png" alt="" />
  </div>
</template>
<script>
export default {
  props: {
    color: {
      type: String,
      default: "black", // 默认black
    },
  },
  data() {
    return {};
  },
  computed: {},
  methods: {
    goback() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="less">
img {
  width: 24px;
  height: 24px;
}
</style>
