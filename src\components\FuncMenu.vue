<template>
  <div class="card">
    <div
      class="card-item"
      v-for="(item, index) in cardItems"
      :key="index"
      @click="handleClick(item)"
    >
      <div class="homepage-badge" v-if="item.hasNewNotifications">
        {{ item.notificationCount > 99 ? '99+' : item.notificationCount }}
      </div>
      <img :src="item.img" alt="" />
      <p>{{ item.title }}</p>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      // cardItems: [
      //   {
      //     title: "产生地业务",
      //     img: require("@/assets/images/a1.png"),
      //     action: "goUSER",
      //   },
      //   {
      //     title: "处置场所业务",
      //     img: require("@/assets/images/a1.png"),
      //     action: "goJKBG",
      //   },
      //   {
      //     title: "运输准运证业务",
      //     img: require("@/assets/images/a1.png"),
      //     action: "goAddress",
      //   },
      // ],
    };
  },
  props: {
    cardItems: {
      type: Array,
      default: () => [], // 默认启用一个空对象
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState("Permissions", ["noPermissionsName"]),
  },
  methods: {
    handleClick(item) {
      if (typeof this[item.action] === "function") {
        this[item.action]();
      } else {
        console.warn(`方法 ${item.action} 不存在`);
      }
    },
    goPlaceOrigin() {
      this.$go("/PlaceOrigin/func");
    },
    goProductionAuditList() {
      this.$go("/ProductionAudit/list");
    },
    goHaulwayList() {
      this.$go("/HaulwayModel/func");
    },
    goTransportViolationsModelList() {
      this.$go("/TransportViolationsModel/list");
    },
    goIdleViolationList() {
      this.$go("/IdleViolationModel/list");
    },
    goDisposalPropertyList() {
      this.$go("/DisposalPropertyModel/func");
    },
    goJKBG() {
      if (this.noPermissionsName === "") {
        this.$go("/health");
        return;
      }
      if (this.noPermissionsName === "personStatus") {
        this.$toast({
          message: "Vui lòng điền theo thứ tự",
          duration: 2000,
        });
      } else {
        this.$go("/health");
      }
    },
    goAddress() {
      if (this.noPermissionsName === "") {
        this.$go("/address/list");
        return;
      }
      if (
        this.noPermissionsName === "personStatus" ||
        this.noPermissionsName === "approvalStatus"
      ) {
        this.$toast({
          message: "Vui lòng điền theo thứ tự",
          duration: 2000,
        });
      } else {
        this.$go("/address/list");
      }
    },
    goYHK() {
      this.$go("/bank/banklist");
    },
    gouPacting() {
      if (this.userInfo.isActing === 1) {
        this.$go("/recommenderlist");
        return;
      }
      this.$go("/upacting");
    },
  },
};
</script>

<style scoped lang="less">
.card {
  display: flex;
  flex-wrap: wrap;
  width: 327px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f2f4f5;
  padding: 10px;
  box-sizing: border-box;
  position: relative; /* 为了让水平分隔线定位 */
}

.card-item {
  flex: 1 0 33.33%; /* 每行3个 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
  box-sizing: border-box;
  text-align: center;
  position: relative;
  /* 移除旧的notification-badge样式，使用首页的homepage-badge样式 */
}

.card-item p {
  font-weight: 600;
  font-size: 12px;
  color: #112950;
  line-height: 18px;
  margin: 10px 0 0;
}

.card-item img {
  width: 32px;
  height: 32px;
}

.card-separator {
  flex-basis: 100%;
  height: 1px;
  background: #f2f4f5;
}

.card-item:nth-child(1)::after,
.card-item:nth-child(2)::after,
.card-item:nth-child(4)::after,
.card-item:nth-child(5)::after,
.card-item:nth-child(6)::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: #f2f4f5;
}
</style>
