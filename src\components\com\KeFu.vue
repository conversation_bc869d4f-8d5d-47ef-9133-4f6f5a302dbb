<template>
  <div class="contain flex-colum">
    <img
      src="@/assets/images/kefu-1.png"
      @click="goKefuZUANSHU()"
      class="imgSize"
      alt=""
    />
    <!-- <img
      style="margin-top: 10px"
      src="@/assets/images/kefu-2.png"
      @click="goKefuYILIAO()"
      class="imgSize"
      alt=""
    /> -->
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {};
  },
  components: {},
  computed: {
    ...mapState("user", ["userInfo"]),
  },
  mounted() {},
  methods: {
    goKefuZUANSHU() {
      var dtLink = this.userInfo?.kfExclusiveObj?.kfParam || "";
      if (dtLink) {
        window.location.href = dtLink;
      } else {
        this.$toast({
          message: "<PERSON><PERSON>n kết dịch vụ khách hàng trống.",
          duration: 3000,
        });
      }
    },
    goKefuYILIAO() {
      var dtLink = this.userInfo?.kfDoctorObj?.kfParam || "";
      if (dtLink) {
        window.location.href = dtLink;
      } else {
        this.$toast({
          message: "Liên kết dịch vụ khách hàng trống.",
          duration: 3000,
        });
      }
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="less" scoped>
.contain {
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 20px;
  bottom: 100px;
}
.imgSize {
  width: 45px;
  height: 45px;
}
.kf {
  font-weight: 600;
  font-size: 8px;
  color: #ffffff;
  margin-top: -15px;
  line-height: 23px;
  text-align: center;
  padding: 0 5px;
  box-sizing: border-box;
  background: #0065ff;
  border-radius: 12px 12px 12px 12px;
}
</style>
