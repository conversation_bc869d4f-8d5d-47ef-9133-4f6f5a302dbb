<template>
  <div class="custom-textarea">
    <div class="label">{{ label }}</div>
    <textarea
      class="textarea"
      :style="textareaStyle"
      v-model="inputValue"
      @input="handleInput"
      :placeholder="placeholder"
    ></textarea>
  </div>
</template>

<script>
export default {
  name: "CustomTextarea",
  props: {
    defaultValue: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "Vui lòng nhập một giá trị",
    },
    label: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      inputValue: this.defaultValue,
    };
  },
  computed: {
    textareaStyle() {
      return {
        border: "1px solid #ccc",
        borderRadius: "4px",
        padding: "8px",
        backgroundColor: "transparent",
        color: "#333",
        width: "100%",
        height: "100px", // 设置合适的高度
        fontFamily: "Times New Roman", // 设置字体
        fontSize: "16px", // 设置字体大小
        boxSizing: "border-box",
      };
    },
  },
  methods: {
    handleInput(event) {
      this.inputValue = event.target.value;
    },
  },
};
</script>

<style scoped>
.label {
  font-weight: 600;
  font-size: 16px;
  color: #112950;
}
.custom-textarea {
  margin-top: 20px;
  width: 100%;
}

.textarea {
  margin: 20px 0;
  width: 100%;
  height: 100px;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 8px;
  background-color: transparent;
  color: #333;
  font-family:Arial, Helvetica, sans-serif;
  font-size: 16px;
  box-sizing: border-box;
}
</style>
