import request from '../utils/request'
//项目部分的接口
export const getDrugProAppList = data =>
    request({
        url: '/site/v1/DrugProApi/getDrugProAppList',
        method: "POST",
        data
    });
    
export const getDrugProInfo = data =>
    request({
        url: '/site/v1/DrugProApi/getDrugProInfo',
        method: "POST",
        data
    });

export const getProOrderAttaList = data =>
    request({
        url: '/site/v1/OrderAttaApi/getProOrderAttaList',
        method: "POST",
        data
    });
export const getDrugProCostAppList = data =>
    request({
        url: '/site/v1/DrugProApi/getDrugProCostAppList',
        method: "POST",
        data
    });
export const joinDrugPro = data =>
    request({
        url: '/site/v1/DrugProApi/joinDrugPro',
        method: "POST",
        data
    });
export const addOrderAtta = data =>
    request({
        url: '/site/v1/OrderAttaApi/addOrderAtta',
        method: "POST",
        data
    });
export const addOrder = data =>
    request({
        url: '/site/v1/OrderApi/addOrder',
        method: "POST",
        data
    });
export const getOrderStatusList = data =>
    request({
        url: '/site/v1/OrderApi/getOrderStatusList',
        method: "POST",
        data
    });
export const payOrder = data =>
    request({
        url: '/site/v1/OrderApi/payOrder',
        method: "POST",
        data
    });
export const postOrder = data =>
    request({
        url: '/site/v1/OrderApi/postOrder',
        method: "POST",
        data
    });
export const trialOrder = data =>
    request({
        url: '/site/v1/OrderApi/trialOrder',
        method: "POST",
        data
    });
    //观察期
export const getOrderTrialRegList = data =>
    request({
        url: '/site/v1/OrderTrialApi/getOrderTrialRegList',
        method: "POST",
        data
    });
export const upOrderViewing = data =>
    request({
        url: '/site/v1/OrderViewingApi/upOrderViewing',
        method: "POST",
        data
    });
export const viewingEndOrder = data =>
    request({
        url: '/site/v1/OrderApi/viewingEndOrder',
        method: "POST",
        data
    });
export const getOrderViewingRegList = data =>
    request({
        url: '/site/v1/OrderViewingApi/getOrderViewingRegList',
        method: "POST",
        data
    });
export const getTrialQuestionList = data =>
    request({
        url: '/site/v1/TrialQuestionApi/getTrialQuestionList',
        method: "POST",
        data
    });
export const upOrderTrial = data =>
    request({
        url: '/site/v1/OrderTrialApi/upOrderTrial',
        method: "POST",
        data
    });
export const trialEndOrder = data =>
    request({
        url: '/site/v1/OrderApi/trialEndOrder',
        method: "POST",
        data
    });