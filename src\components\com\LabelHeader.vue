<template>
  <div class="header flex-row">
    <p class="left">{{ left }}</p>
    <div class="flex-row" v-if="right" @click="handleClick">
      <p>{{ right }}</p>
      <van-icon name="arrow" />
    </div>
  </div>
</template>
<script>
export default {
  name: "CustomInput",
  props: {
    left: {
      type: String,
      default: "",
    },
    right: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {},
  methods: {
    handleClick() {
      this.$emit("custom-click"); // 触发自定义事件
    },
  },
};
</script>

<style scoped lang="less">
.header {
  width: 100%;
  align-items: center;
  justify-content: space-between;
  margin: 12px 0;
  .left {
    font-weight: 600;
    font-size: 17px;
    color: #112950;
    line-height: 22px;
    display: flex;
    align-items: center;
  }
  .left::before {
    content: "";
    width: 4px;
    border-radius: 30px;
    display: block;
    margin-right: 8px;
    height: 15px;
    background-color: #1890ff;
  }
  div {
    font-weight: 600;
    font-size: 13px;
    color: #1890ff;
    line-height: 20px;
    align-items: center;
  }
}
</style>
