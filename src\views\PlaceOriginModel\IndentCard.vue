<template>
  <div class="card">
    <div class="state-box" :class="statusClass" v-if="false">
      {{ statusText }}
    </div>
    <div class="top flex-row">
      <!-- <img :src="indentData.pro?.projectImg" class="proimg" alt="" /> -->
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis max-width">
            {{ indentData?.engineeringName }}
          </div>
        </div>

        <!--<div class="text-ellipsis-2" v-if="indentData.pro?.proIntroduction">
          {{ indentData.pro?.proIntroduction }}
        </div>
        <div class="text-ellipsis-2" v-else>Chưa có hồ sơ</div>-->
      </div>
      <div class="map-box">
        <van-button
          class="goMap-btn"
          type="primary"
          size="small"
          @click="goMap(indentData)"
          round
          icon="location-o"
        >
          查看地图
        </van-button>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>产生地地址: </i
          ><i class="timedata">{{ indentData?.coordinateInfo }}</i>
        </div>
      </div>
      <div></div>
    </div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>处置地名称: </i
          ><i class="timedata">{{ indentData?.agent?.agentName }}</i>
        </div>
      </div>
      <div></div>
    </div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>建筑垃圾类型: </i
          ><i class="timedata">
            {{
              indentData?.moreGarbageTypeList
                ?.map((item) => item.dicValue)
                .join("，")
            }}
          </i>
        </div>
      </div>
      <div></div>
    </div>
    <div class="bottom flex-row">
      <div class="new-text-sty">申请日期：{{ indentData.createDate }}</div>
      <div
        class="flex-row pre-c"
        @click="handleClick(indentData)"
        v-if="shouldShowButton(indentData)"
      >
        <p>{{ getButtonText(indentData.typeState, indentData.applyState) }}</p>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { trialOrder } from "@/api/pro";
import { addPrePayOrder } from "@/api/pay";
import { Dialog } from "vant";
export default {
  data() {
    return {
      isDialogShow: true,
    };
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}), // 默认启用一个空对象
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    statusClass() {
      // 0-待受理 1-受理不通过 2-受理通过 3-审核不成功 4-审核成功 5-围栏绘制成功(生效中)
      // switch (this.indentData.applyState) {
      //   case (1, 3):
      //     return "error";
      //   case (6, 7):
      //     return "end";
      //   case (2, 4, 5):
      //     return "success";
      //   default:
      //     return "success";
      // }
      return "success";
    },
    statusText() {
      return "企业注册";
      // switch (this.indentData.applyState) {
      //   case 0:
      //     return "待受理";
      //   case 1:
      //     return "受理不通过";
      //   case 2:
      //     return "受理通过";
      //   case 3:
      //     return "审核不成功";
      //   case 4:
      //     return "审核成功";
      //   case 5:
      //     return "围栏已生效";
      //   case 6:
      //     return "已失效";
      //   default:
      //     return "未知";
      // }
    },
  },
  methods: {
    // 判断是否应该显示按钮
    shouldShowButton(data) {
      // 如果有typeState字段，根据typeState判断
      if (data.typeState !== undefined) {
        return [15, 16, 17, 18].includes(data.typeState);
      }

      // 否则根据applyState判断
      return [0, 2, 3, 4, 5, 6, 7, 8, 9].includes(data.applyState);
    },

    // 根据状态获取按钮文本
    getButtonText(typeState, applyState) {
      // 如果有typeState字段，优先使用typeState
      if (typeState !== undefined) {
        switch (typeState) {
          case 15:
            return '去审核'; // 待初审
          case 16:
            return '去终审'; // 待终审
          case 17:
            return '查看详情'; // 初审已完成
          case 18:
            return '查看详情'; // 终审已完成
          default:
            return '去审核';
        }
      }

      // 否则根据applyState判断
      if (applyState === 8 || applyState === 3 || applyState === 5 || applyState === 6) {
        return '查看详情'; // 已审核完成（初审完成或终审完成）
      }

      if (applyState === 9) {
        return '去终审'; // 待终审
      }

      return '去审核'; // 默认为待审核（包括初审等）
    },

    formatDateTime(isoString) {
      // 将 ISO 8601 字符串转换为 Date 对象
      const date = new Date(isoString);
      // 检查是否为有效日期
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }
      // 获取年、月、日、小时、分钟和秒
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      // 拼接成所需格式
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    },
    handleClick(e) {
      var a = {
        ...e,
        moreGarbageTypeListStr: e.moreGarbageTypeList
          ?.map((item) => item.dicValue)
          .join("，"),
      };

      // 根据typeState判断跳转到初审还是终审页面
      // 15-待初审, 16-待终审, 17-初审已完成, 18-终审已完成
      if (e.typeState !== undefined) {
        if (e.typeState === 16 || e.typeState === 18) {
          // 终审相关状态，跳转到终审页面
          this.$router.push({ name: "PlaceOriginFinalApproval", params: { data: a } });
        } else {
          // 初审相关状态，跳转到初审页面
          this.$router.push({ name: "PlaceOriginApproval", params: { data: a } });
        }
      } else {
        // 没有typeState字段时，根据applyState判断跳转页面
        if (e.applyState === 9 || e.applyState === 5 || e.applyState === 6) {
          // 状态9为待终审，状态5和6为终审已完成，都跳转到终审页面
          this.$router.push({ name: "PlaceOriginFinalApproval", params: { data: a } });
        } else {
          // 其他状态跳转到初审页面
          this.$router.push({ name: "PlaceOriginApproval", params: { data: a } });
        }
      }
    },
    goMap(e) {
      this.$router.push({ name: "CheckMap", params: { data: e } });
    },
    configAddress() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/set_show", true);
    },
    goPay() {
      this.$store.commit("pay/initData");
      this.$store.commit("pay/SET_type", "ZHIFU");
      var reqData = {
        orderNo: this.indentData.order.orderNo,
        volunteerId: this.userInfo.volunteerId,
      };
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      addPrePayOrder(reqData).then((res) => {
        if (res.data.data === null) {
          this.$toast({
            message: res.data.ynMsg,
            duration: 2000,
          });
          return;
        }
        if (res?.data?.data?.data !== null) {
          this.$store.commit("pay/SET_url", res.data.data.data.qrDataURL);
          this.$store.commit("pay/SET_dialogShow", true);
        } else {
          this.$toast({
            message: res.data.data.desc,
            duration: 2000,
          });
        }
      });
    },
    goRecord() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$store.commit("proturn/SET_orderData", this.indentData.order);
      this.$router.push("/drug/recordstate");
    },
    configAddressIsjion() {
      Dialog.confirm({
        title: "Xác nhận đã nhận thuốc",
        // message: "确认收到药品，开始试药？请仔细确认，这代表试药即将开始！！",
        message:
          "Xác nhận đã nhận thuốc? Vui lòng kiểm tra kỹ, điều này có nghĩa là thử nghiệm lâm sàng sẽ bắt đầu!",
        confirmButtonColor: "#0065ff",
        confirmButtonText: "Xác Nhận",
        cancelButtonText: "Hủy Bỏ",
      })
        .then(() => {
          var reqData = {
            orderNo: this.indentData.order.orderNo,
            volunteerId: this.userInfo.volunteerId,
          };
          trialOrder(reqData).then((res) => {
            this.$toast({
              message: res.data.ynMsg,
              duration: 2000,
            });
            if (res.data.state === 0) {
              this.$emit("getList");
            }
          });
        })
        .catch(() => {
          // on cancel
        });
    },
    sumBitGoSign() {
      var idData = {
        orderNo: this.indentData.order.orderNo,
        projectId: this.indentData.order.proId,
      };
      this.$store.dispatch("proturn/resetCellData");
      this.$store.commit("proturn/set_Id", idData);
      this.$store.commit("proturn/SET_proData", this.indentData.pro);
      this.$router.push("/pro/paydetils");
    },
  },
};
</script>

<style scoped lang="less">
.card {
  width: 100%;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #f2f4f5;
  position: relative;
  .state-box {
    position: absolute;
    right: 30px;
    border-radius: 10px;
    top: -5px;
    padding: 2px 12px;
    box-sizing: border-box;
    font-weight: 400;
    font-size: 12px;
    text-align: center;
    color: #ffffff;
    line-height: 18px;
  }
  .success {
    background-color: #1989fa;
  }
  .error {
    background-color: #f33060;
  }
  .end {
    background-color: #909399;
  }
  .top {
    align-items: center;
    padding: 10px 5px 10px 15px;
    justify-content: space-between;
    box-sizing: border-box;
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.proimg {
  width: 72px;
  height: 72px;
  border-radius: 12px 12px 12px 12px;
}

.pre {
  width: 100%;
  height: 30px;
  justify-content: space-around;
}
.pre div:nth-child(1) {
  width: 100%;
  font-weight: 600;
  font-size: 17px;
  color: #112950;
}

.pre div:nth-child(2) {
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #8e9aab;
  line-height: 18px;
}
.bottom {
  padding: 10px 16px;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  .timebox {
    align-items: center;
    .timeimg {
      width: 16px;
      height: 16px;
    }
    .time {
      margin-left: 5px;
      font-size: 14px;
      color: #6b7089;
      align-items: center;
      i:nth-child(1) {
        width: 110px;
      }
      .timedata {
        margin-left: 2px;
        color: #353c5c !important;
        font-weight: 600;
      }
    }
  }
  .pre-c {
    padding: 5px 5px;
    align-items: center;
    font-weight: 600;
    font-size: 12px;
    color: #1989fa;
    line-height: 18px;
  }
}
.map-box {
  width: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.max-width {
  max-width: 200px;
}
</style>
