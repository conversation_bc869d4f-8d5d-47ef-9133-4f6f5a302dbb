<template>
  <div class="contain">
    <!-- 确认订单 -->
    <van-dialog
      confirmButtonColor="#0065ff"
      confirmButtonText="xác nhận"
      cancelButtonText="Hủy bỏ"
      v-model="show"
      title="Xác nhận đơn hàng"
      show-cancel-button
      @confirm="sumBit"
      @cancel="cancel"
    >
      <div class="flex-colum mid-box">
        <div class="flex-colum">
          <!-- 项目名： -->
          <p>Dự Án Thử Thuốc: </p>
          <p>{{ proData.projectName }}</p>
        </div>
        <div class="flex-colum">
          <!-- 订单号： -->
          <p>Mã Số Dự Án:</p>
          <p>{{ idData.orderNo }}</p>
        </div>
        <div class="flex-colum">
          <!-- 收货人姓名： -->
          <p>Tên Người Nhận Hàng:</p>
          <p>{{ address.userName }}</p>
        </div>
        <div class="flex-colum">
          <!-- 收获人电话 -->
          <p>Số Điện Thoại Của Người Nhận Hàng:</p>
          <p>{{ address.mobileNo }}</p>
        </div>
        <div class="flex-colum">
          <!-- 收获地址 -->
          <p>Địa Chỉ:</p>
          <p>{{ address.addrStr }}</p>
        </div>
        <div class="tips" @click="$go('/address/list')">
          Sai địa chỉ? <i>Để sửa đổi</i>
        </div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import { postOrder } from "@/api/pro";
export default {
  data() {
    return {};
  },
  computed: {
    currentRoute() {
      return this.$route;
    },
    ...mapState("proturn", ["address", "proData", "idData", "show"]),
  },
  created() {
    this.$store.dispatch("proturn/getAdfalueAddress");
  },
  methods: {
    sumBit() {
      var reqData = {
        addrStr: this.address.addrStr,
        mobileNo: this.address.mobileNo,
        orderNo: this.idData.orderNo,
        userName: this.address.userName,
        volunteerId: this.address.volunteerId,
      };
      postOrder(reqData).then((res) => {
        this.$toast({
          message: res.data.ynMsg,
          duration: 2000,
        });
        this.$store.commit("proturn/set_show", false);
        this.$emit("sumbitChange");
      });
    },
    cancel() {
      if (this.currentRoute.path === "/pro/protoco") {
        this.$go("/indent", true);
      } else {
        this.$store.commit("proturn/set_show", false);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.contain {
  position: relative;
  z-index: 99999;
}
.mid-box {
  padding: 5px 15px;
  box-sizing: border-box;
  div {
    margin: 3px;
    p {
      width: 100%;
      display: block;
    }
    p:nth-child(1) {
      font-weight: 400;
      font-size: 15px;
      color: #aaa;
    }
    p:nth-child(2) {
      font-weight: 600;
      font-size: 15px;
      color: #112950;
      line-height: 26px;
    }
  }
}
.tips {
  font-weight: 600;
  font-size: 14px;
  color: #112950;
  line-height: 24px;
  text-align: right;
  margin: 15px 0 !important;
  i {
    color: #0f62f9;
  }
}
</style>
