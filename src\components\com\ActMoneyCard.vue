<template>
  <div :class="['box', state ? 'active' : 'noactive', 'flex-column']">
    <div
      class="flex-column"
      style="align-items: center; justify-content: space-between"
    >
      <div
        class="flex-row"
        style="align-items: center; justify-content: space-between; width: 100%"
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="points"
            size="24px"
            :color="state ? '#529b2e' : '#c45656'"
          />
          <p>Số tiền hoa hồng phụ: {{ this.$moneyGs(sharingMoney) }}</p>
        </div>
      </div>
      <div
        class="flex-row margin-c"
        style="align-items: center; justify-content: space-between; width: 100%"
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="points"
            size="24px"
            :color="state ? '#529b2e' : '#c45656'"
          />
          <p>Tiền Thưởng: {{ this.$moneyGs(subsidyAmount) }}</p>
        </div>
      </div>
      <div
        v-if="statusText"
        class="flex-row margin-c"
        style="align-items: center; justify-content: space-between; width: 100%"
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="todo-list-o"
            size="24px"
            :color="state ? '#529b2e' : '#c45656'"
          />
          <p>Tên: {{ userName }}</p>
        </div>
      </div>
      <div
        class="flex-row margin-c"
        style="align-items: center; justify-content: space-between; width: 100%"
      >
        <div class="flex-row" style="align-items: center">
          <van-icon
            name="clock-o"
            size="24px"
            :color="state ? '#529b2e' : '#c45656'"
          />
          <p>{{ this.$formatIsoString(regTime) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      state: true,
    };
  },
  props: {
    changeType: {
      type: String,
      default: "",
    },
    userName: {
      type: String,
      default: "",
    },
    sharingMoney: {
      type: Number,
      default: 0,
    },
    subsidyAmount: {
      type: Number,
      default: 0,
    },
    regTime: {
      type: String,
      default: "",
    },
  },
  computed: {
    statusText() {
      switch (this.changeType) {
        case "PAY1":
          return "nạp tiền";
        // return "充值";
        case "PAY2":
          return "rút";
        // return "提现";
        case "PAY3":
          return "Hoàn trả tiền đặt cọc";
        // return "退押金";
        case "PAY4":
          return "Sự tiêu thụ";
        // return "消费";
        case "PAY5":
          return "nhiệm vụ";
        // return "分佣";
        case "PAY6":
          return "Tiền Thưởng";
        // return "补偿金";
        case "PAY7":
          return "Rút tiền bị từ chối";
        // return "提现驳回";
        default:
          return "";
      }
    },
  },
  mounted() {
    this.isTrueOrFalse(this.changeType);
  },
  methods: {
    // 越南时间手动加一个小时
    time(dateString) {
      // 将日期字符串转换为 Date 对象
      const date = new Date(dateString);
      // 增加一个小时
      date.setHours(date.getHours() + 1);
      // 格式化日期为所需格式
      const formattedDate = date.toISOString().replace("T", " ").substr(0, 19);
      return formattedDate;
    },
    isTrueOrFalse(changeType) {
      if (changeType === "PAY1") {
        this.state = true;
      } else {
        this.state = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.activetips {
  padding: 3px 8px;
  color: white;
  font-size: 11px;
  box-sizing: border-box;
  border-radius: 0 12px 0 12px;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  background-color: var(--color-zhuti);
}
.box {
  padding: 16px;
  position: relative;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 12px;
}
.margin-c {
  margin-top: 10px;
}
.active {
  background: #d1edc4;
  border: 1px solid #529b2e;
}
.noactive {
  background-color: #fab6b6;
  border: 1px solid #c45656;
}
p {
  font-weight: 600;
  font-size: 14px;
  color: #112950;
  line-height: 18px;
  margin-left: 10px;
}
.title {
  margin-bottom: 10px;
}
.change {
  color: var(--color-zhuti) !important;
}
</style>
