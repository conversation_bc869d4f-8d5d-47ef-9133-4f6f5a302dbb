<template>
  <div class="home">
    <NavHeader ref="navHeader" title="运输核准审核" :back="true"></NavHeader>

    <!-- 标签页 -->
    <van-tabs v-model="active" @change="onTabChange" sticky>
      <van-tab
        v-for="(tab, index) in tabOptions"
        :key="index"
        :title="tab.text"
      >
      </van-tab>
    </van-tabs>

    <div class="information cell">
      <div class="indent-box">
        <IndentCard
          class="card"
          :indentData="item"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></IndentCard>
      </div>
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { agentFindListH5 } from "@/api/config";
import IndentCard from "@/views/HaulwayModel/TransportIndentCard.vue";
import nullState from "@/components/com/NullCop.vue";

export default {
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: 0, // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: 10 }, // 待审核
        { text: "已完成", value: 12 }, // 初审完成
      ],
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        typeState: 10, // 默认为待审核
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200);
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    IndentCard,
    nullState,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    // 标签页切换
    onTabChange(index) {
      this.active = index;
      this.formData.typeState = this.tabOptions[index].value;
      this.formData.pageNum = 1;
      this.finished = false;
      this.getList();
    },

    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },

    getList() {
      if (this.loading) return;
      this.$loadingU.show("请等待....", 5000);
      this.loading = true;
      const reqData = {
        ...this.formData,
      };

      agentFindListH5(reqData)
        .then((res) => {
          console.log("获取运输核准审核列表数据：", res);
          if (res.data.success) {
            const newData = res.data.result;
            if (newData && newData.list) {
              this.dataList = newData.list;
            } else {
              this.dataList = [];
            }
            console.log("获取到的数据：", this.dataList);
          } else {
            if (this.formData.pageNum === 1) {
              this.dataList = [];
            }
            this.finished = true;
          }
          this.$loadingU.hide();
          this.loading = false;
        })
        .catch((error) => {
          console.error("获取运输核准审核列表失败：", error);
          this.$loadingU.hide();
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.center {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
}

.contian {
  width: 100%;
  min-height: 100vh;
  padding: 0 0 144px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  z-index: 0;
}

.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}

.indent-box {
  padding: 12px 14px 24px 14px;
}

.indent-box .card {
  margin-top: 20px;
}

.indent-box .card:nth-child(1) {
  margin-top: 0 !important;
}

/deep/.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}
</style>
