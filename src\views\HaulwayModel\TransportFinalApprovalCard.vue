<template>
  <div class="card">
    <div class="state-box" :class="statusClass">
      {{ statusText }}
    </div>
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData?.agentName || '运输企业名称' }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="underway-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>申请时间: </i>
          <i class="timedata">{{ formatDateTime(indentData?.createTime) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="location-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>申请类型: </i>
          <i class="timedata">{{ indentData?.applyType || '运输核准申请' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="contact" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>联系人: </i>
          <i class="timedata">{{ indentData?.contactPerson || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="phone-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>联系电话: </i>
          <i class="timedata">{{ indentData?.contactPhone || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row">
      <div class="flex-row pre-c" @click="handleClick(indentData)">
        <p>去终审</p>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    statusClass() {
      // 根据状态返回不同的样式类
      const typeState = this.indentData?.typeState;
      switch (typeState) {
        case 11:
          return 'status-pending'; // 终审待审核
        case 13:
          return 'status-completed'; // 终审完成
        default:
          return 'status-default';
      }
    },
    statusText() {
      // 根据状态返回不同的文本
      const typeState = this.indentData?.typeState;
      switch (typeState) {
        case 11:
          return '待终审';
        case 13:
          return '已完成';
        default:
          return '未知状态';
      }
    },
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
          return '-';
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        return '-';
      }
    },
    
    handleClick(data) {
      // 跳转到终审详情页面
      this.$router.push({ 
        name: "TransportFinalApprovalDetail", 
        params: { data: data } 
      });
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.state-box {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  
  &.status-pending {
    background: #faad14;
  }
  
  &.status-completed {
    background: #52c41a;
  }
  
  &.status-default {
    background: #d9d9d9;
    color: #666666;
  }
}

.top {
  padding: 16px;
  position: relative;
}

.pre {
  flex: 1;
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 8px 16px;
  
  &:last-child {
    padding-bottom: 16px;
  }
}

.timebox {
  align-items: center;
  width: 100%;
}

.time {
  margin-left: 8px;
  align-items: center;
  
  i {
    font-style: normal;
    font-size: 14px;
    color: #8c8c8c;
    
    &.timedata {
      color: #262626;
      font-weight: 500;
    }
  }
}

.pre-c {
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 0;
  
  p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #1989fa;
    margin-right: 4px;
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
