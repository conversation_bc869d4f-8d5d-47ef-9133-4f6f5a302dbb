<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData.agentName || '运输企业名称' }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="calendar-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>申请时间: </i><i class="timedata">{{ formatDateTime(indentData.createDate) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="shop-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>申请类型: </i><i class="timedata">{{ getApplyType(indentData) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="contact" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>联系人: </i><i class="timedata">{{ indentData.contract1 || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="phone-o" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>联系电话: </i><i class="timedata">{{ indentData.contract1Tel || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="logistics" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>车辆数量: </i><i class="timedata">{{ indentData.carCount || 0 }}辆</i>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>{{ getButtonText(indentData) }}</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    statusClass() {
      // 根据运输核准状态返回样式类
      switch (this.indentData.approvalState) {
        case 3: // 初审不通过
          return "error";
        case 5: // 审核不通过
          return "error";
        case 8: // 审核通过待上传
          return "warning";
        case 9: // 待终审
          return "warning";
        case 4: // 生效
          return "success";
        case 10: // 终审驳回
          return "error";
        default:
          return "pending";
      }
    },
    statusText() {
      // 根据运输核准状态返回状态文本
      switch (this.indentData.approvalState) {
        case 3:
          return "初审不通过";
        case 5:
          return "审核不通过";
        case 8:
          return "审核通过待上传";
        case 9:
          return "待终审";
        case 4:
          return "生效";
        case 10:
          return "终审驳回";
        default:
          return "待审核";
      }
    },
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
          return '-';
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        return '-';
      }
    },

    getApplyType(data) {
      // 获取申请类型
      if (data?.dictionary2?.dicValue) {
        return data.dictionary2.dicValue;
      }
      return '运输核准申请';
    },

    getButtonText(data) {
      const currentRoute = this.$route.path;
      const approvalState = data.approvalState;

      // 根据当前页面和状态返回按钮文字
      if (currentRoute.includes('/transport-approval')) {
        // 初审页面 - 根据当前tab判断
        const currentTab = this.$parent && this.$parent.active;
        if (currentTab === 0) {
          // 待审核tab - 状态为待审核的显示"去审核"
          return '去审核';
        } else {
          // 已完成tab - 都显示"查看详情"
          return '查看详情';
        }
      } else if (currentRoute.includes('/transport-final-approval')) {
        // 终审页面 - 根据当前tab判断
        const currentTab = this.$parent && this.$parent.active;
        if (currentTab === 0) {
          // 待终审tab - 状态为待终审的显示"去审核"
          return '去审核';
        } else {
          // 已完成tab - 都显示"查看详情"
          return '查看详情';
        }
      }

      return '查看详情'; // 默认
    },

    // 判断是否来自已完成tab
    isFromCompletedTab() {
      const currentRoute = this.$route.path;
      if (currentRoute.includes('/transport-approval')) {
        // 检查当前是否在已完成tab
        return this.$parent && this.$parent.active === 1; // 假设已完成是第二个tab
      } else if (currentRoute.includes('/transport-final-approval')) {
        return this.$parent && this.$parent.active === 1;
      }
      return false;
    },

    handleClick(e) {
      // 根据当前路由和状态判断跳转
      const currentRoute = this.$route.path;
      const isFromCompleted = this.isFromCompletedTab();

      // 将对象序列化为字符串
      const dataString = encodeURIComponent(JSON.stringify(e));

      if (currentRoute.includes('/transport-final-approval')) {
        // 终审页面 - 所有状态都跳转到终审审核页面
        this.$router.push({
          name: "TransportFinalApproval",
          params: { data: dataString },
          query: isFromCompleted ? { from: 'completed' } : {}
        });
      } else {
        // 初审页面 - 所有状态都跳转到初审页面
        this.$router.push({
          name: "TransportApproval",
          params: { data: dataString },
          query: isFromCompleted ? { from: 'completed' } : {}
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.state-box {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  
  &.pending {
    background: #faad14;
  }

  &.warning {
    background: #fa8c16;
  }

  &.success {
    background: #52c41a;
  }

  &.error {
    background: #ff4d4f;
  }
}

.top {
  padding: 16px;
  position: relative;
}

.pre {
  flex: 1;
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 8px 16px;
  
  &:last-child {
    padding-bottom: 16px;
  }
}

.timebox {
  align-items: center;
  width: 100%;
}

.time {
  margin-left: 8px;
  align-items: center;
  
  i {
    font-style: normal;
    font-size: 14px;
    color: #8c8c8c;
    
    &.timedata {
      color: #262626;
      font-weight: 500;
    }
  }
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
