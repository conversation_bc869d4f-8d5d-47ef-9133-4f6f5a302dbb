# 生产环境打包手册

## 概述
本文档提供详细的步骤说明，用于手动配置和构建项目到生产环境，启用OAuth登录功能。

## 生产环境配置
- **API地址**: `http://**************:5511/tpss`
- **登录方式**: OAuth授权认证（无跳过）
- **用户数据**: 空（无测试用户）

## 详细操作步骤

### 步骤1: 创建备份文件（重要）
在进行任何修改之前，先备份将要修改的文件：

```cmd
copy .env.production .env.production.backup
copy src\store\modules\user.js src\store\modules\user.js.backup
copy src\views\IndexView.vue src\views\IndexView.vue.backup
```

### 步骤2: 更新环境配置
打开 `.env.production` 文件，将所有内容替换为：

```
NODE_ENV = 'production'
VUE_APP_PUBLIC_PATH = './'

# 生产环境 - 启用OAuth登录
# VUE_APP_SKIP_LOGIN = false

# 生产环境API地址
VUE_APP_API_URL = http://**************:5511/tpss

#生产环境构建
```

**关键修改:**
- 移除或注释掉 `VUE_APP_SKIP_LOGIN = true`
- 设置API地址为生产服务器: `http://**************:5511/tpss`

### 步骤3: 更新用户存储模块
打开 `src\store\modules\user.js` 文件。

**找到这个部分（大约第3-11行）:**
```javascript
const state = {
    userInfo: {
        userName: '测试用户',
        id: '10',
        // userName: '',
        // id: '',
    },
    volunteerId:'10'
};
```

**替换为:**
```javascript
const state = {
    userInfo: {
        // userName: '测试用户',
        // id: '10',
        userName: '',
        id: '',
    },
    volunteerId:''
};
```

**这样做的作用:**
- 注释掉测试用户数据
- 启用空用户状态以支持OAuth登录
- 清空志愿者ID

### 步骤4: 更新登录逻辑
打开 `src\views\IndexView.vue` 文件。

**找到这个部分（大约第182-192行）:**
```javascript
} else {
  // 开发环境跳过登录
  if (process.env.VUE_APP_SKIP_LOGIN === 'true') {
    console.log("开发环境，跳过登录检查，使用测试用户");
    // 使用store中预设的测试用户信息，无需额外设置
  } else {
    this.goLogin();
  }
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**替换为:**
```javascript
} else {
  this.goLogin();
  // this.$go("/tipsShow");
  console.warn("没有获取到 code 参数");
}
```

**这样做的作用:**
- 移除开发环境登录跳过功能
- 强制所有用户使用OAuth登录
- 简化登录逻辑

### 步骤5: 安装依赖
在命令提示符中运行:
```cmd
npm install
```

等待安装完成。如果遇到错误，尝试:
```cmd
npm install --legacy-peer-deps
```

### 步骤6: 构建生产版本
运行构建命令:
```cmd
npm run build
```

**预期输出:**
- 构建过程应该无错误完成
- `dist\` 文件夹应该被创建/更新
- 文件应该为生产环境优化

### 步骤7: 验证构建输出
检查 `dist\` 文件夹中存在这些文件:
- `index.html`
- `static\` 文件夹包含CSS和JS文件
- `favicon.ico`

## 部署说明

### 上传到Web服务器
1. 将 `dist\` 文件夹的全部内容上传到你的Web服务器
2. 确保Web服务器配置为提供静态文件服务
3. 配置Web服务器处理Vue Router历史模式（将所有路由重定向到index.html）

### Nginx配置示例
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

### Apache配置示例
```apache
RewriteEngine On
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

## 测试生产构建

### 1. 访问应用程序
- 在浏览器中打开你的生产环境URL
- 应该自动重定向到OAuth登录

### 2. OAuth登录流程
- 登录页面: `https://www.xtdzzw.cn/xtsso/authz/oauth/v20/authorize`
- 登录后，应该带着code参数重定向回来
- 应用程序应该处理code并登录用户

### 3. API连接性
- 检查浏览器控制台是否有API错误
- 验证API调用指向: `http://**************:5511/tpss`
- 测试主要应用程序功能

## 故障排除

### 构建错误
- 检查Node.js版本（推荐: 14+）
- 清除 `node_modules` 并重新运行 `npm install`
- 检查修改文件中的语法错误

### 登录问题
- 验证OAuth重定向URL配置正确
- 检查浏览器控制台的JavaScript错误
- 确保API服务器可访问

### API错误
- 验证生产API服务器正在运行
- 检查到 `http://**************:5511/tpss` 的网络连接
- 验证API服务器的CORS设置

## 回滚说明
如果需要回滚更改:

```cmd
copy .env.production.backup .env.production
copy src\store\modules\user.js.backup src\store\modules\user.js
copy src\views\IndexView.vue.backup src\views\IndexView.vue
```

然后重新构建:
```cmd
npm run build
```

## 安全注意事项
- 生产构建已启用OAuth登录
- 生产环境中无测试用户凭据
- 所有API调用通过生产服务器
- 确保生产部署使用HTTPS

## 支持信息
- 生产API地址: `http://**************:5511/tpss`
- OAuth提供商: `https://www.xtdzzw.cn/xtsso/authz/oauth/v20/authorize`
- 构建输出位置: `dist\`
