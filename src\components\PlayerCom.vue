<template>
  <div
    id="J_prismPlayer"
    style="width: 100%; height: 325px; overflow: hidden"
  ></div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "PlayerCom",
  data() {
    return {
      player: null,
    };
  },
  computed: {
    ...mapState("proturn", ["proData"]),
  },
  mounted() {},
  methods: {
    setVideo() {
      this.player = window.Aliplayer({
        id: "J_prismPlayer",
        source: this.proData.operationVideo,
        preload: true,
        autoplay: true,
        loop: true, // 设置为循环播放
        // 其他配置
        controlBarVisibility: "none", // "hover" 表示鼠标悬停时显示，可以设置为 "always" 表示一直显示，或 "none" 表示不显示
      });
      this.player.on("rtsTraceId", function (event) {
        console.log("EVENT rtsTraceId", event.paramData);
      });
      this.player.on("error", (event) => {
        this.$store.dispatch("player/handleUrl");
        console.error("Player Error:", event);
      });
      this.player.on("rtsFallback", function (event) {
        console.log(" EVENT rtsFallback", event.paramData);
      });
    },
  },
};
</script>

<style scoped>
/* 样式 */
</style>
