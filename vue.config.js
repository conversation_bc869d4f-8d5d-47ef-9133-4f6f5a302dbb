const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  transpileDependencies: [],
  lintOnSave: false, // 关闭语法检查
  publicPath: '/',
  assetsDir: 'static',
  parallel: false,
  devServer: {
    proxy: {
      '/api': {
         //target: 'http://113.110.229.83:47025/tpss/',
        // target: 'https://jzljgl.xtzhcg.com:47015/tpss',
        target: 'http://10.129.171.193:5511/tpss/',
        // target: 'https://172.27.25.13:47014/tpss',
        ws: false, // 是否启用websockets
        changeOrisssgin: true, // 开启代理，在本地创建一个虚拟服务端
        secure: false, // 如果是 https 接口，需要配置这个参数
        pathRewrite: {
          "^/api": "", //重写api，把api变成空字符，因为我们真正请求的路径是没有api的
        },
      },
    },
  },

  css: {
    loaderOptions: {
      postcss: {
        postcssOptions: {
          plugins: [
            require('postcss-pxtorem')({
              rootValue: 16,
              unitPrecision: 5,
              propList: ['font', 'font-size', 'line-height', 'letter-spacing'],
              replace: true,
              mediaQuery: false,
              minPixelValue: 0,
              exclude: /node_modules/i,
            }),
          ],
        },
      },
    },
  },
});
