const state = {
    isAddPro: false,
    noPermissionsName:''
};
const mutations = {
    SET_isAddPro: (state, e) => {
        state.isAddPro = e;
    },
    SET_noPermissionsName: (state, e) => {
        state.noPermissionsName = e;
    },
};
function getState(accountStatus, addrStatus, approvalStatus, personStatus) {
    if (accountStatus === 0) {
            if (personStatus !== 1) {
            return 'personStatus';
            //个人信息不合格
        }
          else if (approvalStatus === 0) {
            return 'approvalStatus';
            //健康报告不合格
        }
        //账户状态是否为0
            else if (addrStatus !== 1) {
            return 'addrStatus';
            //地址没有填写不合格
        }
    else
        {
            return true; // 所有字段都有效
        }
    } else {
        return 'accountStatus';
    }
}

const actions = {
    //判断是否有申请项目的权限
    isPerMissions({ commit }, e) {
        var userInfo  = e
        //账户状态
        var accountStatus = userInfo.accountStatus
        //收货地址
        var  approvalStatus = userInfo.approvalStatus 
         //健康问卷是否提交 0未提交 1 提交 
        var addrStatus = userInfo.addrStatus 
        //个人信息是否补充完整
        var personStatus = userInfo.personStatus 
        var state = getState(accountStatus, addrStatus, approvalStatus, personStatus);
        if (typeof (state) === 'boolean') {
            commit('SET_isAddPro', true);
            //把这个清空
            commit('SET_noPermissionsName', '');
        } else {
            commit('SET_isAddPro', false);
         commit('SET_noPermissionsName', state);
        }
  }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
