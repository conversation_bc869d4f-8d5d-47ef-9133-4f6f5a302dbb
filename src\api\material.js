import request from '../utils/request'
//地址管理部分
export const addVolunteerAddrInfo = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/addVolunteerAddrInfo',
        method: "POST",
        data
    });
export const delVolunteerAddrInfo = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/delVolunteerAddrInfo',
        method: "POST",
        data
    });
export const getVolunteerAddrList = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/getVolunteerAddrList',
        method: "POST",
        data
    });
export const updVolunteerAddrInfo = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/updVolunteerAddrInfo',
        method: "POST",
        data
    });
export const isDefaultAddr = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/isDefaultAddr',
        method: "POST",
        data
    });
export const getNewVolunteerAddr = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/getNewVolunteerAddr',
        method: "POST",
        data
    });
//银行部分管理
export const addVolunteerBank = data =>
    request({
        url: '/site/v1/volunteerBankApi/addVolunteerBank',
        method: "POST",
        data
    });
export const getVolunteerBankList = data =>
    request({
        url: '/site/v1/volunteerBankApi/getVolunteerBankList',
        method: "POST",
        data
    });
export const delVolunteerBank = data =>
    request({
        url: '/site/v1/volunteerBankApi/delVolunteerBank',
        method: "POST",
        data
    });
//问卷
export const getPathologicalQuestionList = data =>
    request({
        url: '/site/v1/PathologicalQuestionApi/getPathologicalQuestionList',
        method: "POST",
        data
    });
export const getVolunteerFileList = data =>
    request({
        url: '/site/v1/VolunteerFileApi/getVolunteerFileList',
        method: "POST",
        data
    });
export const batchPathologicalAnswer = data =>
    request({
        url: '/site/v1/PathologicalAnswerApi/batchPathologicalAnswer',
        method: "POST",
        data
    });
export const getAreaTreeList = data =>
    request({
        url: '/site/v1/VolunteerAddrApi/getAreaTreeList',
        method: "POST",
        data
    });
    //附件信息
export const getNewsVolunteerFileList = data =>
    request({
        url: '/site/v1/VolunteerFileApi/getNewsVolunteerFileList',
        method: "POST",
        data
    });
export const addVolunteerFileInfo = data =>
    request({
        url: '/site/v1/VolunteerFileApi/addVolunteerFileInfo',
        method: "POST",
        data
    });
export const getQuestionAnswerList = data =>
    request({
        url: '/site/v1/PathologicalQuestionApi/getQuestionAnswerList',
        method: "POST",
        data
    });
