<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">
          <div class="text-ellipsis">{{ indentData?.agent_name || '违规报警数据' }}</div>
        </div>
      </div>
    </div>
    <div class="xian"></div>
    <div class="bottom flex-row" style="margin-top: 10px">
      <div class="flex-row timebox">
        <van-icon name="location" size="16px" color="#52c41a" />
        <div class="flex-row time">
          <i>违规地址: </i><i class="timedata">{{ indentData?.address || indentData?.agent_name || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="warning-o" size="16px" color="#fa8c16" />
        <div class="flex-row time">
          <i>违规内容: </i><i class="timedata">{{ indentData?.content || '-' }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="logistics" size="16px" color="#1989fa" />
        <div class="flex-row time">
          <i>车辆信息: </i><i class="timedata">{{ getVehicleInfo(indentData) }}</i>
        </div>
      </div>
    </div>
    <div class="bottom flex-row">
      <div class="flex-row timebox">
        <van-icon name="calendar-o" size="16px" color="#722ed1" />
        <div class="flex-row time">
          <i>记录时间: </i><i class="timedata">{{ formatDate(indentData?.end_time) }}</i>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>去审核</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "StandardTransportViolationCard",
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    handleClick(data) {
      this.$router.push({
        name: "TransportViolationsApproval",
        params: { data },
      });
    },

    getVehicleInfo(data) {
      // 根据数据结构获取车辆信息
      if (data?.vehicle_number) {
        return data.vehicle_number;
      }
      if (data?.license_plate) {
        return data.license_plate;
      }
      return '运输车辆'; // 默认显示
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.top {
  padding: 16px;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.pre {
  flex: 1;
}

.text {
  .text-ellipsis {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.xian {
  height: 1px;
  background: #f0f2f5;
  margin: 0 16px;
}

.bottom {
  padding: 8px 16px;
  align-items: center;
  
  .timebox {
    align-items: center;
    width: 100%;
    
    .time {
      margin-left: 8px;
      align-items: center;
      flex: 1;
      
      i {
        font-style: normal;
        font-size: 14px;
        
        &:first-child {
          color: #666;
          min-width: 80px;
        }
        
        &.timedata {
          color: #333;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-colum {
  display: flex;
  flex-direction: column;
}
</style>
