<template>
  <div class="home">
    <NavHeader ref="navHeader" title="现场执法列表" :back="true"></NavHeader>

    <!-- 列表内容区域 -->
    <div class="information cell">
      <div class="indent-box">
        <StandardPatrolCard
          class="card"
          :indentData="item"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></StandardPatrolCard>
      </div>

      <!-- 空状态 -->
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>

    <!-- 案件上传按钮 -->
    <div class="btn-box">
      <yButton title="案件上传" @click="$go('/PatrolModel/add')">
        <template #icon>
          <van-icon name="arrow" />
        </template>
      </yButton>
    </div>
  </div>
</template>

<script>
import { patrolListH5 } from "@/api/config";
import StandardPatrolCard from "@/views/PatrolModel/StandardPatrolCard.vue";
import nullState from "@/components/com/NullCop.vue";
import NavHeader from "@/components/com/NavHeader.vue";
export default {
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      loading: false,
      finished: false,
      option1: [
        {
          text: "请选择渠道",
          value: "",
        },
      ],
      value1: "请选择",
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        applyType: 1,
        applyState: 2,
      },
    };
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    StandardPatrolCard,
    nullState,
    NavHeader,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    addValueAndText(dataArray) {
      return dataArray.map((item) => {
        return {
          ...item,
          value: item.dataCode,
          text: item.dataName,
        };
      });
    },
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSearch() {
      this.formData.page = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },
    getList() {
      if (this.loading) return;
      this.$loadingU.show("请等待....", 5000);
      this.loading = true;
      const reqData = {
        ...this.formData,
      };
      patrolListH5(reqData)
        .then((res) => {
          console.log("获取列表数据：", res);
          if (res.data.success) {
            const newData = res.data.result;
            this.dataList = newData.list;
            console.log("获取到的数据：", this.dataList);
          } else {
            if (this.formData.page === 1) {
              this.dataList = [];
            }
            this.finished = true;
          }
          this.$loadingU.hide();
          this.loading = false;
        })
        .catch(() => {
          this.$loadingU.hide();
          this.loading = false;
        });
    },
    nextPage(item) {
      this.$router.push({ name: "eTjbxx", params: { id: item.etId } });
    },
  },
};
</script>

<style scoped lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px 80px 16px; /* 添加底部内边距避免被按钮遮挡 */

  .indent-box {
    padding-top: 16px;

    .card {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
}

.btn-box {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 100;

  /deep/ .y-button {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}


</style>
