# 统计接口 `/api/minapi/main/statisticsZTC` 字段映射文档

## 接口概述
- **接口地址**: `/minapi/main/statisticsZTC`
- **请求方式**: POST
- **功能**: 获取各业务模块的待审核数量统计
- **用途**: 为首页和各业务页面提供红点提醒数据

## 字段映射关系表

### 1. 产生业务相关字段

| 字段名 | 中文说明 | 对应按钮/功能 | 跳转页面路由 | 所在页面 |
|--------|----------|---------------|-------------|----------|
| `aPendingApprovalCount` | 处置核准审核数量 | 处置核准审核 | `/DisposalPropertyModel/list` | 处置业务页面 |
| `aFinalPendingApprovalCount` | 处置核准终审数量 | 处置核准终审 | `/DisposalPropertyModel/final/list` | 处置业务页面 |
| `cFinalPendingApprovalCount` | 产生核准终审数量 | 产生核准终审 | `/PlaceOrigin/final/list` | 产生地业务页面 |
| `yChuCount` | 处置地延长有效期审核数量 | 延长有效期审核 | `/DisposalAudit/extension/list` | 处置业务页面 |
| `bChuCount` | 处置场所报停审核数量 | 处置场所报停审核 | `/DisposalPropertyModel/baoting/list` | 处置业务页面 |
| `chuCount` | 处置单位注册审核数量 | 处置单位注册审核 | `/DisposalPropertyModel/register/list` | 处置业务页面 |
| `chanCount` | 产生地注册申请审核数量 | 产生单位注册审核 | `/PlaceOrigin/register/list` | 产生地业务页面 |
| `agentCheckCount` | 闲置地违规审核数量 | 闲置地违规审核 | `/IdleViolationModel/list` | 首页违规审核区域 |
| `alarmAentCheckCount` | 运输违规审核数量 | 运输违规审核 | `/TransportViolationsModel/list` | 首页违规审核区域 |
| `yChanCount` | 产生地延期审核数量 | 产生地延期审核 | `/ProductionAudit/yanqi/list` | 产生地业务页面 |
| `bChanCount` | 产生地报停审核数量 | 产生地报停审核 | `/ProductionAudit/stop/list` | 产生地业务页面 |
| `cPendingApprovalCount` | 产生地待审批数量 | - | - | 首页产生业务卡片 |
| `yunCount` | 运输单位注册审核数量 | 运输单位注册审核 | `/HaulwayModel/transport-register/list` | 运输业务页面 |
| `yAgentCount` | 有效期限延长审核数量 | 有效期限延长审核 | `/HaulwayModel/transport-extension/list` | 运输业务页面 |
| `rejectCount` | 运输核准驳回数量 | 运输核准驳回处理 | - | 运输业务页面 |
| `bChanCount` | 产生地报工申请审核数量 | 产生地报工申请审核 | - | 产生地业务页面 |

### 2. 运输业务相关字段

| 字段名 | 中文说明 | 对应按钮/功能 | 跳转页面路由 | 所在页面 |
|--------|----------|---------------|-------------|----------|
| `agentPendingApprovalCount` | 运输核准待审核数量 | 运输核准审核 | `/HaulwayModel/transport-approval` | 运输业务页面 |
| `agentFinalApprovalCount` | 运输核准终审数量 | 运输核准终审 | `/HaulwayModel/transport-final-approval` | 运输业务页面 |
| `chuChangeInfoCount` | 处置地变更信息数量 | 处置地变更信息审核 | - | 处置业务页面 |
| `chuChangeGarbageCount` | 处置地变更垃圾处置类型数量 | 处置地变更垃圾处置类型审核 | - | 处置业务页面 |
| `chanChangeAgentCount` | 产生地变更运输单位数量 | 运输单位变更审核 | `/ProductionAudit/transport/list` | 产生地业务页面 |
| `chuChangeInfoCount` | 处置单位基本资料变更数量 | 处置单位基本资料变更审核 | `/DisposalAudit/company/list` | 处置业务页面 |
| `chuChangeGarbageCount` | 处置类型能力变更数量 | 处置类型能力变更审核 | `/DisposalAudit/capacity/list` | 处置业务页面 |
| `chanChangeGarbageCount` | 产生地变更垃圾类型数量 | 垃圾种类数量审核 | `/ProductionAudit/waste/list` | 产生地业务页面 |
| `chanGarbageCycleCount` | 产生地变更垃圾产生周期数量 | 垃圾产生周期审核 | `/ProductionAudit/cycle/list` | 产生地业务页面 |
| `chanChangeYardCount` | 产生地变更堆放场所数量 | 产生地变更堆放场所审核 | - | 产生地业务页面 |
| `agentCount` | 运输核准审核数量 | 运输核准审核 | - | 运输业务页面 |
| `agentChangeCount` | 企业基本资料变更审核数量 | 企业基本资料变更审核 | `/HaulwayModel/transport-company/list` | 运输业务页面 |
| `agentChangeCarCount` | 运输车辆变更审核数量 | 运输车辆变更审核 | `/HaulwayModel/transport-vehicle/list` | 运输业务页面 |
| `haulwayCount` | 准运证审核数量 | 准运证审核 | `/HaulwayModel/list` | 运输业务页面 |
| `transportApprovalCount` | 运输核准审核数量 | 运输核准审核 | `/HaulwayModel/transport-approval` | 运输业务页面 |
| `agentFinalApprovalCount` | 运输核准终审数量 | 运输核准终审 | `/HaulwayModel/transport-final-approval` | 运输业务页面 |

### 3. 处置业务相关字段

| 字段名 | 中文说明 | 对应按钮/功能 | 跳转页面路由 | 所在页面 |
|--------|----------|---------------|-------------|----------|
| `cFinalPendingApprovalCount` | 处置地终审数量 | 处置地终审 | `/DisposalPropertyModel/final/list` | 处置业务页面 |
| `pendingApprovalCount` | 运输核准待审核数量 | 运输核准待审核 | - | 运输业务页面 |
| `bChuCount` | 处置场所报停审核数量 | 处置场所报停审核 | `/DisposalPropertyModel/baoting/list` | 处置业务页面 |

### 4. 其他业务字段

| 字段名 | 中文说明 | 对应按钮/功能 | 跳转页面路由 | 所在页面 |
|--------|----------|---------------|-------------|----------|
| `patrolCount` | 现场执法数量 | 现场执法 | `/PatrolModel/list` | 首页执法区域 |

## 首页卡片配置

### 产生业务卡片
- **统计字段组合**: `chanCount;yChanCount;bChanCount;cPendingApprovalCount;cFinalPendingApprovalCount;chanChangeAgentCount;chuChangeInfoCount;chanChangeGarbageCount;chanGarbageCycleCount`
- **跳转动作**: `goPlaceOrigin` → `/PlaceOrigin/func`

### 运输业务卡片
- **统计字段组合**: `transportApprovalCount;agentFinalApprovalCount;haulwayCount;agentPendingApprovalCount;yunCount;yAgentCount;agentChangeCount;agentChangeCarCount`
- **跳转动作**: `goHaulwayList` → `/HaulwayModel/func`

### 处置业务卡片
- **统计字段组合**: `aPendingApprovalCount;aFinalPendingApprovalCount;chuCount;bChuCount;cFinalPendingApprovalCount;yChuCount;chuChangeInfoCount;chuChangeGarbageCount`
- **跳转动作**: `goDisposalPropertyList` → `/DisposalPropertyModel/func`

### 运输违规审核卡片
- **统计字段**: `alarmAentCheckCount`
- **跳转动作**: `goTransportViolationsModelList` → `/TransportViolationsModel/list`

### 闲置地违规审核卡片
- **统计字段**: `agentCheckCount`
- **跳转动作**: `goIdleViolationList` → `/IdleViolationModel/list`

## 注意事项

1. **字段命名规律**:
   - `Count` 结尾表示数量统计
   - `Pending` 表示待处理
   - `Final` 表示终审
   - `Change` 表示变更类业务

2. **红点显示逻辑**:
   - 当统计数量 > 0 时显示红点
   - 数量 > 99 时显示 "99+"

3. **数据更新时机**:
   - 应用首页加载时
   - 各业务功能页面初始化时
   - 用户操作后需要刷新统计数据

4. **新增字段处理**:
   - 确认字段对应的业务功能
   - 在相应页面添加按钮和红点显示
   - 配置正确的路由跳转
   - 更新本文档记录

## 开发建议

1. **添加新字段时**:
   - 先确认业务需求和页面设计
   - 按照现有命名规范命名字段
   - 在对应的业务页面添加按钮配置
   - 测试红点显示和页面跳转功能

2. **维护建议**:
   - 定期检查字段使用情况
   - 清理无用的统计字段
   - 保持文档与代码同步更新
