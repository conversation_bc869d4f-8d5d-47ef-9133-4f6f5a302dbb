<template>
  <div class="com-content flex-colum">
    <img src="../assets/images/logo.png" alt="" />
    <div class="pre-box flex-colum">
      <p class="title">未获取到您的登录信息</p>
      <p class="title">请重新登录或稍后再试......</p>
    </div>
    <!-- <div class="start-btn" @click="goback()">返回</div> -->
  </div>
</template>
<script>
import { goback } from "@/utils/com";
import { mapState } from "vuex";
export default {
  data() {
    return {};
  },
  mounted() {},
  created() {},
  computed: {
    ...mapState("user", ["userInfo"]),
  },
  methods: {
    goback() {
      this.$router.go(-1); // 返回上一页
    },
    goTel() {
      var token = localStorage.getItem("TOKEN") || "";
      if (
        Object.keys(this.userInfo).length === 0 ||
        this.userInfo === undefined ||
        this.userInfo === "" ||
        token === ""
      ) {
        this.$go("/index");
        this.$store.dispatch("config/setActive", 0);
      } else {
        this.$go("/index");
        this.$store.dispatch("config/setActive", 0);
      }
    },
  },
};
</script>

<style scoped lang="less">
.com-content {
  width: 100%;
  min-height: 100vh;
  background: #83acf7;
  box-sizing: border-box;
  align-items: center;
}
img {
  width: 60%;
  height: 60%;
  margin-top: 100px;
}

.pre-box {
  margin-top: 40px;
  font-weight: bold;
  color: white;
  text-align: center;
  .title {
    font-size: 24px;
    line-height: 42px;
    color: #ffffff;
  }
  .sub-title {
    font-weight: 400;
    font-size: 24px;
    color: #f2f6fe;
    margin-top: 10px;
  }
}
.page-bottom {
  margin: 0 20px;
  font-weight: normal;
  color: #9ebeff;
  margin-top: 40px;
  text-align: left;
  line-height: 16px;
  .page-bottom-p {
    font-weight: 400;
    font-size: 12px;
  }
  .page-bottom-p1 {
    font-weight: 400;
    font-size: 10px;
  }
}
.start-btn {
  width: 327px;
  height: 54px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  text-align: center;
  color: var(--color-zhuti);
  font-size: 17px;
  color: #0f62f9;
  line-height: 54px;
  margin-top: 40px;
}
</style>
