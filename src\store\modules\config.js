
const state = {
    tabActive: 0,
    kefuLink: '',
    statisticsZTC: {},
    pageData:{}
};

const mutations = {
        SET_pageData: (state, pageData) => {
        state.pageData = pageData;
    },
    SET_statisticsZTC: (state, statisticsZTC) => {
        state.statisticsZTC = statisticsZTC;
    },
    remove_statisticsZTC: (state) => {
        state.statisticsZTC = {};
    },
        clear: (state) => {
        state.pageData = {};
    },

};

const actions = {
        async setActive({ commit },data) {
        commit('SET_pageData', data);
    },


};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
