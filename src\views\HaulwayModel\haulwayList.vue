<template>
  <div class="home">
    <NavHeader
      ref="navHeader"
      title="运输业务申请列表"
      :back="true"
    ></NavHeader>
    <van-sticky :offset-top="navHeight">
      <van-tabs v-model="active">
        <van-tab
          :title="item.text"
          v-for="(item, index) in tabOptions"
          :key="index"
        >
        </van-tab>
      </van-tabs>
    </van-sticky>
    <div class="information cell">
      <div class="indent-box">
        <IndentCard
          class="card"
          :indentData="item"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></IndentCard>
      </div>
      <div v-if="dataList.length === 0" class="center">
        <nullState msg="暂无数据" />
      </div>
    </div>
  </div>
</template>
<script>
import { haulwayListH5 } from "@/api/config";
import IndentCard from "@/views/HaulwayModel/IndentCard.vue";
import nullState from "@/components/com/NullCop.vue";
// import { initDataList } from "@/utils/init.js";
export default {
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: "1", // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: "1" },
        { text: "已完成", value: "2" },
      ],
      option1: [
        {
          text: "请选择渠道",
          value: "",
        },
      ],
      value1: "请选择",
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        typeState: "2",
        baseType: "truck",
      },
    };
  },
  mounted() {
    // this.$loadingU.show("加载中...", 8000);
    // this.columns = [
    //   {
    //     text: "请选择分类",
    //     value: "",
    //   },
    //   ...this.addValueAndText(this.columns),
    // ];
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    IndentCard,
    nullState,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    addValueAndText(dataArray) {
      return dataArray.map((item) => {
        return {
          ...item,
          value: item.dataCode,
          text: item.dataName,
        };
      });
    },
    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },
    handleBack() {
      this.$router.go(-1);
    },
    handleSearch() {
      this.formData.page = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },
    getList() {
      if (this.loading) return;
      this.$loadingU.show("请等待....", 5000);
      this.loading = true;
      const reqData = {
        ...this.formData,
      };
      haulwayListH5(reqData)
        .then((res) => {
          console.log("获取列表数据：", res);
          if (res.data.success) {
            const newData = res.data.result;
            this.dataList = newData.list;
            console.log("获取到的数据：", this.dataList);
          } else {
            if (this.formData.page === 1) {
              this.dataList = [];
            }
            this.finished = true;
          }
          this.$loadingU.hide();
          this.loading = false;
        })
        .catch(() => {
          this.$loadingU.hide();
          this.loading = false;
        });
    },
    nextPage(item) {
      this.$router.push({ name: "eTjbxx", params: { id: item.etId } });
    },
  },
};
</script>

<style scoped lang="less">
.center {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
}
.imgPosi {
  position: relative;
}
.posi-tips {
  position: absolute;
  right: 0;
  top: 0;
  padding: 1px 6px;
  box-sizing: border-box;
  border-radius: 15px;
  color: white;
  background-color: #f56c6c;
}
.type-imgsize {
  width: 20px;
  height: 20px;
  margin: 5px 5px 0;
}
/deep/.van-dropdown-menu__bar {
  box-shadow: none;
}

.listContnet {
  .listHeader {
    display: flex;
    .listName {
      color: rgb(16, 39, 95);
      font-size: 18px;
      font-weight: 700;
      .listXb {
        font-size: 14px;
        font-weight: normal;
      }
    }

    .listText,
    .listTextZd {
      position: relative;
      color: #fff;
      font-size: 12px;
      transform-style: preserve-3d;
      text-align: center;
      padding: 0 5px;
      width: 40px;
      margin-left: 5px;
      margin-right: 15px;
    }

    .listText::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(1, 128, 254);
    }

    .listTextZd-y {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      padding: 3px 5px;
      box-sizing: border-box;
      border-radius: 5px;
      transform: skew(-12deg) translateZ(-1px);
      background-color: rgb(255, 111, 0);
    }
  }

  .listSection {
    display: flex;
    flex-direction: flex-start;

    .img {
      padding-top: 10px;
      margin-right: 10px;
    }

    div.listDiv {
      display: flex;
      align-items: center;
      color: #314b8b;
      font-size: 15px;
      margin-top: 10px;

      svg {
        margin-right: 5px;
      }

      .blue {
        color: rgb(11, 110, 250);
      }
    }
  }

  .listFooter {
    margin-top: 10px;
    .van-col--12 {
      .van-button {
        background-color: rgb(229, 243, 254);
        color: rgb(1, 128, 254);
        padding: 8px 12px;
        border-radius: 5px;
        text-align: center;
        height: auto;
        width: 100%;
        border: none;
        font-weight: 700;
      }
    }
  }
}
.contian {
  width: 100%;
  min-height: 100vh;
  padding: 0 0 144px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  z-index: 0;
}
.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}
.indent-box {
  padding: 12px 14px 24px 14px;
}
.indent-box .card {
  margin-top: 20px;
}
.indent-box .card:nth-child(1) {
  margin-top: 0 !important;
}
.tips-cc {
  font-weight: 400;
  font-size: 14px;
  color: #ce0602;
  line-height: 25px;
}
/deep/.van-tabs__line {
  background-color: var(--color-zhuti-bg) !important;
}
</style>
