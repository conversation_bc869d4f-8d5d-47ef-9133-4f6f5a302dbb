import request from '../utils/request'
export const findListH5 = data =>
    request({
        url: '/minapi/administrativeApply/findListH5',
        method: "POST",
        data
    });
    
export const updateStateH5 = data =>
    request({
        url: '/minapi/administrativeApply/updateStateH5',
        method: "POST",
        data
    });

export const auditH5 = data =>
    request({
        url: '/minapi/administrativeApply/auditH5',
        method: "POST",
        data
    });

export const saveOrUpdateH5 = data =>
    request({
        url: '/minapi/administrativeApply/saveOrUpdateH5',
        method: "POST",
        data
    });
    
export const stationAuditFindListH5 = data =>
    request({
        url: '/minapi/stationAudit/findListH5',
        method: "POST",
        data
    });
    
export const stationAuditAuditH5 = data =>
    request({
        url: '/minapi/stationAudit/auditH5',
        method: "POST",
        data
    });
    
export const haulwayListH5 = data =>
    request({
        url: '/minapi/haulway/haulwayListH5',
        method: "POST",
        data
    });
    
export const updateHaulwaySealH5 = data =>
    request({
        url: '/minapi/haulway/updateHaulwaySealH5',
        method: "POST",
        data
    });
    
export const patrolSaveOrUpdateH5 = data =>
    request({
        url: '/minapi/patrolSaveOrUpdateH5',
        method: "POST",
        data
    });
    
export const upload = data =>
    request({
        url: '/minapi/common/upload',
        method: "POST",
        data
    });
    
export const patrolListH5 = data =>
    request({
        url: '/minapi/patrol/patrolListH5',
        method: "POST",
        data
    });
    

    
export const agentCheckRecordListH5 = data =>
    request({
        url: '/minapi/agentCheckRecord/agentCheckRecordListH5',
        method: "POST",
        data
    });
    
export const sysUserTempListH5 = data =>
    request({
        url: '/minapi/sysUserTemp/sysUserTempListH5',
        method: "POST",
        data
    });
    
export const initLogin = data =>
    request({
        url: '/minapi/zwlogin/initLogin',
        method: "POST",
        data
    });
    
export const addCheckH5 = data =>
    request({
        url: '/minapi/agentCheckRecord/addCheckH5',
        method: "POST",
        data
    });

export const deleteCheck = data =>
    request({
        url: '/minapi/agentCheckRecord/deleteCheck',
        method: "POST",
        data
    });
    
export const sysUserTempUpdateStateH5 = data =>
    request({
        url: '/minapi/sysUserTemp/updateStateH5',
        method: "POST",
        data
    });
    
export const toLogin = data =>
    request({
        url: '/minapi/zwlogin/toLogin',
        method: "POST",
        data
    });
    
    
export const statisticsZTC = data =>
    request({
        url: '/minapi/main/statisticsZTC',
        method: "POST",
        data
    });

// 运输核准审核和终审接口
export const agentFindListH5 = data =>
    request({
        url: '/minapi/agent/findListH5',
        method: "POST",
        data
    });

// 运输核准初审接口
export const agentSaveOrUpdate = data =>
    request({
        url: '/minapi/agent/saveOrUpdate',
        method: "POST",
        data
    });

// 运输核准终审接口
export const agentFinalApproval = data =>
    request({
        url: '/minapi/agent/finalApproval',
        method: "POST",
        data
    });
    
