<template>
  <div class="container">
    <div class="nav-header">
      <div class="nav-content">
        <van-icon name="arrow-left" size="20px" @click="$router.go(-1)" />
        <span class="nav-title">运输车辆变更审核</span>
      </div>
    </div>

    <div class="content">
      <!-- 申请信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3>申请信息</h3>
        </div>
        <div class="info-item">
          <span class="label">申请企业：</span>
          <span class="value">{{ detailData.enterpriseName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">申请时间：</span>
          <span class="value">{{ formatDateTime(detailData.applyDate) }}</span>
        </div>
        <div class="info-item" v-if="detailData.oldVehicleNumber">
          <span class="label">原车牌号：</span>
          <span class="value">{{ detailData.oldVehicleNumber }}</span>
        </div>
        <div class="info-item" v-if="detailData.newVehicleNumber">
          <span class="label">新车牌号：</span>
          <span class="value">{{ detailData.newVehicleNumber }}</span>
        </div>
        <div class="info-item" v-if="detailData.oldVehicleType">
          <span class="label">原车辆类型：</span>
          <span class="value">{{ detailData.oldVehicleType }}</span>
        </div>
        <div class="info-item" v-if="detailData.newVehicleType">
          <span class="label">新车辆类型：</span>
          <span class="value">{{ detailData.newVehicleType }}</span>
        </div>
        <div class="info-item" v-if="detailData.oldLoadCapacity">
          <span class="label">原载重量：</span>
          <span class="value">{{ detailData.oldLoadCapacity }}吨</span>
        </div>
        <div class="info-item" v-if="detailData.newLoadCapacity">
          <span class="label">新载重量：</span>
          <span class="value">{{ detailData.newLoadCapacity }}吨</span>
        </div>
        <div class="info-item" v-if="detailData.content">
          <span class="label">变更理由：</span>
          <span class="value">{{ detailData.content }}</span>
        </div>
      </div>

      <!-- 审核操作区域 -->
      <div class="audit-card" v-if="detailData.typeState === 1">
        <!-- 审核结果 -->
        <div class="audit-item">
          <div class="audit-label">审核结果</div>
          <div class="simple-radio-group">
            <van-radio-group v-model="form.state" direction="horizontal">
              <van-radio :name="1" class="radio-option">通过</van-radio>
              <van-radio :name="2" class="radio-option">不通过</van-radio>
            </van-radio-group>
          </div>
        </div>

        <!-- 确认车辆信息 -->
        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认车牌号</div>
          <van-field
            v-model="form.confirmedVehicleNumber"
            placeholder="请输入确认的车牌号"
            class="business-field"
          />
        </div>

        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认车辆类型</div>
          <van-field
            v-model="form.confirmedVehicleType"
            placeholder="请输入确认的车辆类型"
            class="business-field"
          />
        </div>

        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认载重量（吨）</div>
          <van-field
            v-model="form.confirmedLoadCapacity"
            placeholder="请输入确认的载重量"
            type="number"
            class="business-field"
          />
        </div>

        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认运输能力（吨/日）</div>
          <van-field
            v-model="form.confirmedTransportCapacity"
            placeholder="请输入确认的运输能力"
            type="number"
            class="business-field"
          />
        </div>

        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认驾驶员</div>
          <van-field
            v-model="form.confirmedDriver"
            placeholder="请输入确认的驾驶员姓名"
            class="business-field"
          />
        </div>

        <div class="audit-item" v-if="form.state === 1">
          <div class="audit-label">确认联系电话</div>
          <van-field
            v-model="form.confirmedDriverPhone"
            placeholder="请输入确认的联系电话"
            type="tel"
            class="business-field"
          />
        </div>

        <!-- 审核备注 -->
        <div class="audit-item">
          <div class="audit-label">审核备注</div>
          <van-field
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入审核备注"
            rows="3"
            autosize
            class="business-field"
          />
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            size="large"
            :loading="isSubmitting"
            @click="submitAudit"
            class="submit-btn"
          >
            提交审核
          </van-button>
        </div>
      </div>

      <!-- 已完成状态显示 -->
      <div class="result-card" v-else>
        <div class="card-header">
          <h3>审核结果</h3>
        </div>
        <div class="info-item">
          <span class="label">审核状态：</span>
          <span class="value" :class="getStatusClass(detailData.typeState)">
            {{ getStatusText(detailData.typeState) }}
          </span>
        </div>
        <div class="info-item" v-if="detailData.auditTime">
          <span class="label">审核时间：</span>
          <span class="value">{{ formatDateTime(detailData.auditTime) }}</span>
        </div>
        <div class="info-item" v-if="detailData.remarks">
          <span class="label">审核备注：</span>
          <span class="value">{{ detailData.remarks }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { stationAuditAuditH5 } from "@/api/config";

export default {
  name: "TransportVehicleApproval",
  data() {
    return {
      detailData: {},
      form: {
        state: "",
        remarks: "",
        confirmedVehicleNumber: "",
        confirmedVehicleType: "",
        confirmedLoadCapacity: "",
        confirmedTransportCapacity: "",
        confirmedDriver: "",
        confirmedDriverPhone: "",
      },
      isSubmitting: false,
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      try {
        const data = this.$route.params.data;
        if (data) {
          this.detailData = JSON.parse(data);
          console.log("详情数据：", this.detailData);
          
          // 预填充确认信息
          this.form.confirmedVehicleNumber = this.detailData.newVehicleNumber || "";
          this.form.confirmedVehicleType = this.detailData.newVehicleType || "";
          this.form.confirmedLoadCapacity = this.detailData.newLoadCapacity || "";
          this.form.confirmedTransportCapacity = this.detailData.newTransportCapacity || "";
          this.form.confirmedDriver = this.detailData.newDriver || "";
          this.form.confirmedDriverPhone = this.detailData.newDriverPhone || "";
        }
      } catch (error) {
        console.error("解析数据失败：", error);
        this.$toast.fail("数据解析失败");
        this.$router.go(-1);
      }
    },

    formatDateTime(dateTime) {
      if (!dateTime) return "-";
      return dateTime.replace(/T/, " ").substring(0, 19);
    },

    getStatusText(status) {
      const statusMap = {
        1: "待审核",
        2: "审核通过",
        3: "审核不通过",
      };
      return statusMap[status] || "未知状态";
    },

    getStatusClass(status) {
      const classMap = {
        1: "status-pending",
        2: "status-approved",
        3: "status-rejected",
      };
      return classMap[status] || "";
    },

    validateForm() {
      if (!this.form.state) {
        this.$toast.fail("请选择审核结果");
        return false;
      }

      if (this.form.state === 1) {
        if (!this.form.confirmedVehicleNumber.trim()) {
          this.$toast.fail("请输入确认的车牌号");
          return false;
        }
        if (!this.form.confirmedVehicleType.trim()) {
          this.$toast.fail("请输入确认的车辆类型");
          return false;
        }
        if (!this.form.confirmedLoadCapacity) {
          this.$toast.fail("请输入确认的载重量");
          return false;
        }
        if (!this.form.confirmedTransportCapacity) {
          this.$toast.fail("请输入确认的运输能力");
          return false;
        }
      }

      if (!this.form.remarks.trim()) {
        this.$toast.fail("请输入审核备注");
        return false;
      }

      return true;
    },

    buildRequestData() {
      const reqData = {
        id: this.detailData.id,
        state: this.form.state,
        remarks: this.form.remarks,
      };

      if (this.form.state === 1) {
        reqData.confirmedVehicleNumber = this.form.confirmedVehicleNumber;
        reqData.confirmedVehicleType = this.form.confirmedVehicleType;
        reqData.confirmedLoadCapacity = this.form.confirmedLoadCapacity;
        reqData.confirmedTransportCapacity = this.form.confirmedTransportCapacity;
        reqData.confirmedDriver = this.form.confirmedDriver;
        reqData.confirmedDriverPhone = this.form.confirmedDriverPhone;
      }

      return reqData;
    },

    async submitAudit() {
      console.log("提交的数据：", this.form);
      
      if (!this.validateForm()) return;
      
      const reqData = this.buildRequestData();
      console.log("提交的请求数据：", reqData);
      
      this.isSubmitting = true;
      try {
        const res = await stationAuditAuditH5(reqData);
        if (res.data.success || res.data.state === "success") {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.nav-header {
  background: #ffffff;
  border-bottom: 1px solid #f0f2f5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  height: 44px;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-left: 12px;
}

.content {
  padding: 16px;
}

.info-card,
.audit-card,
.result-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.card-header {
  margin-bottom: 16px;
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0;
  }
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .label {
    font-size: 14px;
    color: #8c8c8c;
    min-width: 100px;
    flex-shrink: 0;
  }
  
  .value {
    font-size: 14px;
    color: #262626;
    flex: 1;
    word-break: break-all;
  }
}

.audit-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.audit-label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.simple-radio-group {
  .van-radio-group {
    display: flex;
    gap: 24px;
  }
  
  .radio-option {
    font-size: 14px;
  }
}

.business-field {
  background: #fafafa;
  border-radius: 8px;
  
  :deep(.van-field__control) {
    font-size: 14px;
    color: #262626;
  }
}

.submit-section {
  margin-top: 24px;
}

.submit-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.status-pending {
  color: #faad14;
}

.status-approved {
  color: #52c41a;
}

.status-rejected {
  color: #ff4d4f;
}
</style>
