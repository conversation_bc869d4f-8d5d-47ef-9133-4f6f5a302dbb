<template>
  <div :class="['box', isChange ? 'active' : 'noactive', 'flex-colum']">
    <p class="title">số thứ tự:{{ cardData.orderNo }}</p>
    <div
      class="flex-row"
      style="align-items: center; justify-content: space-between"
    >
      <div class="flex-row" style="align-items: center">
        <van-icon name="paid" size="24px" :color="isChange ? '#0f62f9' : ''" />
        <p>Số tiền hoa hồng phụ:{{ cardData.sharingMoney }}</p>
        <!-- 分佣金额 -->
      </div>
    </div>
    <div
      class="flex-row margin-c"
      style="align-items: center; justify-content: space-between"
    >
      <div class="flex-row" style="align-items: center">
        <van-icon
          name="balance-list-o"
          size="24px"
          :color="isChange ? '#0f62f9' : ''"
        />
        <!-- 分佣比例 -->
        <p>tỷ lệ hoa hồng:{{ cardData.sharingRatio }}</p>
      </div>
    </div>
    <div
      class="flex-row margin-c"
      style="align-items: center; justify-content: space-between"
    >
      <div class="flex-row" style="align-items: center">
        <van-icon
          name="coupon-o"
          size="24px"
          :color="isChange ? '#0f62f9' : ''"
        />
        <!-- 记录时间 -->
        <p>Ghi lại thời gian:{{ cardData.regTime }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    cardData: {
      type: Object,
      default: () => ({}),
    },
    isChange: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
  mounted() {},
  methods: {
    goEdit() {},
  },
};
</script>

<style scoped lang="less">
.activetips {
  padding: 3px 8px;
  color: white;
  font-size: 11px;
  box-sizing: border-box;
  border-radius: 0 12px 0 12px;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  background-color: var(--color-zhuti);
}
.box {
  padding: 16px;
  position: relative;
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 12px;
}
.active {
  background: #f2f6fe;
  border: 1px solid #9ebaed;
}
.noactive {
  background-color: #fff;
  border: 1px solid #f2f4f5;
}
p {
  font-weight: 600;
  font-size: 14px;
  color: #112950;
  line-height: 18px;
}
.title {
  margin-bottom: 10px;
}
.change {
  color: var(--color-zhuti) !important;
}
.margin-c {
  margin-top: 10px;
}
</style>
