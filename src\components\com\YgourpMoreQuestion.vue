<template>
  <div class="custom-input">
    <div class="label">
      {{ content }}
    </div>
    <div class="input-container flex-row">
      <slot name="left-icon" v-if="hasLeftIcon"></slot>
      <div class="input-field" :style="inputFieldStyle">
        <van-checkbox-group
          v-model="result"
          direction="horizontal"
          @change="handleInput"
          class="flex-colum gorup"
        >
          <van-checkbox v-if="quesData.astr" :name="quesData.astr"
            >A.{{ quesData.astr }}</van-checkbox
          >
          <van-checkbox v-if="quesData.bstr" :name="quesData.bstr"
            >B.{{ quesData.bstr }}</van-checkbox
          >
          <van-checkbox :name="quesData.cstr" v-if="quesData.cstr"
            >C.{{ quesData.cstr }}</van-checkbox
          >
          <van-checkbox :name="quesData.dstr" v-if="quesData.dstr"
            >D.{{ quesData.dstr }}</van-checkbox
          >
        </van-checkbox-group>
      </div>
      <div class="xian"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomInput",
  props: {
    quesData: {
      type: Object,
      default: () => ({}),
    },
    isEnabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      result: [],
      content: "",
      inputValue: "",
    };
  },
  computed: {
    hasLeftIcon() {
      return !!this.$slots["left-icon"];
    },
    inputFieldStyle() {
      return {
        borderBottomColor: this.isEnabled
          ? this.radio !== ""
            ? "#0f62f9"
            : "grey"
          : "grey",
        paddingLeft: this.hasLeftIcon ? "30px" : "0",
      };
    },
  },
  mounted() {
    this.handleStr(this.quesData.contentStr);
  },
  methods: {
    //进度：选项改变括号内容（待完善）
    handleStr(e) {
      this.content = e.replace(/（\s*）/g, "（    ）");
    },
    handleInput() {
      var answer = {};
      var a = this.result.join(",");
      if (this.$listeners["input-change"]) {
        answer = {
          pathologicalId: this.quesData.pathologicalId,
          answerStr: a,
        };
        this.$emit("input-change", answer);
      }
      if (this.$listeners["input-change-record"]) {
        answer = {
          question: this.quesData.contentStr,
          answerStr: a,
        };
        this.$emit("input-change-record", answer);
      }
    },
  },
};
</script>

<style scoped lang="less">
.custom-input {
  width: 100%;
  display: inline-block;
}

.input-container {
  position: relative;
  align-items: center;
  width: 100%;
}
.label {
  font-weight: 600;
  font-size: 16px;
  color: #112950;
}
.input-field {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 15px;
  color: #112950;
  border-bottom: 2px solid grey;
  width: 100%;
}
.xian {
  border-bottom-color: #aaa;
}
.gorup {
  .van-checkbox {
    margin: 5px 0;
  }
}
</style>
