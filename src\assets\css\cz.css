body,
ol,
ul,
h1,
h2,
h3,
h4,
h5,
h6,
p,
th,
td,
dl,
dd,
form,
fieldset,
legend,
input,
textarea,
select,
td {
    margin: 0;
    padding: 0;
}

html,
body {
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    word-wrap: break-word;
}

a,
u,
s,
del {
    color: #666;
    text-decoration: none;
}

fieldset,
a img,
.bor0 {
    border: 0;
}

i,
em,
b {
    font-style: normal;
    font-weight: 400;
    font-family: Arial, Helvetica, sans-serif;
}

li {
    list-style: none;
}

img {
    vertical-align: middle;
}

table {
    border-collapse: collapse;
}

.ind2 {
    text-indent: 2em;
}

textarea {
    resize: none;
}

textarea,
input {
    outline: 0;
}

.valign {
    text-align: center;
}

.valign img {
    vertical-align: middle;
}

.valign:after {
    content: "";
    display: inline-block;
    vertical-align: middle;
    height: 100%;
}

.over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.clear:after {
    content: "";
    clear: both;
    display: block;
}

.clear:before {
    content: "";
    display: table;
}

.left {
    float: left !important;
}

.right {
    float: right !important;
}

/* :hover {

    cursor: pointer
} */