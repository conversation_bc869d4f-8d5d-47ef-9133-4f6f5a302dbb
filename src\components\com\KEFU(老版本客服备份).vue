<template>
  <div class="contain flex-colum">
    <img
      src="@/assets/images/kefu.png"
      @click="goKefu()"
      class="imgSize"
      alt=""
    />
    <div class="kf">dịch vụ khách hàng</div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {};
  },

  components: {},
  computed: {
    ...mapState("config", ["kefuLink"]),
  },
  mounted() {},
  methods: {
    goKefu() {
      if (this.kefuLink !== "") {
        window.location.href = this.kefuLink;
      } else {
        // 没有找到客服链接
        this.$toast({
          duration: 2000,
          message: "<PERSON>hông tìm thấy liên kết dịch vụ khách hàng",
        });
      }
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="less" scoped>
.contain {
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 20px;
  bottom: 150px;
}
.imgSize {
  width: 80px;
  height: 80px;
}
.kf {
  font-weight: 600;
  font-size: 8px;
  color: #ffffff;
  margin-top: -15px;
  line-height: 23px;
  text-align: center;
  padding: 0 5px;
  box-sizing: border-box;
  background: #0065ff;
  border-radius: 12px 12px 12px 12px;
}
</style>
