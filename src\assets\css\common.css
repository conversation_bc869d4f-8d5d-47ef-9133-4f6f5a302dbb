.boxCard {
  width: calc(100vw - 4vw);
  height: calc(100vh - 20vh);
  background-color: var(--background-white);
  box-sizing: border-box;
  margin: 2vw;
  border-radius: 10px;
  border: 1px solid var(--border-card-color);
  position: relative;
}

/* 表格 滚动条 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #f2f2f2;
  border-radius: 5px;

}

/* 表格 滚动条 */
::-webkit-scrollbar-thumb {
  -webkit-box-shadow: 0 0 0px 3px #f2f2f2 inset;
  border-radius: 5px;
  border: none;
  background-color: #a2a3a5;
  opacity: 0.8;
}

.serial {
  position: absolute;
  bottom: 5vh;
  width: 100%;
  text-align: center;

}

.flex-colum {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
}

.yh-p {
  font-size: 14px;
  color: var(--font-color-1);
}

.yh-p2 {
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;
  color: var(--font-color-2);
}

.white {
  color: #ffffff;
}

.input-icon{
  width: 20px;
  height: 20px;
  margin-right:-20px;
}
.text-ellipsis {
  white-space: nowrap !important;
  /* 禁止换行 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
  text-overflow: ellipsis !important;
  /* 显示省略号 */
}
.text-ellipsis-2 {
  display: -webkit-box;
  /* 使用flex-box布局 */
  -webkit-line-clamp: 2;
  /* 限制显示的行数 */
  -webkit-box-orient: vertical;
  /* 垂直方向排列 */
  overflow: hidden;
  /* 隐藏溢出部分 */
}