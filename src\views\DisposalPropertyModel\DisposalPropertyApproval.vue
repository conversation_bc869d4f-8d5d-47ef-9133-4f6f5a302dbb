<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="isViewMode ? '处置地审核详情' : '处置地审核'" :back="true"></NavHeader>

    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.engineeringName"
              name="content"
              label="处置地名称："
              :readonly="true"
            />
            <van-field
              :value="infoData?.coordinateInfo"
              name="content"
              label="处置地地址："
              :readonly="true"
            >
              <template #button>
                <van-button
                  size="mini"
                  type="primary"
                  @click="showMap"
                  icon="location-o"
                  round
                  class="map-btn"
                >
                  地图
                </van-button>
              </template>
            </van-field>
            <van-field
              :value="getDisposalTypes()"
              name="content"
              label="处置类型："
              :readonly="true"
            />
            <van-field
              :value="infoData?.landCharacterDic?.dicValue || '-'"
              name="content"
              label="用地性质："
              :readonly="true"
            />
            <van-field
              :value="getLandUseValidityPeriod()"
              name="content"
              label="使用有效期："
              :readonly="true"
            />
            <van-field
              :value="infoData?.allCapacity ? `${infoData.allCapacity} 万立方米` : '-'"
              name="content"
              label="设计库容："
              :readonly="true"
            />
            <!-- <van-field
              :value="infoData?.addressCity?.fullAreaName || '-'"
              name="content"
              label="所属地区："
              :readonly="true"
            /> -->
            <van-field
              :value="infoData?.agent?.agentName || '-'"
              name="content"
              label="单位名称："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section">
      <LabelHeader left="申请材料"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <!-- 按固定分类显示申请材料 -->
            <div class="categorized-attachments">
              <!-- 行政许可申请书 -->
              <div class="attachment-category">
                <div class="category-title">行政许可申请书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs1')"
                    :key="'applyImgs1-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs1').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 营业执照或法人证书 -->
              <div class="attachment-category">
                <div class="category-title">营业执照或法人证书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs2')"
                    :key="'applyImgs2-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs2').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 授权委托书 -->
              <div class="attachment-category">
                <div class="category-title">授权委托书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs3')"
                    :key="'applyImgs3-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs3').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 信用承诺书 -->
              <div class="attachment-category">
                <div class="category-title">信用承诺书</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs4')"
                    :key="'applyImgs4-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs4').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 土地使用证明 -->
              <div class="attachment-category">
                <div class="category-title">土地使用证明</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs5')"
                    :key="'applyImgs5-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs5').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 环境影响评价批复 -->
              <div class="attachment-category">
                <div class="category-title">环境影响评价批复</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs6')"
                    :key="'applyImgs6-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs6').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 场地平面图 -->
              <div class="attachment-category">
                <div class="category-title">场地平面图</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs7')"
                    :key="'applyImgs7-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs7').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 进场路线图 -->
              <div class="attachment-category">
                <div class="category-title">进场路线图</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs8')"
                    :key="'applyImgs8-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs8').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 环境卫生制度 -->
              <div class="attachment-category">
                <div class="category-title">环境卫生制度</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs9')"
                    :key="'applyImgs9-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs9').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 安全管理制度 -->
              <div class="attachment-category">
                <div class="category-title">安全管理制度</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs10')"
                    :key="'applyImgs10-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs10').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 垃圾分类处理方案 -->
              <div class="attachment-category">
                <div class="category-title">垃圾分类处理方案</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs11')"
                    :key="'applyImgs11-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs11').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>

              <!-- 申请单位基本信息表 -->
              <div class="attachment-category">
                <div class="category-title">申请单位基本信息表</div>
                <div class="category-files">
                  <div
                    v-for="(item, index) in getAttachmentsByType('applyImgs12')"
                    :key="'applyImgs12-' + index"
                    class="attachment-row"
                    @click="previewFile(item)"
                  >
                    <div v-if="isImageFile(item.filePath)" class="image-row">
                      <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                      <div class="attachment-title">{{ item.displayTitle }}</div>
                    </div>
                    <div v-else class="file-row">
                      <div class="file-info">
                        <div class="file-icon">
                          <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                        </div>
                        <div class="file-details">
                          <div class="file-name">{{ item.displayTitle }}</div>
                          <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                        </div>
                        <div class="file-action">
                          <van-icon name="eye-o" size="20px" color="#1989fa" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="getAttachmentsByType('applyImgs12').length === 0" class="no-files">暂无文件</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <LabelHeader left="审核情况" v-if="!isReadOnly"></LabelHeader>
    <div class="information" v-if="!isReadOnly">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="applyState" label="审核：" label-width="120px">
              <template #input>
                <van-radio-group
                  v-model="form.applyState"
                  direction="horizontal"
                >
                  <van-radio :name="8">通过</van-radio>
                  <van-radio :name="3" style="margin-left: 20px">不通过</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.ruralDevelopmentRemarks"
              name="ruralDevelopmentRemarks"
              label-width="120px"
              rows="3"
              autosize
              label="审核备注:"
              type="textarea"
              maxlength="100"
              placeholder="请输入备注"
              show-word-limit
            />
          </van-form>
          <div class="btn-box">
            <yButton :top150="true" title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 只读模式 - 审核结果展示 -->
    <LabelHeader left="审核结果" v-if="isReadOnly"></LabelHeader>
    <div class="information" v-if="isReadOnly">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field name="applyState" label="审核结果：" label-width="120px">
              <template #input>
                <van-radio-group
                  :value="infoData.applyState"
                  direction="horizontal"
                  disabled
                >
                  <van-radio :name="8">通过</van-radio>
                  <van-radio :name="3" style="margin-left: 20px">不通过</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              :value="getAuditRemarks(infoData)"
              name="auditRemarks"
              label="审核备注："
              label-width="120px"
              type="textarea"
              rows="3"
              :readonly="true"
            />
            <van-field
              :value="infoData.auditTime || ''"
              name="auditDate"
              label="审核时间："
              label-width="120px"
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { updateStateH5 } from "@/api/config";
import { FILE_BASE_URL } from "@/utils/globalConstants";
import { ImagePreview } from 'vant';
import LabelHeader from "@/components/com/LabelHeader.vue";
import NavHeader from "@/components/com/NavHeader.vue";
import yButton from "@/components/com/ComBtn.vue";

export default {
  name: "DisposalPropertyApproval",
  components: {
    LabelHeader,
    NavHeader,
    yButton,
  },
  data() {
    return {
      form: {
        applyState: "",
        ruralDevelopmentRemarks: "",
      },
      showCalendar: false,
      rules: {
        applyState: [{ required: true, message: "请选择审核结果" }],
        ruralDevelopmentRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: null,
      isSubmitting: false,
    };
  },
  computed: {
    // 判断是否为查看模式
    isViewMode() {
      // 从已完成tab进入
      const fromCompletedTab = this.$route.query.from === 'completed';

      // 根据applyState判断：8-通过, 3-不通过
      const isCompletedStatus = this.infoData && (this.infoData.applyState === 8 || this.infoData.applyState === 3);
      return fromCompletedTab || isCompletedStatus;
    },
    hasAttachments() {
      // 总是显示申请材料部分，因为我们要显示固定的分类标题
      return true;
    },
    isReadOnly() {
      return this.infoData && (this.infoData.applyState === 8 || this.infoData.applyState === 3);
    },
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 根据attachType获取对应的附件
    getAttachmentsByType(attachType) {
      if (!this.infoData || !this.infoData.attachments) {
        return [];
      }

      // 只返回指定类型且有实际文件路径的附件
      return this.infoData.attachments.filter(item =>
        item.attachType === attachType &&
        item.filePath &&
        item.filePath.trim() !== '' &&
        item.filePath !== null &&
        item.filePath !== undefined
      );
    },

    initData() {
      const dataParam = this.$route.params.data;
      
      try {
        if (typeof dataParam === 'string') {
          this.infoData = JSON.parse(decodeURIComponent(dataParam));
        } else {
          this.infoData = dataParam;
        }
        
        console.log("获取到的申请数据：", this.infoData);

        // 打印申请材料JSON数据
        console.log("=== 处置业务申请材料数据 ===");
        console.log("attachments 完整数据：", JSON.stringify(this.infoData.attachments, null, 2));
        if (this.infoData.attachments && this.infoData.attachments.length > 0) {
          this.infoData.attachments.forEach((item, index) => {
            console.log(`附件${index + 1}:`, {
              attachType: item.attachType,
              displayTitle: item.displayTitle,
              filePath: item.filePath
            });
          });
        }
      } catch (error) {
        console.error("解析申请数据失败：", error);
        this.$toast.fail("数据格式错误");
        this.$router.go(-1);
      }
    },

    getDisplayTitle() {
      return this.infoData?.agent?.agentName || this.infoData?.engineeringName || '处置地申请';
    },

    getValidityPeriod() {
      const start = this.infoData?.beginValidityTime;
      const end = this.infoData?.endValidityTime;
      if (start && end) {
        return `${start} - ${end}`;
      }
      return '-';
    },

    // 获取处置类型（从moreGarbageTypeList数组中提取dicValue）
    getDisposalTypes() {
      if (!this.infoData || !this.infoData.moreGarbageTypeList || this.infoData.moreGarbageTypeList.length === 0) {
        return '-';
      }

      const types = this.infoData.moreGarbageTypeList
        .filter(item => item.dicValue)
        .map(item => item.dicValue);

      return types.length > 0 ? types.join('、') : '-';
    },

    // 获取使用有效期
    getLandUseValidityPeriod() {
      const start = this.infoData?.landUseValidityStart;
      const end = this.infoData?.landUseValidityEnd;
      if (start && end) {
        return `${start} 至 ${end}`;
      }
      return '-';
    },

    // 查看地图功能
    showMap() {
      this.$router.push({
        name: "CheckMap",
        params: { data: this.infoData },
      });
    },

    getStatusClass() {
      if (this.infoData?.applyState === 8) return 'status-approved';
      if (this.infoData?.applyState === 3) return 'status-rejected';
      return 'status-pending';
    },

    getStatusText() {
      if (this.infoData?.applyState === 8) return '已通过';
      if (this.infoData?.applyState === 3) return '已驳回';
      return '待审核';
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      try {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    // 获取审核备注（兼容不同字段名）
    getAuditRemarks(data) {
      return data.ruralDevelopmentRemarks || data.acceptedRemarks || '无';
    },
    // 文件预览相关方法
    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      return `${FILE_BASE_URL}${filePath}`;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PowerPoint演示文稿';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        // 图片预览 - 使用直接导入的 ImagePreview 组件
        ImagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    validateForm() {
      if (!this.form.applyState) {
        this.$toast.fail("请选择审核结果");
        return false;
      }
      if (!this.form.ruralDevelopmentRemarks) {
        this.$toast.fail("请输入审核备注");
        return false;
      }
      return true;
    },
    async sumBit() {
      console.log("提交的数据：", this.form);
      
      if (!this.validateForm()) return;
      
      const reqData = {
        id: this.infoData.id,
        applyState: this.form.applyState,
        ruralDevelopmentRemarks: this.form.ruralDevelopmentRemarks,
        applyDate: this.formatDateTime(new Date()), // 添加初审日期
      };
      
      console.log("构建的请求数据：", reqData);
      
      this.isSubmitting = true;
      try {
        const res = await updateStateH5(reqData);
        console.log("API响应：", res);
        
        if (res.data.state === "success" || res.data.success) {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  padding-bottom: 20px;
}



// 与产生地一致的样式
.information {
  padding: 0 16px 16px;

  .y-card-box {
    .y-card {
      background: #ffffff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
      border: 1px solid #f0f2f5;
    }
  }
}

// 表单样式
:deep(.van-field) {
  padding: 12px 0;

  .van-field__label {
    color: #262626;
    font-weight: 500;
  }

  .van-field__value {
    color: #595959;
  }
}

// 通用标题样式
.section-title {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 0;
  
  span {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-left: 8px;
  }
}

// 附件部分
.attachments-section {
  .categorized-attachments {
    .attachment-category {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
        padding: 0 4px;
      }

      .category-files {
        .attachment-row {
          margin-bottom: 12px;
          cursor: pointer;

          &:last-child {
            margin-bottom: 0;
          }

          // 图片行样式
          .image-row {
            img {
              width: 100%;
              max-height: 200px;
              object-fit: contain;
              border-radius: 8px;
              background: #f5f5f5;
              border: 1px solid #e6f4ff;
            }

            .attachment-title {
              font-size: 12px;
              color: #8c8c8c;
              text-align: center;
              margin-top: 6px;
              line-height: 18px;
            }
          }

          // 文件行样式
          .file-row {
            .file-info {
              display: flex;
              align-items: center;
              padding: 12px;
              background: #ffffff;
              border: 1px solid #e6f4ff;
              border-radius: 8px;
              transition: all 0.3s;

              &:hover {
                border-color: #1989fa;
                box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
              }

              .file-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8faff;
                border-radius: 6px;
                margin-right: 10px;
              }

              .file-details {
                flex: 1;

                .file-name {
                  font-size: 13px;
                  color: #262626;
                  font-weight: 500;
                  margin-bottom: 2px;
                  line-height: 18px;
                }

                .file-type {
                  font-size: 11px;
                  color: #8c8c8c;
                  line-height: 14px;
                }
              }

              .file-action {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }

        .no-files {
          font-size: 12px;
          color: #8c8c8c;
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px dashed #d9d9d9;
        }
      }
    }
  }
}

// 按钮容器样式
.btn-box {
  margin-top: 24px;
  padding: 0 16px;
}

// 单选按钮组样式
:deep(.van-radio-group) {
  .van-radio {
    margin-right: 16px;

    .van-radio__label {
      color: #262626;
      font-size: 14px;
    }
  }
}

// 单选按钮样式
.van-radio {
  margin: 5px 3px;
}

// 地图按钮样式
.map-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}
</style>
