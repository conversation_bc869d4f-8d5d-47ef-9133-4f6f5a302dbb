<template>
  <div class="container">
    <NavHeader
      ref="navHeader"
      title="审核详情"
      :back="true"
    ></NavHeader>

    <!-- 申请信息概览 -->
    <div class="overview-section">
      <div class="overview-card">
        <div class="overview-header">
          <div class="site-name">{{ getDisplayTitle() }}</div>
          <div class="apply-time">{{ formatDateTime(infoData?.applyDate) }}</div>
        </div>
        <div class="overview-content">
          <div class="info-item">
            <span class="label">产生地地址</span>
            <span class="value">{{ getAddress() }}</span>
          </div>
          <div class="info-item">
            <span class="label">原产生周期</span>
            <span class="value">{{ infoData?.originalCycle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">申请变更为</span>
            <span class="value">{{ infoData?.newCycle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">变更理由</span>
            <span class="value">{{ infoData?.content || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <div class="section-title">
        <van-icon name="photo-o" size="18px" color="#1989fa" />
        <span>申请材料</span>
      </div>

      <!-- 所有文件 - 每行一个 -->
      <div class="all-attachments">
        <div
          v-for="(item, index) in infoData.attachment"
          :key="index"
          class="attachment-row"
          @click="previewFile(item)"
        >
          <!-- 图片文件显示 -->
          <div v-if="isImageFile(item.filePath)" class="image-row">
            <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
            <div class="attachment-title">{{ item.displayTitle }}</div>
          </div>

          <!-- 非图片文件显示 -->
          <div v-else class="file-row">
            <div class="file-info">
              <div class="file-icon">
                <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
              </div>
              <div class="file-details">
                <div class="file-name">{{ item.displayTitle }}</div>
                <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
              </div>
              <div class="file-action">
                <van-icon name="eye-o" size="20px" color="#1989fa" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核操作 -->
    <div class="audit-section" v-if="!isReadOnly">
      <div class="section-title">
        <van-icon name="completed" size="18px" color="#1989fa" />
        <span>审核操作</span>
      </div>

      <div class="audit-card">
        <!-- 审核结果 -->
        <div class="audit-item">
          <div class="audit-label">审核结果</div>
          <div class="simple-radio-group">
            <van-radio-group v-model="form.state" direction="horizontal">
              <van-radio :name="1" class="radio-option">同意</van-radio>
              <van-radio :name="2" class="radio-option">不同意</van-radio>
            </van-radio-group>
          </div>
        </div>

        <!-- 审核备注 -->
        <div class="audit-item">
          <div class="audit-label">审核备注</div>
          <van-field
            v-model="form.remarks"
            type="textarea"
            rows="4"
            maxlength="100"
            placeholder="请输入审核备注..."
            show-word-limit
            class="remarks-field"
          />
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button
          type="primary"
          size="large"
          block
          @click="submitAudit()"
          :loading="isSubmitting"
          class="submit-btn"
        >
          {{ isSubmitting ? '提交中...' : '提交审核' }}
        </van-button>
      </div>
    </div>

    <!-- 已完成状态的审核结果展示 -->
    <div class="audit-result-section" v-if="isReadOnly">
      <div class="section-title">
        <van-icon name="completed" size="18px" color="#1989fa" />
        <span>审核结果</span>
      </div>

      <div class="audit-card">
        <!-- 审核状态 -->
        <div class="audit-item">
          <div class="audit-label">审核状态</div>
          <div class="audit-value">
            <span :class="['status-tag', infoData.state === 1 ? 'status-approved' : 'status-rejected']">
              {{ infoData.state === 1 ? '已同意' : '已拒绝' }}
            </span>
          </div>
        </div>

        <!-- 审核备注 -->
        <div class="audit-item" v-if="infoData.remarks">
          <div class="audit-label">审核备注</div>
          <div class="audit-value">{{ infoData.remarks }}</div>
        </div>

        <!-- 审核时间 -->
        <div class="audit-item" v-if="infoData.auditTime">
          <div class="audit-label">审核时间</div>
          <div class="audit-value">{{ formatDateTime(infoData.auditTime) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { stationAuditAuditH5 } from "@/api/config";
import { FILE_BASE_URL } from "@/utils/globalConstants";

export default {
  name: "ProductionCycleChangeApproval",
  data() {
    return {
      form: {
        state: "",
        remarks: "",
      },
      isSubmitting: false,
      infoData: null,
      isReadOnly: false, // 是否为只读模式
    };
  },
  computed: {
    hasAttachments() {
      return this.infoData && this.infoData.attachment && this.infoData.attachment.length > 0;
    },
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      // 从路由参数中获取数据
      this.infoData = this.$route.params.data || {};
      console.log("接收到的数据：", this.infoData);

      // 判断是否为只读模式（已完成状态）
      this.isReadOnly = this.infoData.state === 1 || this.infoData.state === 2;

      // 处理附件数据
      if (this.infoData.attachment && typeof this.infoData.attachment === "string") {
        try {
          this.infoData.attachment = JSON.parse(this.infoData.attachment);
        } catch (e) {
          console.error("解析附件数据出错：", e);
          this.infoData.attachment = [];
        }
      }
    },

    getDisplayTitle() {
      return this.infoData?.constructionSite?.siteName || 
             this.infoData?.administrativeApply?.engineeringName || 
             '产生地名称';
    },

    getAddress() {
      return this.infoData?.constructionSite?.coordinateInfo || 
             this.infoData?.administrativeApply?.coordinateInfo || 
             '-';
    },

    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return '-';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    validateForm() {
      if (!this.form.state) {
        this.$toast.fail("请选择审核结果");
        return false;
      }
      if (!this.form.remarks) {
        this.$toast.fail("请输入审核备注");
        return false;
      }
      
      return true;
    },

    buildRequestData() {
      return {
        id: this.infoData.id,
        state: this.form.state,
        remarks: this.form.remarks,
      };
    },

    async submitAudit() {
      console.log("提交的数据：", this.form);
      
      if (!this.validateForm()) return;
      
      const reqData = this.buildRequestData();
      console.log("提交的请求数据：", reqData);
      
      this.isSubmitting = true;
      try {
        const res = await stationAuditAuditH5(reqData);
        if (res.data.success || res.data.state === "success") {
          this.$toast.success(res.data.message || "审核提交成功");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1500);
        } else {
          this.$toast.fail(res.data.message || "审核提交失败");
        }
      } catch (error) {
        console.error("审核提交失败：", error);
        this.$toast.fail("审核提交失败");
      } finally {
        this.isSubmitting = false;
      }
    },

    // 文件预览相关方法
    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      return `${FILE_BASE_URL}${filePath}`;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PowerPoint演示文稿';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);
      
      if (this.isImageFile(item.filePath)) {
        this.$imagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
  padding-bottom: 20px;
}

// 概览部分
.overview-section {
  padding: 16px;

  .overview-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .site-name {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        flex: 1;
        line-height: 24px;
      }

      .apply-time {
        font-size: 12px;
        color: #8c8c8c;
        white-space: nowrap;
        margin-left: 12px;
      }
    }

    .overview-content {
      .info-item {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: #8c8c8c;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          font-size: 14px;
          color: #262626;
          flex: 1;
          line-height: 20px;
        }
      }
    }
  }
}

// 通用标题样式
.section-title {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 0;

  span {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-left: 8px;
  }
}

// 附件部分
.attachments-section {
  padding: 0 16px 16px;

  // 所有附件 - 每行一个
  .all-attachments {
    .attachment-row {
      margin-bottom: 16px;
      cursor: pointer;

      // 图片行样式
      .image-row {
        img {
          width: 100%;
          max-height: 300px;
          object-fit: contain;
          border-radius: 8px;
          background: #f5f5f5;
          border: 1px solid #e6f4ff;
        }

        .attachment-title {
          font-size: 14px;
          color: #8c8c8c;
          text-align: center;
          margin-top: 8px;
          line-height: 20px;
        }
      }

      // 文件行样式
      .file-row {
        .file-info {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #ffffff;
          border: 1px solid #e6f4ff;
          border-radius: 8px;
          transition: all 0.3s;

          &:hover {
            border-color: #1989fa;
            box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
          }

          .file-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8faff;
            border-radius: 8px;
            margin-right: 12px;
          }

          .file-details {
            flex: 1;

            .file-name {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              margin-bottom: 4px;
              line-height: 20px;
            }

            .file-type {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 16px;
            }
          }

          .file-action {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 审核部分
.audit-section {
  .audit-card {
    background: #ffffff;
    border-radius: 12px;
    margin: 0 16px 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .audit-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .audit-label {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 12px;
      }
    }
  }
}

// 简化单选按钮样式
.simple-radio-group {
  .radio-option {
    margin-right: 24px;
    font-size: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 业务字段样式
.business-field {
  /deep/ .van-field__control {
    background: #f8faff;
    border: 1px solid #e6f4ff;
    border-radius: 8px;
    padding: 12px;
  }
}

// 备注字段样式
.remarks-field {
  /deep/ .van-field__control {
    background: #f8faff;
    border: 1px solid #e6f4ff;
    border-radius: 8px;
    padding: 12px;
    min-height: 80px;
  }
}

// 提交按钮
.submit-section {
  padding: 0 16px;

  .submit-btn {
    height: 48px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
  }
}

// 只读模式样式
.audit-result-section {
  padding: 0 16px 16px;

  .audit-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .audit-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .audit-label {
        font-size: 14px;
        color: #262626;
        font-weight: 500;
        margin-bottom: 12px;
        line-height: 20px;
      }

      .audit-value {
        font-size: 14px;
        color: #595959;
        line-height: 20px;
      }

      .status-tag {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;

        &.status-approved {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.status-rejected {
          background: #fff2f0;
          color: #ff4d4f;
          border: 1px solid #ffccc7;
        }
      }
    }
  }
}
</style>
