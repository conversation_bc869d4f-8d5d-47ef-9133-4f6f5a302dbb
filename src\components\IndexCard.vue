<template>
  <div class="card" v-if="!isAddPro">
    <van-swipe :autoplay="3000">
      <van-swipe-item v-for="(image, index) in images" :key="index">
        <img :src="image" />
      </van-swipe-item>
    </van-swipe>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      images: [require("../assets/images/cg/1.png")],
      SuccessData: {
        img: require("../assets/images/success.png"),
        title: "Dự án ứng dụng!!!",
        subtitle:
          "Thông tin của bạn đã được xem xét, hãy đi và nộp đơn cho dự án.",
        btnText: "Điền vào",
      },
      addrStatus_ErrorData: {
        stepPre: "Bước 3",
        img: require("../assets/images/error.png"),
        //地址未填写
        title: "Địa chỉ chưa điền!",
        //填写完个人地址之后才可以申请项目噢！！
        subtitle:
          "Vui lòng điền chính xác địa chỉ, nếu không bạn sẽ không thể nhận được gói thông tin thuốc thử.",
        btnText: "Điền vào",
        color: "f33060",
        route: "/address/list",
      },
      approvalStatus_ErrorData: {
        stepPre: "Bước 2",
        img: require("../assets/images/error.png"),
        //健康报告未填写
        title: "Báo cáo sức khỏe cá nhân (chưa hoàn thành)!",
        //请完成健康报告并且通过审核后申请项目！！！
        subtitle:
          "Vui lòng hoàn thành báo cáo sức khỏe cá nhân, áp dụng cho dự án sau khi vượt qua đánh giá.",
        btnText: "Điền vào",
        color: "f33060",
        route: "/question",
      },
      personStatus_ErrorData: {
        stepPre: "Bước 1",
        img: require("../assets/images/error.png"),
        //个人信息未填写
        title: "Thông tin cá nhân chưa được điền!",
        //请补充完整个人信息
        subtitle: "Vui lòng cung cấp đầy đủ thông tin cá nhân.",
        btnText: "Điền vào",
        color: "f33060",
        route: "/user",
      },
      accountStatus_ErrorData: {
        img: require("../assets/images/error.png"),
        //账户已被禁用
        title: "Tài khoản đã bị vô hiệu hóa",
        //请联系客服
        subtitle: "Vui lòng liên hệ với dịch vụ khách hàng",
        btnText: "Nộp đơn",
        color: "f33060",
        route: "/customer",
      },
      data: {},
    };
  },
  computed: {
    ...mapState("Permissions", ["noPermissionsName", "isAddPro"]),
  },
  components: {},
  created() {},
  mounted() {
    this.istext();
  },
  methods: {
    goVideo() {
      this.$go("/uservideo");
    },
    istext() {
      switch (this.noPermissionsName) {
        //账户状态
        case "accountStatus":
          this.data = {
            ...this.accountStatus_ErrorData,
          };
          return;
        //人员信息
        case "personStatus":
          this.data = {
            ...this.personStatus_ErrorData,
          };
          return;
        //健康报告
        case "approvalStatus":
          this.data = {
            ...this.approvalStatus_ErrorData,
          };
          return;
        //收货信息
        case "addrStatus":
          this.data = {
            ...this.addrStatus_ErrorData,
          };
          return;
      }
    },
  },
};
</script>

<style scoped lang="less">
.suc {
  color: #07c160;
}
.error {
  color: #f33060;
}
img {
  width: 100%;
  height: 110px;
}
.card {
  width: 100%;
  background-color: #fff;
  border-radius: 12px 12px 12px 12px;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.02),
    0px 16px 6px 0px rgba(0, 0, 0, 0.02), 0px 40px 20px 0px rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  overflow: hidden;

  .pre {
    width: 100%;
    justify-content: space-between;
    margin-left: 10px;
    text-align: left;
    .title {
      font-weight: 600;
      font-size: 17px;
      line-height: 22px;
    }
    .sub-title {
      font-weight: 400;
      font-size: 14px;
      color: #8e9aab;
      line-height: 24px;
      margin: 10px 0;
    }
  }
}
.box {
  width: 100%;
  height: 100%;
  align-items: center;
}
.btn-size {
  width: 40%;
  margin: 0 auto;
}
.left-v {
  font-size: 16px;
  align-items: center;
  margin-right: 15px;
  border-bottom: 1px solid red;
}
.step {
  font-size: 16px;
  font-weight: 600;
  color: black;
  align-items: center;
}
.bottom {
  width: 100%;
  justify-content: space-between;
}
</style>
