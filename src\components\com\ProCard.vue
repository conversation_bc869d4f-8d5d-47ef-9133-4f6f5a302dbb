<template>
  <div class="card">
    <div class="top flex-row">
      <div class="pre flex-colum">
        <div class="text">{{ data.projectName }}</div>
      </div>
    </div>
    <div class="flex-row timebox">
      <div class="flex-row chusheng">
        <div class="money" style="padding-left: 5px;">{{ $moneyGs(data.subsidyMoney) }} ₫</div>
        <!--<div class="flex-row time">
          <i>Thời Gian1: </i><i class="timedata">{{ time(data.regTime) }}</i>
        </div>-->
        <div class="time">
          <i>Tuổi2: </i><i class="timedata">{{ data.starAge }}-{{ data.endAge }} Tuổi</i>
        </div>
      </div>
      <div class="flex-row chusheng">
        <div class="time">
          <i>Nam: </i><i class="timedata">{{ data.manNum }} </i>&nbsp;/&nbsp;<i>Nữ:</i><i class="timedata">{{ data.womanNum }} </i>
        </div>
        <div class="time">
          <i class="timedata">{{ data.trialDays }}</i>&nbsp;+&nbsp;<i class="timedata">{{ data.observeDays }}</i>&nbsp;<i>Ngày</i>
        </div>
      </div>

      <div class="flex-row chusheng">
        <div class="time"><i>Thời Gian: </i><i class="timedata">{{ time(data.regTime) }}</i>
        </div>
      </div>


      <div class="flex-row chusheng">
        <div class="time"><i>Điều Kiện:</i>&nbsp;<i class="timedata">{{ needText() }}</i>
        </div>
      </div>
      <div class="chusheng2 flex-row">
        <div class="flex-row pre-c" @click="handleApplyPro">
          <p>Đăng Ký</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return { needTextValue: "" };
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState("Permissions", ["isAddPro", "noPermissionsName"]),
    ...mapState("user", ["userInfo"]),
    levelClass() {
      switch (this.data.proLevel) {
        case "A":
          return "level-a";
        case "B":
          return "level-b";
        case "C":
          return "level-c";
        case "D":
          return "level-d";
        case "E":
          return "level-e";
        default:
          return "default-level";
      }
    },
  },
  methods: {
    needText() {
      const a = "Bảng câu hỏi sức khỏe";
      const b = "Xét nghiệm nước tiểu";
      const c = "Điện tâm đồ";
      const d = "Xét nghiệm máu";
      let t = "";
      if (this.data.isJk === 1) {
        t = a;
      }
      if (this.data.isNj === 1) {
        t += t ? `, ${b}` : b;
      }
      if (this.data.isXdt === 1) {
        t += t ? `, ${c}` : c;
      }
      if (this.data.isXj === 1) {
        t += t ? `, ${d}` : d;
      }
      return t;
    },
    moneyGs(number) {
      return number.toLocaleString();
    },
    time(t) {
      const date = new Date(t);
      const year = date.getUTCFullYear().toString().slice(-0); // 获取年份的最后两位
      const month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
      const day = date.getUTCDate().toString().padStart(2, "0");
      return `${day}/${month}/${year}`;
    },
    handleApplyPro() {
      if (this.noPermissionsName !== "") {
        this.$toast({
          message: "Vui lòng điền đầy đủ thông tin",
          duration: 2000,
        });
        return;
      }
      var idData = {
        projectId: this.data.projectId,
      };
      var a = JSON.stringify(idData);
      this.$router.push({
        path: "/prodetils",
        query: { idData: a },
      });
    },
  },
};
</script>

<style scoped lang="less">
.gary {
  color: #909399 !important;
}
.card {
  width: 327px;
  background: #ecf2ff;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #f2f4f5;
  position: relative;
  .top {
    align-items: center;
    padding: 0 16px 0;
    box-sizing: border-box;
  }
  .xian {
    width: 327px;
    height: 1px;
    background: #f2f4f5;
    border-radius: 0px 0px 0px 0px;
  }
}
.proimg {
  width: 72px;
  height: 72px;
  border-radius: 12px 12px 12px 12px;
}
.timebox {
  padding: 3px 16px 5px;
  box-sizing: border-box;
  align-items: center;
  flex-wrap: wrap;
  .timeimg {
    width: 16px;
    height: 16px;
  }
  .time {
    margin: 5px;
    font-size: 14px;
    color: #6b7089;
    .timedata {
      color: #353c5c !important;
      font-weight: 600;
    }
  }
  .time2 {
    font-weight: 600;
    font-size: 18px;
    color:#be0000
  }
}
.chusheng {
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.pre {
  width: 100%;
  margin: 20px 10px 10px 5px;
  justify-content: space-around;
}

.pre div:nth-child(1) {
  width: 100%;
  font-weight: 600;
  font-size: 17px;
  color: #112950;
}

.pre div:nth-child(2) {
  font-weight: 400;
  font-size: 12px;
  color: #8e9aab;
  line-height: 18px;
}
.bottom {
  padding: 16px;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
}
.money {
  height: 18px;
  font-weight: 600;
  font-size: 16px;
  color: #be0000;
  line-height: 18px;
  border-radius: 12px 12px 12px 12px;
  text-align: center;
}
.pre-c {
  align-items: center;
  font-weight: 600;
  font-size: 12px;
  border-radius: 6px;
  background-color: rgb(0, 174, 255);
  color: white;
  padding: 5px 8px;
  line-height: 18px;
}
.chusheng2 {
  width: 100%;
  margin: 0 0 5px 0;
  justify-content: flex-end;
}
.state-box {
  position: absolute;
  right: 30px;
  border-radius: 10px;
  top: -10px;
  padding: 2px 12px;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  color: #ffffff;
  line-height: 18px;
}
.level-a {
  background-color: #ff0000; // red
}
.level-b {
  background-color: #ffa500; // orange
}
.level-c {
  background-color: #bdbd17; // yellow
}
.level-d {
  background-color: #008000; // green
}
.level-e {
  background-color: #0000ff; // blue
}
.default-level {
  background-color: #909399; // grey
}
</style>
