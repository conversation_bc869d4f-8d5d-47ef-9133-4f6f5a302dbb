<template>
  <div class="home">
    <NavHeader ref="navHeader" title="运输车辆变更审核" :back="true"></NavHeader>

    <!-- 标签页 -->
    <van-tabs v-model="active" @change="onTabChange" sticky>
      <van-tab
        v-for="(tab, index) in tabOptions"
        :key="index"
        :title="tab.text"
      >
      </van-tab>
    </van-tabs>

    <div class="information cell">
      <div class="indent-box">
        <ChangeIndentCard
          class="card"
          :indentData="item"
          :currentTabState="tabOptions[active].value"
          v-for="(item, index) in dataList"
          :key="index + 'c'"
          @getList="getList"
        ></ChangeIndentCard>
      </div>
      <div v-if="dataList.length === 0" class="center">
        <NullCop msg="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import { stationAuditFindListH5 } from "@/api/config";
import ChangeIndentCard from "@/views/ProductionAuditModel/ChangeIndentCard.vue";
import NullCop from "@/components/com/NullCop.vue";

export default {
  name: "TransportVehicleList",
  data() {
    return {
      title: "",
      total: "",
      dataList: [],
      active: 0, // 默认选中第一个标签
      loading: false,
      finished: false,
      tabOptions: [
        { text: "待审核", value: 1 }, // 待审核
        { text: "已完成", value: 2 }, // 已完成
      ],
      navHeight: 0,
      formData: {
        pageNum: 1,
        pageSize: 10,
        type: 3, // 运输业务
        changeType: 9, // 运输车辆变更审核
        typeState: 1, // 默认为待审核
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.navHeight = this.$refs.navHeader.$el.offsetHeight - 1;
      console.log("NavHeader 高度:", this.navHeight);
    });
    this.getList();
    this.debouncedHandleScroll = this.$debounce(this.handleScroll, 200); // 200ms 防抖
    window.addEventListener("scroll", this.debouncedHandleScroll);
  },
  components: {
    ChangeIndentCard,
    NullCop,
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.debouncedHandleScroll);
  },
  methods: {
    // 标签页切换
    onTabChange(index) {
      this.active = index;
      this.formData.typeState = this.tabOptions[index].value;
      this.formData.pageNum = 1;
      this.finished = false;
      this.getList();
    },

    handleScroll() {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
        this.formData.pageSize += 20;
        this.loading = false;
        this.getList();
      }
    },

    handleSearch() {
      this.formData.pageNum = 1;
      this.finished = false;
      this.loading = false;
      this.getList();
    },

    async getList() {
      try {
        console.log("请求参数:", this.formData);
        const res = await stationAuditFindListH5(this.formData);
        console.log("接口返回:", res);

        if (res.data.success || res.data.state === "success") {
          if (this.formData.pageNum === 1) {
            this.dataList = res.data.data || [];
          } else {
            this.dataList = [...this.dataList, ...(res.data.data || [])];
          }
          
          this.total = res.data.total || 0;
          this.loading = false;
          this.finished = (res.data.data || []).length === 0;
        } else {
          this.$toast.fail(res.data.message || "获取数据失败");
          this.loading = false;
          this.finished = true;
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$toast.fail("获取数据失败");
        this.loading = false;
        this.finished = true;
      }
    },
  },
};
</script>

<style scoped lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background: #f8faff;
}

.information {
  padding: 0 16px;
  
  .indent-box {
    padding-top: 16px;
    
    .card {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
}

/deep/ .van-tabs__wrap {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .van-tab {
  font-size: 16px;
  font-weight: 500;
}

/deep/ .van-tab--active {
  color: #1989fa;
}

/deep/ .van-tabs__line {
  background: #1989fa;
}
</style>
