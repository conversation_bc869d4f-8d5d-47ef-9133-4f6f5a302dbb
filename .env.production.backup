NODE_ENV = 'production'
VUE_APP_PUBLIC_PATH = './'

# Test Environment - Skip Login
VUE_APP_SKIP_LOGIN = true

# Use relative API URL for Nginx proxy (recommended)
VUE_APP_API_URL = /api/

# Direct API URL (if no proxy)
# VUE_APP_API_URL = http://**************:47025/tpss

# Direct API URLs (backup options)
# VUE_APP_API_URL = https://jzljgl.xtzhcg.com:47015/tpss
# VUE_APP_API_URL = http://**************:5511/tpss
# VUE_APP_API_URL = http://**************:47025/tpss
# VUE_APP_API_URL = http://************:9091/care-cms
#Test Environment Build
