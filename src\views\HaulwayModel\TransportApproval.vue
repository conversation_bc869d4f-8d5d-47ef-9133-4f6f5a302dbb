<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="isViewMode ? '运输核准审核详情' : '运输核准审核'" :back="true"></NavHeader>
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.agentName"
              name="agentName"
              label="运输企业名称："
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1"
              name="contract1"
              label="联系人："
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1Tel"
              name="contract1Tel"
              label="联系电话："
              :readonly="true"
            />
            <van-field
              :value="getApplyType(infoData)"
              name="applyType"
              label="申请类型："
              :readonly="true"
            />
            <van-field
              :value="formatDateTime(infoData?.createDate)"
              name="createDate"
              label="申请时间："
              :readonly="true"
            />
            <van-field
              :value="infoData?.unitNature"
              name="unitNature"
              label="企业性质："
              :readonly="true"
            />
            <van-field
              :value="infoData?.businessNature"
              name="businessNature"
              label="经营性质："
              :readonly="true"
            />
            <van-field
              :value="infoData?.ucsCode"
              name="ucsCode"
              label="统一信用代码："
              :readonly="true"
            />
            <van-field
              :value="infoData?.carCount"
              name="carCount"
              label="车辆数量："
              :readonly="true"
            />
            <van-field
              :value="infoData?.coordinateInfo"
              name="coordinateInfo"
              label="企业地址："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <LabelHeader left="申请材料"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <!-- 所有文件 - 每行一个 -->
            <div class="all-attachments">
              <div
                v-for="(item, index) in infoData.attachments"
                :key="index"
                class="attachment-row"
                @click="previewFile(item)"
              >
                <!-- 图片文件显示 -->
                <div v-if="isImageFile(item.filePath)" class="image-row">
                  <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                  <div class="attachment-title">{{ item.displayTitle }}</div>
                </div>

                <!-- 非图片文件显示 -->
                <div v-else class="file-row">
                  <div class="file-info">
                    <div class="file-icon">
                      <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                    </div>
                    <div class="file-details">
                      <div class="file-name">{{ item.displayTitle }}</div>
                      <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                    </div>
                    <div class="file-action">
                      <van-icon name="eye-o" size="20px" color="#1989fa" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LabelHeader :left="isViewMode ? '审核结果' : '审核情况'"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 查看模式：显示审核结果 -->
            <template v-if="isViewMode">
              <van-field name="approvalState" label="审核结果：" label-width="120px">
                <template #input>
                  <van-radio-group
                    :value="infoData.approvalState"
                    direction="horizontal"
                    disabled
                  >
                    <van-radio :name="8">通过</van-radio>
                    <van-radio :name="5">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                :value="getAuditRemarks(infoData)"
                name="auditRemarks"
                label="审核备注："
                label-width="120px"
                type="textarea"
                rows="3"
                :readonly="true"
              />
              <van-field
                :value="infoData.examineDate || ''"
                name="auditDate"
                label="审核时间："
                label-width="120px"
                :readonly="true"
              />
            </template>

            <!-- 编辑模式：审核操作 -->
            <template v-else>
              <van-field name="applyState" label="审核：" label-width="120px">
                <template #input>
                  <van-radio-group
                    v-model="form.applyState"
                    direction="horizontal"
                  >
                    <van-radio :name="8">通过</van-radio>
                    <van-radio :name="5">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                v-model="form.ruralDevelopmentRemarks"
                name="ruralDevelopmentRemarks"
                label-width="120px"
                rows="3"
                autosize
                label="审核备注"
                type="textarea"
                maxlength="100"
                placeholder="请输入备注"
                show-word-limit
              />
            </template>
          </van-form>
          <div class="btn-box" v-if="!isViewMode">
            <yButton :top150="true" title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 提交中的动画弹窗 -->
    <van-overlay :show="isSubmitting" class="submit-overlay">
      <div class="submit-loading-container">
        <div class="submit-loading-content">
          <van-loading size="40px" color="#1989fa" vertical>
            正在提交初审结果...
          </van-loading>

          <div class="submit-progress">
            <div class="progress-text">
              <span>已用时：{{ submitElapsedSeconds }}秒</span>
            </div>
            <div class="progress-tip">
              服务器正在处理您的请求，请耐心等待
            </div>
          </div>

          <div class="submit-actions">
            <van-button
              type="default"
              size="small"
              @click.stop="handleBackDuringSubmit"
              class="back-button"
            >
              返回
            </van-button>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { agentSaveOrUpdate } from "@/api/config";
import { FILE_BASE_URL } from "@/utils/globalConstants";
import NavHeader from "@/components/com/NavHeader.vue";
import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  components: {
    NavHeader,
    LabelHeader,
  },
  data() {
    return {
      form: {
        applyState: 8, // 默认选择通过
        ruralDevelopmentRemarks: "",
      },
      rules: {
        applyState: [{ required: true, message: "请选择审核结果" }],
        ruralDevelopmentRemarks: [{ required: true, message: "请输入审核备注" }],
      },
      infoData: {},
      isSubmitting: false,
      submitStartTime: null,
      submitTimer: null,
      submitElapsedSeconds: 0,
      FILE_BASE_URL,
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    isViewMode() {
      // 如果是从已完成tab进入，或者状态已经是完成状态，则为查看模式
      const fromCompletedTab = this.$route.query.from === 'completed';
      const isCompletedStatus = this.infoData && (this.infoData.approvalState === 4 || this.infoData.approvalState === 5 || this.infoData.approvalState === 8 || this.infoData.approvalState === 10);
      return fromCompletedTab || isCompletedStatus;
    },
    isShowData() {
      return Object.keys(this.infoData).length > 0;
    },
    hasAttachments() {
      return this.infoData && this.infoData.attachments && this.infoData.attachments.length > 0;
    },
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    this.stopSubmitAnimation();
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return '-';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    getApplyType(data) {
      // 获取申请类型
      if (data?.dictionary2?.dicValue) {
        return data.dictionary2.dicValue;
      }
      return '运输核准申请';
    },

    // 文件处理相关方法
    isImageFile(filePath) {
      if (!filePath) return false;
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileUrl(filePath) {
      return `${this.FILE_BASE_URL}${filePath}`;
    },

    getFileIcon(filePath) {
      if (!filePath) return 'description';
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));

      switch (extension) {
        case '.pdf':
          return 'description';
        case '.doc':
        case '.docx':
          return 'description';
        case '.xls':
        case '.xlsx':
          return 'description';
        default:
          return 'description';
      }
    },

    getFileIconColor(filePath) {
      if (!filePath) return '#969799';
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));

      switch (extension) {
        case '.pdf':
          return '#ff4d4f';
        case '.doc':
        case '.docx':
          return '#1890ff';
        case '.xls':
        case '.xlsx':
          return '#52c41a';
        case '.ppt':
        case '.pptx':
          return '#fa8c16';
        default:
          return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      if (!filePath) return '文件';
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));

      switch (extension) {
        case '.pdf':
          return 'PDF文档';
        case '.doc':
        case '.docx':
          return 'Word文档';
        case '.xls':
        case '.xlsx':
          return 'Excel表格';
        case '.ppt':
        case '.pptx':
          return 'PPT演示';
        case '.jpg':
        case '.jpeg':
          return 'JPEG图片';
        case '.png':
          return 'PNG图片';
        case '.gif':
          return 'GIF图片';
        default:
          return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        this.$imagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 初始化数据
    initData() {
      const dataParam = this.$route.params.data;

      try {
        if (typeof dataParam === 'string') {
          this.infoData = JSON.parse(decodeURIComponent(dataParam));
        } else {
          this.infoData = dataParam;
        }

        console.log("获取到的运输核准申请数据：", this.infoData);
      } catch (error) {
        console.error("解析申请数据失败：", error);
        this.$toast.fail("数据格式错误");
        this.$router.go(-1);
      }
    },

    getAuditRemarks(data) {
      return data.ruralDevelopmentRemarks || data.auditRemarks || '-';
    },

    startSubmitAnimation() {
      this.isSubmitting = true;
      this.submitStartTime = Date.now();
      this.submitElapsedSeconds = 0;

      this.submitTimer = setInterval(() => {
        if (this.submitStartTime) {
          this.submitElapsedSeconds = Math.floor((Date.now() - this.submitStartTime) / 1000);
        }
      }, 1000);
    },

    stopSubmitAnimation() {
      this.isSubmitting = false;
      this.submitStartTime = null;
      this.submitElapsedSeconds = 0;

      if (this.submitTimer) {
        clearInterval(this.submitTimer);
        this.submitTimer = null;
      }
    },

    handleBackDuringSubmit() {
      if (confirm('数据还在提交中，确定要返回吗？返回后提交将继续进行。')) {
        this.$router.go(-1);
      }
    },

    sumBit() {
      this.$refs.form.validate().then(() => {
        this.startSubmitAnimation();

        // 获取当前日期，格式为 YYYY-MM-DD
        const today = new Date();
        const examineDate = today.getFullYear() + '-' +
          String(today.getMonth() + 1).padStart(2, '0') + '-' +
          String(today.getDate()).padStart(2, '0');

        const reqData = {
          id: this.infoData.id,
          ruralDevelopmentRemarks: this.form.ruralDevelopmentRemarks,
          approvalState: this.form.applyState, // 8-审核通过待上传，5-审核不通过
          examineDate: examineDate
        };

        console.log("运输核准初审提交的请求数据：", reqData);

        agentSaveOrUpdate(reqData).then((res) => {
          this.stopSubmitAnimation();
          if (res.data.success) {
            this.$toast.success("初审提交成功");
            this.$router.go(-1);
          } else {
            this.$toast.fail(res.data.message || "初审提交失败");
          }
        }).catch((error) => {
          this.stopSubmitAnimation();
          console.error("初审提交失败：", error);
          this.$toast.fail("初审提交失败");
        });
      }).catch(() => {
        this.$toast.fail("请完善审核信息");
      });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 10px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}

.y-card-box {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  overflow: hidden;
}

.y-card {
  padding: 16px;
}

.uploadImgsize {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #f0f2f5;
}

.btn-box {
  margin-top: 24px;
  padding: 0 16px;
}

// 附件部分
.attachments-section {
  .all-attachments {
    .attachment-row {
      margin-bottom: 16px;
      cursor: pointer;

      // 图片行样式
      .image-row {
        img {
          width: 100%;
          max-height: 300px;
          object-fit: contain;
          border-radius: 8px;
          background: #f5f5f5;
          border: 1px solid #e6f4ff;
        }

        .attachment-title {
          font-size: 14px;
          color: #8c8c8c;
          text-align: center;
          margin-top: 8px;
          line-height: 20px;
        }
      }

      // 文件行样式
      .file-row {
        .file-info {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #ffffff;
          border: 1px solid #e6f4ff;
          border-radius: 8px;
          transition: all 0.3s;

          &:hover {
            border-color: #1989fa;
            box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
          }

          .file-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8faff;
            border-radius: 8px;
            margin-right: 12px;
          }

          .file-details {
            flex: 1;

            .file-name {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              margin-bottom: 4px;
              line-height: 20px;
            }

            .file-type {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 16px;
            }
          }

          .file-action {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 提交动画样式
.submit-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
}

.submit-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.submit-loading-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  width: 90%;
}

.submit-progress {
  margin-top: 20px;
}

.progress-text {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.progress-tip {
  font-size: 12px;
  color: #999999;
  line-height: 1.4;
}

.submit-actions {
  margin-top: 20px;
}

.back-button {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666666;
}

// 表单样式优化
/deep/ .van-field__label {
  color: #262626;
  font-weight: 500;
}

/deep/ .van-field__control {
  color: #595959;
}

/deep/ .van-radio__label {
  color: #262626;
  font-size: 14px;
  margin-left: 8px;
}

/deep/ .van-radio--checked .van-radio__label {
  color: #1989fa;
}

/deep/ .van-radio {
  margin-right: 24px;
  margin-bottom: 8px;
}

/deep/ .van-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .van-radio__icon {
  font-size: 16px;
}

/deep/ .van-radio__icon--checked {
  color: #1989fa;
}

.van-radio {
  margin: 5px 3px;
}

.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}
</style>