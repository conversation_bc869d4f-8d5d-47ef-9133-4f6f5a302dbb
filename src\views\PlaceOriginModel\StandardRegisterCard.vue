<template>
  <div class="card">
    <!-- 主要信息区域 -->
    <div class="card-content">
      <!-- 单位名称 - 主要信息 -->
      <div class="info-row main-info">
        <div class="info-item full-width">
          <van-icon name="shop-o" size="16px" color="#1989fa" />
          <span class="label">单位名称：</span>
          <span class="value main-value">{{ indentData?.agentName || '-' }}</span>
        </div>
      </div>

      <!-- 登录账号 -->
      <div class="info-row">
        <div class="info-item full-width">
          <van-icon name="manager-o" size="14px" color="#52c41a" />
          <span class="label">登录账号：</span>
          <span class="value">{{ indentData?.loginId || '-' }}</span>
        </div>
      </div>

      <!-- 注册时间 -->
      <div class="info-row">
        <div class="info-item full-width">
          <van-icon name="calendar-o" size="14px" color="#722ed1" />
          <span class="label">注册时间：</span>
          <span class="value">{{ formatDate(indentData?.createDate) }}</span>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="card-footer">
      <div class="action-btn-bottom" @click="handleClick(indentData)">
        <span>{{ getActionText() }}</span>
        <van-icon name="arrow" color="#1989fa" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "StandardRegisterCard",
  props: {
    indentData: {
      type: Object,
      default: () => ({}),
    },
    currentTabState: {
      type: Number,
      default: 1,
    },
  },
  methods: {
    handleClick(data) {
      this.$router.push({
        name: "PlaceOriginRegisterApproval",
        params: { data },
      });
    },

    getActionText() {
      return this.currentTabState === 1 ? "去审核" : "查看详情";
    },



    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
  },
};
</script>

<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

// 卡片内容区域
.card-content {
  padding: 16px;
  padding-bottom: 8px;
}

// 卡片底部
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
  padding-top: 12px;
}

// 底部操作按钮
.action-btn-bottom {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: all 0.2s ease;

  span {
    font-size: 14px;
    color: #1989fa;
    margin-right: 4px;
    font-weight: 500;
  }

  &:hover {
    background: rgba(25, 137, 250, 0.05);
  }

  &:active {
    transform: scale(0.98);
  }
}

// 信息行
.info-row {
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &.main-info {
    margin-bottom: 14px;

    .main-value {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }
}

// 信息项
.info-item {
  display: flex;
  align-items: center;

  &.full-width {
    width: 100%;
  }

  .van-icon {
    margin-right: 6px;
    flex-shrink: 0;
  }

  .label {
    font-size: 13px;
    color: #666;
    margin-right: 4px;
    flex-shrink: 0;
    min-width: fit-content;
  }

  .value {
    font-size: 13px;
    color: #333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.main-value {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }
}
</style>
