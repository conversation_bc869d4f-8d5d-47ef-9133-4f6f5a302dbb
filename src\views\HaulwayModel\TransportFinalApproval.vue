<template>
  <div class="container">
    <NavHeader ref="navHeader" :title="isViewMode ? '运输核准终审详情' : '运输核准终审'" :back="true"></NavHeader>

    <!-- 调试信息 -->
    <div v-if="!infoData || Object.keys(infoData).length === 0" style="padding: 20px; text-align: center; color: #999;">
      正在加载数据...
    </div>

    <template v-else>
      <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="infoData?.agentName"
              name="agentName"
              label="运输企业名称："
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1"
              name="contract1"
              label="联系人："
              :readonly="true"
            />
            <van-field
              :value="infoData?.contract1Tel"
              name="contract1Tel"
              label="联系电话："
              :readonly="true"
            />
            <van-field
              :value="getApplyType(infoData)"
              name="applyType"
              label="申请类型："
              :readonly="true"
            />
            <van-field
              :value="formatDateTime(infoData?.createDate)"
              name="createDate"
              label="申请时间："
              :readonly="true"
            />
            <van-field
              :value="infoData?.unitNature"
              name="unitNature"
              label="企业性质："
              :readonly="true"
            />
            <van-field
              :value="infoData?.businessNature"
              name="businessNature"
              label="经营性质："
              :readonly="true"
            />
            <van-field
              :value="infoData?.ucsCode"
              name="ucsCode"
              label="统一信用代码："
              :readonly="true"
            />
            <van-field
              :value="infoData?.carCount"
              name="carCount"
              label="车辆数量："
              :readonly="true"
            />
            <van-field
              :value="infoData?.coordinateInfo"
              name="coordinateInfo"
              label="企业地址："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料 -->
    <div class="attachments-section" v-if="hasAttachments">
      <LabelHeader left="申请材料"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <div class="all-attachments">
              <div
                v-for="(item, index) in infoData.attachments"
                :key="index"
                class="attachment-row"
                @click="previewFile(item)"
              >
                <!-- 图片文件显示 -->
                <div v-if="isImageFile(item.filePath)" class="image-row">
                  <img :src="getFileUrl(item.filePath)" :alt="item.displayTitle" />
                  <div class="attachment-title">{{ item.displayTitle }}</div>
                </div>

                <!-- 非图片文件显示 -->
                <div v-else class="file-row">
                  <div class="file-info">
                    <div class="file-icon">
                      <van-icon :name="getFileIcon(item.filePath)" size="32px" :color="getFileIconColor(item.filePath)" />
                    </div>
                    <div class="file-details">
                      <div class="file-name">{{ item.displayTitle }}</div>
                      <div class="file-type">{{ getFileTypeText(item.filePath) }}</div>
                    </div>
                    <div class="file-action">
                      <van-icon name="eye-o" size="20px" color="#1989fa" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LabelHeader left="初审意见"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <van-field
              :value="getInitialAuditResult()"
              name="initialResult"
              label="初审结果："
              :readonly="true"
            />
            <van-field
              :value="getInitialAuditRemarks()"
              name="initialRemarks"
              label="初审备注："
              :readonly="true"
              type="textarea"
              rows="2"
            />
            <van-field
              :value="infoData?.examineDate || ''"
              name="initialDate"
              label="初审时间："
              :readonly="true"
            />
          </van-form>
        </div>
      </div>
    </div>

    <!-- 核准证 -->
    <div class="approval-cert-section" v-if="hasApprovalCert">
      <LabelHeader left="核准证"></LabelHeader>
      <div class="information">
        <div class="y-card-box">
          <div class="y-card">
            <div class="cert-files-display">
              <div
                v-for="(file, index) in approvalCertFiles"
                :key="index"
                class="cert-file-item"
                @click="previewFile(file)"
              >
                <img
                  :src="getFileUrl(file.filePath)"
                  :alt="file.displayTitle"
                  class="cert-image"
                />
                <div class="cert-title">{{ file.displayTitle }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <LabelHeader :left="isViewMode ? '终审结果' : '终审情况'"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 查看模式：显示终审结果 -->
            <template v-if="isViewMode">
              <van-field name="applyState" label="终审结果：" label-width="120px">
                <template #input>
                  <van-radio-group
                    :value="infoData.approvalState"
                    direction="horizontal"
                    disabled
                  >
                    <van-radio :name="4">通过</van-radio>
                    <van-radio :name="10" style="margin-left: 20px">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                :value="getAuditRemarks(infoData)"
                name="finalAuditRemarks"
                label="终审备注："
                label-width="120px"
                type="textarea"
                rows="3"
                :readonly="true"
              />
              <van-field
                :value="infoData.finalApprovalDate || ''"
                name="finalAuditDate"
                label="终审时间："
                label-width="120px"
                :readonly="true"
              />

              <!-- 终审新增字段显示 -->
              <van-field
                :value="infoData.certNum || ''"
                name="certNum"
                label="证件编号："
                label-width="120px"
                :readonly="true"
              />

              <van-field
                :value="getValidityPeriodText()"
                name="validityPeriod"
                label="有效期："
                label-width="120px"
                :readonly="true"
              />


            </template>

            <!-- 编辑模式：终审操作 -->
            <template v-else>
              <van-field name="applyState" label="终审：" label-width="120px">
                <template #input>
                  <van-radio-group
                    v-model="form.applyState"
                    direction="horizontal"
                  >
                    <van-radio :name="4">通过</van-radio>
                    <van-radio :name="10" style="margin-left: 20px">不通过</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                v-model="form.ruralDevelopmentRemarks"
                name="ruralDevelopmentRemarks"
                label-width="120px"
                rows="3"
                autosize
                label="终审备注"
                type="textarea"
                maxlength="100"
                placeholder="请输入备注"
                show-word-limit
              />

              <!-- 证件编号 -->
              <van-field
                v-model="form.certNum"
                name="certNum"
                label="证件编号"
                label-width="120px"
                placeholder="请输入证件编号"
              />

              <!-- 有效期 -->
              <van-field
                v-model="form.beginValidityTime"
                name="beginValidityTime"
                label="有效期"
                label-width="120px"
                placeholder="2025-08-01"
                readonly
                @click="showStartDatePicker = true"
              />

              <van-field
                v-model="form.endValidityTime"
                name="endValidityTime"
                label="至"
                label-width="120px"
                placeholder="2025-08-28"
                readonly
                @click="showEndDatePicker = true"
              />


            </template>
          </van-form>

          <!-- 日期选择器 -->
          <van-popup v-model="showStartDatePicker" position="bottom">
            <van-datetime-picker
              v-model="currentStartDate"
              type="date"
              title="选择开始日期"
              @confirm="onStartDateConfirm"
              @cancel="showStartDatePicker = false"
            />
          </van-popup>

          <van-popup v-model="showEndDatePicker" position="bottom">
            <van-datetime-picker
              v-model="currentEndDate"
              type="date"
              title="选择结束日期"
              @confirm="onEndDateConfirm"
              @cancel="showEndDatePicker = false"
            />
          </van-popup>

          <div class="btn-box" v-if="!isViewMode">
            <yButton :top150="true" title="提交" @click="sumBit()"></yButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 提交中的动画弹窗 -->
    <van-overlay :show="isSubmitting" class="submit-overlay">
      <div class="submit-loading-container">
        <div class="submit-loading-content">
          <van-loading size="40px" color="#1989fa" vertical>
            正在提交终审结果...
          </van-loading>

          <div class="submit-progress">
            <div class="progress-text">
              <span>已用时：{{ submitElapsedSeconds }}秒</span>
            </div>
            <div class="progress-tip">
              服务器正在处理您的请求，请耐心等待
            </div>
          </div>

          <div class="submit-actions">
            <van-button
              type="default"
              size="small"
              @click.stop="handleBackDuringSubmit"
              class="back-button"
            >
              返回
            </van-button>
          </div>
        </div>
      </div>
    </van-overlay>
    </template>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { agentFinalApproval } from "@/api/config";
import { FILE_BASE_URL, IMG_BASE_URL } from "@/utils/globalConstants";
import NavHeader from "@/components/com/NavHeader.vue";
import LabelHeader from "@/components/com/LabelHeader.vue";

export default {
  components: {
    NavHeader,
    LabelHeader,
  },
  data() {
    return {
      form: {
        applyState: "",
        ruralDevelopmentRemarks: "",
        certNum: "", // 证件编号
        beginValidityTime: "", // 有效期开始
        endValidityTime: "", // 有效期结束
      },
      rules: {
        applyState: [{ required: true, message: "请选择终审结果" }],
        ruralDevelopmentRemarks: [{ required: true, message: "请输入终审备注" }],
        certNum: [{ required: true, message: "请输入证件编号" }],
        beginValidityTime: [{ required: true, message: "请选择有效期开始时间" }],
        endValidityTime: [{ required: true, message: "请选择有效期结束时间" }],
      },
      infoData: {},
      showStartDatePicker: false,
      showEndDatePicker: false,
      currentStartDate: new Date(),
      currentEndDate: new Date(),
      isSubmitting: false,
      submitStartTime: null,
      submitTimer: null,
      submitElapsedSeconds: 0,
      FILE_BASE_URL,
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    isViewMode() {
      // 如果是从已完成tab进入，或者状态已经是完成状态，则为查看模式
      const fromCompletedTab = this.$route.query.from === 'completed';
      const isCompletedStatus = this.infoData && (this.infoData.approvalState === 4 || this.infoData.approvalState === 10);
      return fromCompletedTab || isCompletedStatus;
    },
    isShowData() {
      return this.infoData && this.infoData.attachments && this.infoData.attachments.length > 0;
    },
    hasAttachments() {
      return this.infoData && this.infoData.attachments && this.infoData.attachments.length > 0;
    },
    // 检查是否有核准证文件
    hasApprovalCert() {
      return this.approvalCertFiles && this.approvalCertFiles.length > 0;
    },

    // 获取核准证文件列表
    approvalCertFiles() {
      if (!this.infoData || !this.infoData.approvalCertFileUrl) {
        return [];
      }

      try {
        const certData = JSON.parse(this.infoData.approvalCertFileUrl);
        return Array.isArray(certData) ? certData : [];
      } catch (error) {
        console.error('解析核准证文件数据失败:', error);
        return [];
      }
    },
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    this.stopSubmitAnimation();
  },
  methods: {
    formatDateTime(isoString) {
      if (!isoString) return '-';
      try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return '-';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '-';
      }
    },

    getApplyType(data) {
      // 获取申请类型
      if (data?.dictionary2?.dicValue) {
        return data.dictionary2.dicValue;
      }
      return '运输核准申请';
    },

    // 获取初审结果
    getInitialAuditResult() {
      if (!this.infoData) return '-';
      switch (this.infoData.approvalState) {
        case 8:
          return '初审通过';
        case 5:
          return '初审不通过';
        default:
          return '已初审';
      }
    },

    // 获取初审备注
    getInitialAuditRemarks() {
      return this.infoData?.ruralDevelopmentRemarks || this.infoData?.auditRemarks || '无';
    },

    // 文件预览相关方法
    getFileUrl(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) {
        return filePath;
      }
      return `${IMG_BASE_URL}${filePath}`;
    },

    isImageFile(filePath) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      return imageExtensions.includes(extension);
    },

    getFileIcon(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'description';
        case '.doc':
        case '.docx': return 'description';
        case '.xls':
        case '.xlsx': return 'description';
        case '.ppt':
        case '.pptx': return 'description';
        default: return 'description';
      }
    },

    getFileIconColor(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return '#ff4d4f';
        case '.doc':
        case '.docx': return '#1890ff';
        case '.xls':
        case '.xlsx': return '#52c41a';
        case '.ppt':
        case '.pptx': return '#fa8c16';
        default: return '#8c8c8c';
      }
    },

    getFileTypeText(filePath) {
      const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
      switch (extension) {
        case '.pdf': return 'PDF文档';
        case '.doc':
        case '.docx': return 'Word文档';
        case '.xls':
        case '.xlsx': return 'Excel表格';
        case '.ppt':
        case '.pptx': return 'PPT演示';
        case '.jpg':
        case '.jpeg': return 'JPEG图片';
        case '.png': return 'PNG图片';
        case '.gif': return 'GIF图片';
        default: return '文档';
      }
    },

    previewFile(item) {
      const fileUrl = this.getFileUrl(item.filePath);

      if (this.isImageFile(item.filePath)) {
        this.$imagePreview([fileUrl]);
      } else {
        const extension = item.filePath.toLowerCase().substring(item.filePath.lastIndexOf('.'));
        if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(extension)) {
          this.openFilePreview(fileUrl, extension);
        } else {
          this.downloadFile(fileUrl, item.displayTitle);
        }
      }
    },

    openFilePreview(fileUrl, extension) {
      if (extension === '.pdf') {
        window.open(fileUrl, '_blank');
      } else {
        const previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
        window.open(previewUrl, '_blank');
      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 初始化数据
    initData() {
      const dataParam = this.$route.params.data;

      if (!dataParam) {
        console.error('缺少数据参数');
        this.$toast.fail('缺少必要参数');
        this.$router.go(-1);
        return;
      }

      try {
        if (typeof dataParam === 'string') {
          this.infoData = JSON.parse(decodeURIComponent(dataParam));
        } else {
          this.infoData = dataParam;
        }

        console.log("获取到的运输核准申请数据：", this.infoData);
      } catch (error) {
        console.error("解析申请数据失败：", error);
        this.$toast.fail("数据格式错误");
        this.$router.go(-1);
      }
    },

    getAuditRemarks(data) {
      return data.finalApprovalRemark || data.ruralDevelopmentRemarks || data.auditRemarks || '-';
    },

    // 预览核准证
    previewCertificate(cert) {
      if (!cert.filePath) return;

      const fileUrl = cert.filePath.startsWith('http')
        ? cert.filePath
        : `${IMG_BASE_URL}${cert.filePath}`;

      // 在新窗口打开图片
      window.open(fileUrl, '_blank');
    },

    getValidityPeriodText() {
      if (!this.infoData) return '无';
      const start = this.infoData.beginValidityTime;
      const end = this.infoData.endValidityTime;
      if (start && end) {
        return `${start} 至 ${end}`;
      } else if (start) {
        return `${start} 起`;
      } else if (end) {
        return `至 ${end}`;
      }
      return '无';
    },

    previewImage(url) {
      this.$imagePreview([url]);
    },

    onStartDateConfirm(date) {
      this.form.beginValidityTime = this.formatDate(date);
      this.showStartDatePicker = false;
    },

    onEndDateConfirm(date) {
      this.form.endValidityTime = this.formatDate(date);
      this.showEndDatePicker = false;
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },



    startSubmitAnimation() {
      this.isSubmitting = true;
      this.submitStartTime = Date.now();
      this.submitElapsedSeconds = 0;

      this.submitTimer = setInterval(() => {
        if (this.submitStartTime) {
          this.submitElapsedSeconds = Math.floor((Date.now() - this.submitStartTime) / 1000);
        }
      }, 1000);
    },

    stopSubmitAnimation() {
      this.isSubmitting = false;
      this.submitStartTime = null;
      this.submitElapsedSeconds = 0;

      if (this.submitTimer) {
        clearInterval(this.submitTimer);
        this.submitTimer = null;
      }
    },

    handleBackDuringSubmit() {
      if (confirm('数据还在提交中，确定要返回吗？返回后提交将继续进行。')) {
        this.$router.go(-1);
      }
    },

    sumBit() {
      this.$refs.form.validate().then(() => {
        // 开始提交动画
        this.startSubmitAnimation();

        const reqData = {
          id: this.infoData.id,
          approvalState: this.form.applyState, // 4-生效 10-终审驳回
          certNum: this.form.certNum,
          beginValidityTime: this.form.beginValidityTime,
          endValidityTime: this.form.endValidityTime,
          finalApprovalRemark: this.form.ruralDevelopmentRemarks
        };

        console.log("运输核准终审提交的请求数据：", reqData);

        agentFinalApproval(reqData).then((res) => {
          this.stopSubmitAnimation();
          if (res.data.success) {
            this.$toast.success("终审提交成功");
            this.$router.go(-1);
          } else {
            this.$toast.fail(res.data.message || "终审提交失败");
          }
        }).catch(() => {
          this.stopSubmitAnimation();
          this.$toast.fail("终审提交失败");
        });
      }).catch(() => {
        this.$toast.fail("请完善终审信息");
      });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 10px;
}

.information {
  border-radius: 15px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 10px 0;
}

.y-card-box {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f2f5;
  overflow: hidden;
}

.y-card {
  padding: 16px;
}

// 附件部分
.attachments-section {
  .all-attachments {
    .attachment-row {
      margin-bottom: 16px;
      cursor: pointer;

      // 图片行样式
      .image-row {
        img {
          width: 100%;
          max-height: 300px;
          object-fit: contain;
          border-radius: 8px;
          background: #f5f5f5;
          border: 1px solid #e6f4ff;
        }

        .attachment-title {
          font-size: 14px;
          color: #8c8c8c;
          text-align: center;
          margin-top: 8px;
          line-height: 20px;
        }
      }

      // 文件行样式
      .file-row {
        .file-info {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #ffffff;
          border: 1px solid #e6f4ff;
          border-radius: 8px;
          transition: all 0.3s;

          &:hover {
            border-color: #1989fa;
            box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
          }

          .file-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8faff;
            border-radius: 8px;
            margin-right: 12px;
          }

          .file-details {
            flex: 1;

            .file-name {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              margin-bottom: 4px;
              line-height: 20px;
            }

            .file-type {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 16px;
            }
          }

          .file-action {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

.uploadImgsize {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #f0f2f5;
}

.btn-box {
  margin-top: 24px;
  padding: 0 16px;
}

// 提交动画样式
.submit-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
}

.submit-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.submit-loading-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  width: 90%;
}

.submit-progress {
  margin-top: 20px;
}

.progress-text {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.progress-tip {
  font-size: 12px;
  color: #999999;
  line-height: 1.4;
}

.submit-actions {
  margin-top: 20px;
}

.back-button {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666666;
}

// 表单样式优化
/deep/ .van-field__label {
  color: #262626;
  font-weight: 500;
}

/deep/ .van-field__control {
  color: #595959;
}

/deep/ .van-radio__label {
  color: #262626;
  font-size: 14px;
  margin-left: 8px;
}

/deep/ .van-radio--checked .van-radio__label {
  color: #1989fa;
}

/deep/ .van-radio {
  margin-right: 24px;
  margin-bottom: 8px;
}

/deep/ .van-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

/deep/ .van-radio__icon {
  font-size: 16px;
}

/deep/ .van-radio__icon--checked {
  color: #1989fa;
}

.van-radio {
  margin: 5px 3px;
}

.img2 {
  position: relative;
}
.button-box {
  width: 100%;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
}
/deep/.van-button {
  width: 100%;
  margin: 0 10px;
}
.type-imgsize {
  width: 30px;
  height: 30px;
  margin: 0 5px;
}
.type-img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 设置图片之间的间距 */
}
/deep/.fl-sty {
  .van-field__body {
    height: 44px;
  }
}
.mid-center {
  width: 100%;
  align-items: center;
}
.rltx {
  display: inline-block;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-left: 80px;
}


</style>



// 提交动画样式
.submit-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
}

.submit-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.submit-loading-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  width: 90%;
}

.submit-progress {
  margin-top: 20px;
}

.progress-text {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.progress-tip {
  font-size: 12px;
  color: #999999;
  line-height: 1.4;
}

.submit-actions {
  margin-top: 20px;
}

.back-button {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666666;
}

/* 核准证样式 */
.certificate-display {
  .certificate-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .file-info {
      display: flex;
      align-items: center;
      padding: 16px;
      background: #ffffff;
      border: 1px solid #e6f4ff;
      border-radius: 8px;
      transition: all 0.3s;

      &:hover {
        border-color: #1989fa;
        box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
      }

      .file-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8faff;
        border-radius: 8px;
        margin-right: 12px;
      }

      .file-details {
        flex: 1;

        .file-name {
          font-size: 14px;
          color: #262626;
          font-weight: 500;
          margin-bottom: 4px;
          line-height: 20px;
        }

        .file-type {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 16px;
        }
      }

      .preview-icon {
        margin-left: 12px;
        color: #8c8c8c;
      }
    }
  }
}

// 核准证文件显示样式（大图垂直布局）
.cert-files-display {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .cert-file-item {
    display: block;
    text-align: center;
    cursor: pointer;
    margin-bottom: 16px;

    .file-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      min-width: 80px;
    }

    .cert-image {
      width: 100%;
      height: auto;
      max-height: 300px;
      border-radius: 8px;
      border: 1px solid #e6f4ff;
      object-fit: contain;
      cursor: pointer;
      background: #f5f5f5;
      transition: all 0.3s;

      &:hover {
        border-color: #1989fa;
        box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
      }
    }

    .cert-title {
      font-size: 14px;
      color: #8c8c8c;
      text-align: center;
      margin-top: 8px;
      line-height: 20px;
    }
  }
}

// 核准证展示样式（覆盖默认样式）
.approval-cert-section {
  .cert-files-display {
    .cert-file-item {
      margin-bottom: 16px;
      cursor: pointer;
      text-align: center;
      display: block !important; // 覆盖 flex 布局
      align-items: unset !important;
      gap: unset !important;

      .cert-image {
        width: 100% !important; // 强制覆盖 80px 宽度
        height: auto !important; // 强制覆盖 80px 高度
        max-height: 300px;
        object-fit: contain;
        border-radius: 8px;
        background: #f5f5f5;
        border: 1px solid #e6f4ff;
        transition: all 0.3s;

        &:hover {
          border-color: #1989fa;
          box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
        }
      }

      .cert-title {
        font-size: 14px;
        color: #8c8c8c;
        text-align: center;
        margin-top: 8px;
        line-height: 20px;
      }
    }
  }
}
</style>

