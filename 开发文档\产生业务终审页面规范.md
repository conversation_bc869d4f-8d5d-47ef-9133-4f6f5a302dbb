# 产生业务终审页面设计规范

## 1. 页面结构

### 1.1 基本布局
```vue
<template>
  <div class="container">
    <!-- 顶部导航 -->
    <NavHeader ref="navHeader" :title="isViewMode ? '产生地终审详情' : '产生地终审'" :back="true"></NavHeader>
    
    <!-- 审核数据区域 -->
    <LabelHeader left="审核数据"></LabelHeader>
    <div class="information">
      <div class="y-card-box">
        <div class="y-card">
          <van-form ref="form">
            <!-- 数据字段 -->
          </van-form>
        </div>
      </div>
    </div>

    <!-- 申请材料区域 -->
    <div class="attachments-section">
      <LabelHeader left="申请材料"></LabelHeader>
      <!-- 7个固定分类的材料内容 -->
    </div>

    <!-- 初审意见区域 -->
    <LabelHeader left="初审意见"></LabelHeader>
    <div class="information">
      <!-- 初审结果、备注、时间 -->
    </div>

    <!-- 核准证区域 -->
    <div class="approval-cert-section">
      <LabelHeader left="核准证"></LabelHeader>
      <!-- 核准证文件显示 -->
    </div>

    <!-- 证件信息区域 -->
    <LabelHeader left="证件信息"></LabelHeader>
    <div class="information">
      <!-- 证件编号和有效期 -->
    </div>

    <!-- 终审操作/结果区域 -->
    <LabelHeader :left="isViewMode ? '终审结果' : '终审情况'"></LabelHeader>
    <div class="information">
      <!-- 终审表单或结果显示 -->
    </div>
  </div>
</template>
```

## 2. 审核数据区域规范

### 2.1 必须显示的字段（按顺序）
```vue
<!-- 产生地名称 -->
<van-field
  :value="infoData?.engineeringName"
  name="content"
  label="产生地名称："
  :readonly="true"
/>

<!-- 产生地地址（带查看地图按钮） -->
<van-field
  :value="infoData?.coordinateInfo"
  name="content"
  label="产生地地址："
  :readonly="true"
>
  <template #button>
    <van-button
      size="mini"
      type="primary"
      @click="showMap"
      icon="location-o"
      round
      class="map-btn"
    >
      地图
    </van-button>
  </template>
</van-field>

<!-- 建筑垃圾类型 -->
<van-field
  :value="infoData.moreGarbageTypeListStr"
  name="content"
  label="建筑垃圾类型："
  label-width="120"
  :readonly="true"
/>

<!-- 有效期 -->
<van-field
  :value="
    (infoData?.beginValidityTime || '') +
    ' 至 ' +
    (infoData?.endValidityTime || '')
  "
  name="content"
  label="有效期："
  :readonly="true"
/>

<!-- 申报容量 -->
<van-field
  :value="`${infoData?.allCapacity ?? ''} 方`"
  name="content"
  label="申报容量："
  :readonly="true"
/>

<!-- 所属地区 -->
<van-field
  :value="infoData?.addressDistrict?.fullAreaName"
  name="content"
  label="所属地区:"
  :readonly="true"
/>

<!-- 主运单位 -->
<van-field
  :value="infoData?.mainCarrierUnitAgent?.agentName || '-'"
  name="content"
  label="主运单位："
  :readonly="true"
/>

<!-- 联运单位 -->
<van-field
  :value="getJointTransportUnits()"
  name="content"
  label="联运单位："
  :readonly="true"
/>
```

## 3. 申请材料区域规范

### 3.1 固定分类显示
按照《申请材料展示规范.md》实现，包含7个固定分类：
1. 行政许可申请书 (`applyImgs1`)
2. 营业执照或法人证书 (`applyImgs2`)
3. 授权委托书 (`applyImgs3`)
4. 信用承诺书 (`applyImgs4`)
5. 运输合同 (`applyImgs5`)
6. 处置合同 (`applyImgs6`)
7. 建筑垃圾产生信息表 (`applyImgs7`)

## 4. 初审意见区域规范

### 4.1 显示内容
```vue
<van-field
  :value="infoData?.firstAuditResult === 2 ? '通过' : '不通过'"
  name="content"
  label="初审结果："
  :readonly="true"
/>
<van-field
  :value="getAuditRemarks(infoData)"
  name="content"
  label="初审备注："
  :readonly="true"
  type="textarea"
  rows="2"
/>
<van-field
  :value="infoData?.firstAuditTime || ''"
  name="content"
  label="初审时间："
  :readonly="true"
/>
```

## 5. 终审情况区域规范（合并设计）

### 5.1 整体布局
将证件信息、核准证和终审结果合并到一个"终审情况"卡片中：

```vue
<!-- 终审情况 - 合并设计 -->
<LabelHeader :left="isViewMode ? '终审结果' : '终审情况'"></LabelHeader>
<div class="information">
  <div class="y-card-box">
    <div class="y-card">
      <van-form ref="form">
        <!-- 证件信息 -->
        <!-- 有效期（标签式） -->
        <!-- 核准证文件网格 -->
        <!-- 终审结果 -->
      </van-form>
    </div>
  </div>
</div>
```

### 5.2 证件信息显示
```vue
<!-- 查看模式 -->
<van-field
  :value="infoData.certNum || ''"
  name="certNum"
  label="证件编号："
  label-width="120px"
  :readonly="true"
/>

<!-- 编辑模式 -->
<van-field
  v-model="form.certNum"
  name="certNum"
  label="证件编号"
  label-width="120px"
  placeholder="请输入证件编号"
/>
```

### 5.3 有效期标签式显示
```vue
<van-field name="validityPeriod" label="有效期" label-width="120px" :readonly="true">
  <template #input>
    <div class="validity-tags" @click="showStartDatePicker = true">
      <van-tag type="primary" size="medium">{{ infoData.beginValidityTime || '2025-08-01' }}</van-tag>
      <span class="to-text">至</span>
      <van-tag type="success" size="medium">{{ infoData.endValidityTime || '2025-08-28' }}</van-tag>
    </div>
  </template>
</van-field>
```

### 5.4 核准证网格设计
```vue
<!-- 核准证文件展示 - 紧凑网格布局 -->
<div class="cert-files-section" v-if="approvalCertFiles.length > 0">
  <div class="cert-files-grid">
    <div
      v-for="(file, index) in approvalCertFiles"
      :key="index"
      class="cert-file-card"
      @click="previewFile(file)"
    >
      <div class="cert-image-wrapper">
        <img :src="getFileUrl(file.filePath)" :alt="file.displayTitle" class="cert-image" />
        <div class="cert-overlay">
          <div class="cert-type">{{ getCertTypeLabel(file, index) }}</div>
          <!-- 不显示删除文字 -->
        </div>
      </div>
    </div>
  </div>
</div>
```

### 5.5 终审结果显示
```vue
<!-- 查看模式 -->
<van-field name="applyState" label="审核结果：" label-width="120px">
  <template #input>
    <van-radio-group :value="infoData.applyState" direction="horizontal" disabled>
      <van-radio :name="5">通过</van-radio>
      <van-radio :name="6">不通过</van-radio>
    </van-radio-group>
  </template>
</van-field>
<van-field
  :value="getAuditRemarks(infoData)"
  name="finalAuditRemarks"
  label="备注："
  label-width="120px"
  type="textarea"
  rows="1"
  autosize
  :readonly="true"
/>

<!-- 编辑模式 -->
<van-field name="applyState" label="审核结果：" label-width="120px">
  <template #input>
    <van-radio-group v-model="form.applyState" direction="horizontal">
      <van-radio :name="5">通过</van-radio>
      <van-radio :name="6">不通过</van-radio>
    </van-radio-group>
  </template>
</van-field>
<van-field
  v-model="form.ruralDevelopmentRemarks"
  name="ruralDevelopmentRemarks"
  label="备注："
  label-width="120px"
  type="textarea"
  rows="1"
  autosize
  placeholder="请输入内容"
  show-word-limit
  maxlength="100"
/>
```

## 6. 核心方法规范

### 6.1 核准证相关方法
```javascript
// 获取核准证类型标签
getCertTypeLabel(file, index) {
  const title = (file.displayTitle || file.fileName || '').toLowerCase();

  // 根据文件名关键词识别
  if (title.includes('正本') || title.includes('original')) {
    return '正本';
  }
  if (title.includes('副本') || title.includes('copy')) {
    return '副本';
  }

  // 按索引分配：第一个为正本，第二个为副本
  return index === 0 ? '正本' : '副本';
},

// 获取核准证文件列表
approvalCertFiles() {
  if (!this.infoData || !this.infoData.approvalCertFileUrl) {
    return [];
  }

  try {
    const certData = JSON.parse(this.infoData.approvalCertFileUrl);
    return Array.isArray(certData) ? certData : [];
  } catch (error) {
    console.error('解析核准证文件数据失败:', error);
    return [];
  }
},
```

## 7. 样式规范

### 7.1 核准证网格样式
```less
// 核准证文件网格样式
.cert-files-section {
  margin: 16px 0;

  .cert-files-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .cert-file-card {
      position: relative;
      cursor: pointer;

      .cert-image-wrapper {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        background: #f5f5f5;
        border: 1px solid #e6f4ff;
        transition: all 0.3s;

        &:hover {
          border-color: #1989fa;
          box-shadow: 0 2px 8px rgba(25, 137, 250, 0.1);
        }

        .cert-image {
          width: 100%;
          height: 120px;
          object-fit: cover;
          display: block;
        }

        .cert-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
          color: white;
          padding: 8px;

          .cert-type {
            font-size: 12px;
            font-weight: 500;
          }
          // 注意：不显示删除文字
        }
      }
    }
  }
}
```

### 7.2 有效期标签样式
```less
// 有效期标签式样式
.validity-tags {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;

  .to-text {
    color: #8c8c8c;
    font-size: 13px;
    font-weight: 500;
  }

  .van-tag {
    font-size: 13px !important;
    padding: 6px 12px !important;
    border-radius: 16px !important;
    font-weight: 500 !important;
  }
}
```

### 7.3 备注字段优化
```less
// 备注字段优化 - 减少空白
.van-field {
  &.van-field--textarea {
    .van-field__control {
      min-height: auto !important;
    }

    .van-field__body {
      textarea {
        min-height: 40px !important;
        line-height: 1.4 !important;
        padding: 8px 0 !important;
      }
    }
  }
}
```

## 8. 核心方法规范

### 8.1 状态判断逻辑
```javascript
computed: {
  isViewMode() {
    // 从已完成tab进入
    const fromCompletedTab = this.$route.query.from === 'completed';

    // 根据applyState判断：5-通过, 6-不通过
    const isCompletedStatus = this.infoData && (this.infoData.applyState === 5 || this.infoData.applyState === 6);
    return fromCompletedTab || isCompletedStatus;
  },
}
```

### 8.2 必需方法
```javascript
methods: {
  // 获取联运单位列表
  getJointTransportUnits() {
    if (!this.infoData || !this.infoData.uuitApplyAgents || this.infoData.uuitApplyAgents.length === 0) {
      return '-';
    }
    
    const unitNames = this.infoData.uuitApplyAgents
      .filter(agent => agent.agentName)
      .map(agent => agent.agentName);
    
    return unitNames.length > 0 ? unitNames.join('、') : '-';
  },

  // 根据attachType获取对应的附件
  getAttachmentsByType(attachType) {
    if (!this.infoData || !this.infoData.attachments) {
      return [];
    }
    
    return this.infoData.attachments.filter(item => 
      item.attachType === attachType &&
      item.filePath && 
      item.filePath.trim() !== ''
    );
  },

  // 获取核准证类型标签
  getCertTypeLabel(file, index) {
    const title = (file.displayTitle || file.fileName || '').toLowerCase();
    
    if (title.includes('正本') || title.includes('original')) {
      return '正本';
    }
    if (title.includes('副本') || title.includes('copy')) {
      return '副本';
    }
    
    return index === 0 ? '正本' : '副本';
  },

  // 查看地图功能
  showMap() {
    this.$router.push({
      name: "CheckMap",
      params: { data: this.infoData },
    });
  },

  // 文件预览
  previewFile(item) {
    const fileUrl = this.getFileUrl(item.filePath);

    if (this.isImageFile(item.filePath)) {
      ImagePreview([fileUrl]);
    } else {
      // 文档预览逻辑
    }
  },
}
```

## 9. 设计要点

### 9.1 合并布局优势
- **空间节省**：将证件信息、核准证、终审结果合并到一个卡片
- **视觉统一**：所有终审相关信息集中显示
- **操作便捷**：减少页面滚动，提高操作效率

### 9.2 核准证网格设计
- **2列布局**：核准证正本和副本并排显示
- **固定高度**：120px统一高度，保持视觉一致
- **遮罩标签**：底部渐变遮罩显示类型，不显示删除文字
- **悬停效果**：鼠标悬停时边框高亮

### 9.3 有效期标签设计
- **彩色标签**：开始日期用蓝色(primary)，结束日期用绿色(success)
- **圆角设计**：16px圆角，现代化外观
- **可点击**：保持日期选择功能
- **视觉层次**：不同颜色区分开始和结束

### 9.4 备注字段优化
- **紧凑显示**：`rows="1"` + `autosize`，减少空白
- **自适应高度**：根据内容自动调整高度
- **最小高度**：40px最小高度，避免过小

## 10. 查看地图按钮样式
```less
.map-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}
```

## 11. 重要更新说明

### 11.1 客户确认的优化点
1. **核准证图片**：移除"删除"文字，只显示类型标签
2. **有效期显示**：使用彩色标签式，美观且功能完整
3. **备注字段**：使用 `rows="1"` + `autosize`，减少空白
4. **合并布局**：证件信息、核准证、终审结果在同一卡片

### 11.2 与初审页面的主要区别
- **布局更紧凑**：终审相关信息合并显示
- **核准证管理**：2列网格显示，带类型标签
- **有效期样式**：标签式显示，视觉效果更佳
- **状态值不同**：终审使用 5/6，初审使用 8/3

## 12. 数据初始化

### 12.1 页面挂载时的数据处理
```javascript
mounted() {
  try {
    this.infoData = this.$route.params.data;
    
    if (!this.infoData) {
      console.error("页面数据为空，请检查路由传参");
      this.$toast.fail("页面数据获取失败");
      return;
    }
    
    console.log("终审页面数据：", this.infoData);
    
    // 打印申请材料数据
    console.log("=== 申请材料数据 ===");
    console.log("attachments：", JSON.stringify(this.infoData.attachments, null, 2));
    
    // 打印核准证数据
    console.log("=== 核准证数据 ===");
    console.log("approvalCertFileUrl：", this.infoData.approvalCertFileUrl);
    console.log("解析后的核准证文件：", this.approvalCertFiles);
    
  } catch (error) {
    console.error("终审页面初始化失败：", error);
    this.$toast.fail("页面初始化失败");
  }

  // 设置表单初始值
  if (this.infoData && this.infoData.applyState) {
    this.form.applyState = this.infoData.applyState;
  }
  if (this.infoData) {
    const remarks = this.infoData.ruralDevelopmentRemarks || this.infoData.acceptedRemarks;
    if (remarks) {
      this.form.ruralDevelopmentRemarks = remarks;
    }
  }
},
```

## 13. 与初审页面的区别

### 13.1 独有功能
1. **初审意见显示**：显示初审的结果、备注、时间
2. **核准证管理**：2列网格显示核准证文件，带类型标签
3. **证件信息**：证件编号和有效期的输入/显示
4. **终审状态**：使用状态值 5(通过) 和 6(不通过)
5. **合并布局**：证件信息、核准证、终审结果在同一卡片

### 13.2 共同功能
1. **审核数据显示**：与初审页面字段一致
2. **申请材料展示**：使用相同的7个固定分类
3. **图片预览功能**：支持大图查看
4. **查看地图功能**：地图按钮样式一致

## 14. 注意事项

1. **状态值区别**：终审使用 5/6，初审使用 8/3
2. **核准证数据**：从 `approvalCertFileUrl` 字段解析JSON数据
3. **有效期显示**：使用标签式，蓝色(开始) + 绿色(结束)
4. **备注优化**：使用 `rows="1"` + `autosize` 减少空白
5. **API接口**：使用 `auditH5` 和 `saveOrUpdateH5` 接口
6. **图片预览**：确保导入 `ImagePreview` 组件
7. **调试支持**：包含完整的数据日志输出
8. **核准证样式**：不显示删除文字，只显示类型标签
